{"annotations": {"list": [{"builtIn": 1, "datasource": "apisix", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "type": "dashboard"}]}, "description": "MicroService API Gateway Apache APISIX", "editable": true, "gnetId": 11719, "graphTooltip": 0, "id": null, "iteration": 1617695812393, "links": [], "panels": [{"collapsed": false, "datasource": "apisix", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "panels": [], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 1}, "id": 8, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true, "ymax": null, "ymin": null}, "tableColumn": "", "targets": [{"expr": "sum(apisix_nginx_http_current_connections{state=\"total\", instance=~\"$instance\"})", "intervalFactor": 2, "legendFormat": "Total", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Total Connections", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 1}, "id": 16, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true, "ymax": null, "ymin": null}, "tableColumn": "", "targets": [{"expr": "sum(apisix_nginx_http_current_connections{state=\"accepted\", instance=~\"$instance\"})", "intervalFactor": 2, "legendFormat": "Accepted", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Accepted Connections", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 1}, "id": 11, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true, "ymax": null, "ymin": null}, "tableColumn": "", "targets": [{"expr": "sum(apisix_nginx_http_current_connections{state=\"handled\", instance=~\"$instance\"})", "intervalFactor": 2, "legendFormat": "Total", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Handled Connections", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 6}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(apisix_nginx_http_current_connections{state=~\"active|reading|writing|waiting\", instance=~\"$instance\"}) by (state)", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{state}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Nginx connection state", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": null, "format": "Misc", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "apisix", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 13, "panels": [], "title": "Bandwidth", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 13}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(apisix_bandwidth{instance=~\"$instance\"}[30s])) by (type)", "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Bandwidth", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 19}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(irate(apisix_bandwidth{type=\"ingress\", service =~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (service)", "legendFormat": "service:{{service}}", "refId": "A"}, {"expr": "sum(irate(apisix_bandwidth{type=\"ingress\", service =~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (route)", "legendFormat": "route:{{route}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ingress per service/route", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 25}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(apisix_bandwidth{type=\"egress\", service =~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (service)", "interval": "", "legendFormat": "service:{{service}}", "refId": "A"}, {"expr": "sum(rate(apisix_bandwidth{type=\"egress\", service =~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (route)", "legendFormat": "route:{{route}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Egress per service/route", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "apisix", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}, "id": 15, "panels": [], "title": "HTTP", "type": "row"}, {"aliasColors": {"HTTP Status:200": "green", "HTTP Status:500": "red"}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 3, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 32}, "hiddenSeries": false, "id": 2, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "state", "lines": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(apisix_http_status{service=~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (code)", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "HTTP Status:{{code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests per second (RPS) by status code", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 3, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 38}, "hiddenSeries": false, "id": 32, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "state", "lines": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(apisix_http_status{service=~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (service)", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "service:{{service}}", "refId": "A"}, {"expr": "sum(rate(apisix_http_status{service=~\"$service\",route=~\"$route\",instance=~\"$instance\"}[1m])) by (route)", "interval": "", "legendFormat": "route:{{route}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests per second (RPS) per service/route", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 44}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.90, sum(rate(apisix_http_latency_bucket{type=~\"request\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "format": "time_series", "interval": "", "legendFormat": "P90", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(apisix_http_latency_bucket{type=~\"request\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(apisix_http_latency_bucket{type=~\"request\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P99", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "description": "", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 44}, "hiddenSeries": false, "id": 28, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.90, sum(rate(apisix_http_latency_bucket{type=~\"apisix\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "P90", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(apisix_http_latency_bucket{type=~\"apisix\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(apisix_http_latency_bucket{type=~\"apisix\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P99", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "APISIX Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 44}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.90, sum(rate(apisix_http_latency_bucket{type=~\"upstream\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "format": "time_series", "interval": "", "legendFormat": "P90", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(apisix_http_latency_bucket{type=~\"upstream\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum(rate(apisix_http_latency_bucket{type=~\"upstream\",service=~\"$service\",consumer=~\"$consumer\",node=~\"$node\"}[1m])) by (le))", "interval": "", "legendFormat": "P99", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Upstream Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "apisix", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 50}, "id": 23, "panels": [], "title": "Misc", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 18, "x": 0, "y": 51}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(apisix_etcd_modify_indexes{key=~\"consumers|global_rules|max_modify_index|prev_index|protos|routes|services|ssls|stream_routes|upstreams|x_etcd_index\"}) by (key)", "interval": "", "intervalFactor": 1, "legendFormat": "{{key}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Etcd modify indexes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 18, "y": 51}, "id": 25, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.3.7", "targets": [{"expr": "sum(apisix_etcd_reachable{instance=~\"$instance\"})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Etcd reachable", "type": "stat"}, {"datasource": "apisix", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 21, "y": 51}, "id": 29, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.3.7", "targets": [{"expr": "sum(apisix_nginx_metric_errors_total{instance=~\"$instance\"})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Nginx metric errors", "type": "stat"}], "refresh": "5s", "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {}, "datasource": "apisix", "definition": "label_values(apisix_http_status,service)", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "service", "options": [], "query": "label_values(apisix_http_status,service)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "apisix", "definition": "label_values(apisix_http_status,route)", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "route", "options": [], "query": "label_values(apisix_http_status,route)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "apisix", "definition": "label_values(apisix_nginx_http_current_connections,instance)", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "instance", "options": [], "query": "label_values(apisix_http_status,instance)", "refresh": 2, "regex": ".*", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "apisix", "definition": "label_values(apisix_http_status,consumer)", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "consumer", "options": [], "query": "label_values(apisix_http_status,consumer)", "refresh": 2, "regex": ".*", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "apisix", "definition": "label_values(apisix_http_status,node)", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "node", "options": [], "query": "label_values(apisix_http_status,node)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Apache APISIX", "uid": "bLlNuRLWz", "version": 13}