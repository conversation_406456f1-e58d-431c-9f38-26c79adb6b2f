{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "alwaysAllow": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"]}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.3"], "env": {"TAVILY_API_KEY": "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"}, "alwaysAllow": ["tavily-search", "tavily-extract", "tavily-crawl", "tavily-map"]}}}