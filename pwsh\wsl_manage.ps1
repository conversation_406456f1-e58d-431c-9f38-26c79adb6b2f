#Requires -Version 7

<#
.SYNOPSIS
    A PowerShell script to manage WSL distributions, including backup, restore, and reinstallation.
.DESCRIPTION
    This script provides an interactive menu to:
    1. Backup an existing WSL distribution to a .tar file.
    2. Restore a WSL distribution from a .tar file.
    3. Reinstall the latest stable Ubuntu distribution.
    Author: 智囊 (Your AI IT Consultant)
    Version: 1.3 (Cancellable operations)
.NOTES
    Run this script with Administrator privileges in PowerShell 7+.
#>

# --- 配置区 ---
# 设置你的备份文件存放根目录 (支持用户自定义路径)
$BackupConfigFile = Join-Path -Path $env:USERPROFILE -ChildPath ".wsl_backup_config"
if (Test-Path $BackupConfigFile) {
    $BackupBaseFolder = Get-Content $BackupConfigFile -First 1
} else {
    $BackupBaseFolder = Join-Path -Path $env:USERPROFILE -ChildPath "Documents\WSL_Backups"
    $BackupBaseFolder | Out-File $BackupConfigFile -Encoding UTF8
}

# 要安装的Ubuntu发行版名称 (通常 "Ubuntu" 会安装最新的LTS版本)
$UbuntuDistroName = "Ubuntu"
# --- 配置区结束 ---

# 确保备份目录存在
if (-not (Test-Path -Path $BackupBaseFolder -PathType Container)) {
    Write-Host "备份目录 '$BackupBaseFolder' 不存在，正在创建..." -ForegroundColor Yellow
    try {
        New-Item -Path $BackupBaseFolder -ItemType Directory -Force | Out-Null
        Write-Host "目录创建成功。" -ForegroundColor Green
    }
    catch {
        Write-Host "创建备份目录失败！请检查权限或手动创建 '$BackupBaseFolder'。" -ForegroundColor Red
        Read-Host "按 Enter 键退出..."
        exit 1
    }
}

function Show-Menu {
    Clear-Host
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host "           WSL 管理脚本 by 智囊 (v1.3)"
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host
    Write-Host "  1. 备份一个已安装的 WSL 发行版" -ForegroundColor Green
    Write-Host "  2. 从备份文件还原 WSL 发行版" -ForegroundColor Green
    Write-Host "  3. 重装最新的 Ubuntu 发行版" -ForegroundColor Yellow
    Write-Host "  4. 列出所有已安装的 WSL 发行版" -ForegroundColor Cyan
    Write-Host "  5. 设置备份目录" -ForegroundColor Magenta
    Write-Host "  Q. 退出" -ForegroundColor Red
    Write-Host
}

function Backup-WSL {
    Clear-Host
    Write-Host "--- WSL 发行版备份 ---" -ForegroundColor Cyan
    # 获取并过滤WSL发行版列表，确保只包含有效的非空名称
    $distros = (wsl --list --quiet) -split "`n" |
               Where-Object { $_.Trim() -ne "" } |
               ForEach-Object { $_.Trim() } |
               Where-Object { $_ -notmatch '^[\s-]*$' }

    if ($distros.Count -eq 0) {
        Write-Host "未找到任何已安装的 WSL 发行版。" -ForegroundColor Red
        return
    }

    Write-Host "可用的发行版:"
    $distros | ForEach-Object { Write-Host "- $_" }

    # [升级] 增加取消选项
    $distroToBackup = Read-Host "请输入您想要备份的发行版名称 (或输入 'q' 取消)"
    if ($distroToBackup -eq 'q') { Write-Host "操作已取消。"; return }

    if (-not ($distros -contains $distroToBackup)) {
        Write-Host "错误：发行版 '$distroToBackup' 不存在。" -ForegroundColor Red
        return
    }

    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = Join-Path -Path $BackupBaseFolder -ChildPath "$($distroToBackup)_$($timestamp).tar"

    Write-Host "准备将 '$distroToBackup' 备份到 '$backupFile'..." -ForegroundColor Yellow
    Write-Host "为确保数据一致性，将临时关闭所有WSL实例。" -ForegroundColor Yellow

    try {
        wsl --shutdown
        Write-Host "正在执行备份操作，请耐心等待..."
        wsl --export $distroToBackup $backupFile
        Write-Host "备份成功！文件已保存至: $backupFile" -ForegroundColor Green
    }
    catch {
        Write-Host "备份失败！错误信息: $($_.Exception.Message)" -ForegroundColor Red
        if (Test-Path $backupFile) { Remove-Item $backupFile -Force }
    }
}

function Restore-WSL {
    Clear-Host
    Write-Host "--- WSL 发行版还原 ---" -ForegroundColor Cyan

    $backupFiles = Get-ChildItem -Path $BackupBaseFolder -Filter "*.tar" | Sort-Object -Property CreationTime -Descending

    if ($backupFiles.Count -eq 0) {
        Write-Host "在 '$BackupBaseFolder' 目录中未找到任何 .tar 备份文件。" -ForegroundColor Red
        return
    }

    Write-Host "在 '$BackupBaseFolder' 中找到以下备份 (按时间倒序):"
    for ($i = 0; $i -lt $backupFiles.Count; $i++) {
        $file = $backupFiles[$i]
        $fileSize = "{0:N2} MB" -f ($file.Length / 1MB)
        Write-Host (" {0,2}. {1,-40} {2,-12} {3}" -f `
            ($i + 1), `
            $file.Name, `
            $fileSize, `
            ("({0:yyyy-MM-dd HH:mm})" -f $file.CreationTime) `
        ) -ForegroundColor Green
    }
    Write-Host

    $choice = 0
    do {
        try {
            $selection = Read-Host "请输入您想还原的备份编号 (或输入 'q' 取消)"
            if ($selection -eq 'q') { Write-Host "操作已取消。"; return }
            $choice = [int]$selection
            if ($choice -lt 1 -or $choice -gt $backupFiles.Count) {
                Write-Host "无效的编号，请输入 1 到 $($backupFiles.Count) 之间的数字。" -ForegroundColor Yellow
                $choice = 0
            }
        }
        catch {
            Write-Host "无效输入，请输入一个数字。" -ForegroundColor Yellow
        }
    } while ($choice -eq 0)

    $backupFile = $backupFiles[$choice - 1].FullName
    Write-Host "您已选择: $backupFile" -ForegroundColor Cyan

    # [升级] 增加取消选项
    $newDistroName = Read-Host "请输入还原后新发行版的名称 (例如: Ubuntu-Restored, 或输入 'q' 取消)"
    if ($newDistroName -eq 'q') { Write-Host "操作已取消。"; return }
    if ([string]::IsNullOrWhiteSpace($newDistroName)) {
        Write-Host "错误：发行版名称不能为空。" -ForegroundColor Red
        return
    }

    # 检查发行版是否已存在，如果存在则提示用户删除
    $installedDistros = (wsl --list --quiet) -split "`n" |
                       Where-Object { $_.Trim() -ne "" } |
                       ForEach-Object { $_.Trim() }
    if ($installedDistros -contains $newDistroName) {
        Write-Host "警告: 名为 '$newDistroName' 的发行版已存在。" -ForegroundColor Yellow
        $confirmDelete = Read-Host "您想先注销 (删除) 现有的发行版吗? 此操作不可逆！(y/N)"
        if ($confirmDelete -ne 'y') {
            Write-Host "操作已取消。"
            return
        }
        try {
            Write-Host "正在关闭所有WSL实例以安全注销..." -ForegroundColor Yellow
            wsl --shutdown
            Write-Host "正在注销 '$newDistroName'..."
            wsl --unregister $newDistroName
            Write-Host "'$newDistroName' 已成功注销。" -ForegroundColor Green
        }
        catch {
            Write-Host "注销失败！错误信息: $($_.Exception.Message)" -ForegroundColor Red
            return
        }
    }

    $installLocation = Join-Path -Path $env:LOCALAPPDATA -ChildPath "WSL\$newDistroName"
    Write-Host "发行版将安装在: $installLocation"
    # 确保安装目录存在
    if (-not (Test-Path $installLocation)) {
        New-Item -Path $installLocation -ItemType Directory -Force | Out-Null
    }

    try {
        Write-Host "正在从 '$backupFile' 导入到 '$newDistroName'..."
        wsl --import $newDistroName $installLocation $backupFile
        Write-Host "还原成功！新发行版 '$newDistroName' 已创建。" -ForegroundColor Green

        $setDefault = Read-Host "是否将 '$newDistroName' 设置为默认发行版? (y/N)"
        if ($setDefault -eq 'y') {
            wsl --set-default $newDistroName
            Write-Host "'$newDistroName' 已设为默认。" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "还原失败！错误信息: $($_.Exception.Message)" -ForegroundColor Red
        # 如果导入失败，清理创建的空目录
        if (Test-Path $installLocation) {
            Remove-Item -Path $installLocation -Recurse -Force
        }
    }
}

function Reinstall-LatestUbuntu {
    Clear-Host
    Write-Host "--- 重装最新 Ubuntu ---" -ForegroundColor Cyan
    Write-Host "此操作将尝试安装 '$UbuntuDistroName'。"

    $installedDistros = wsl --list --quiet | Where-Object { -not [string]::IsNullOrWhiteSpace($_) }
    if ($installedDistros -contains $UbuntuDistroName) {
        Write-Host "检测到已安装的 '$UbuntuDistroName'。" -ForegroundColor Yellow
        # 这里的 (y/N) 已经隐式支持了取消操作
        $confirm = Read-Host "您确定要注销（删除）现有的 '$UbuntuDistroName' 并重新安装吗？所有数据都将丢失！(y/N)"
        if ($confirm -ne 'y') {
            Write-Host "操作已取消。"
            return
        }

        try {
            Write-Host "正在注销 '$UbuntuDistroName'..."
            wsl --unregister $UbuntuDistroName
            Write-Host "'$UbuntuDistroName' 已成功注销。" -ForegroundColor Green
        }
        catch {
            Write-Host "注销失败！错误信息: $($_.Exception.Message)" -ForegroundColor Red
            return
        }
    }

    try {
        Write-Host "正在从 Microsoft Store 下载并安装最新的 '$UbuntuDistroName'..."
        Write-Host "这可能需要一些时间，请耐心等待。"
        wsl --install -d $UbuntuDistroName
        Write-Host "'$UbuntuDistroName' 安装成功！" -ForegroundColor Green
        Write-Host "首次启动时，您需要设置新的用户名和密码。"
    }
    catch {
        Write-Host "安装失败！请检查您的网络连接和 WSL 设置。" -ForegroundColor Red
        Write-Host "错误信息: $($_.Exception.Message)"
    }
}

# --- 主循环 ---
do {
    Show-Menu
    $choice = Read-Host "请输入您的选择"

    switch ($choice) {
        '1' { Backup-WSL }
        '2' { Restore-WSL }
        '3' { Reinstall-LatestUbuntu }
        '4' {
            Clear-Host
            Write-Host "--- 已安装的 WSL 发行版 ---" -ForegroundColor Cyan
            wsl --list --verbose
        }
        '5' {
            Clear-Host
            Write-Host "--- 设置备份目录 ---" -ForegroundColor Magenta
            Write-Host "当前备份目录: $BackupBaseFolder"
            $newPath = Read-Host "请输入新的备份目录路径 (或按Enter保持当前)"
            if (-not [string]::IsNullOrWhiteSpace($newPath)) {
                if (Test-Path $newPath -PathType Container) {
                    $BackupBaseFolder = $newPath
                    $BackupBaseFolder | Out-File $BackupConfigFile -Encoding UTF8
                    Write-Host "备份目录已更新为: $BackupBaseFolder" -ForegroundColor Green
                } else {
                    Write-Host "错误: 路径 '$newPath' 不存在或不是目录" -ForegroundColor Red
                }
            }
        }
        'q' { Write-Host "正在退出..."; break }
        default { Write-Host "无效输入，请重试。" -ForegroundColor Yellow }
    }

    if ($choice -ne 'q') {
        Read-Host "按 Enter 键返回主菜单..."
    }

} while ($choice -ne 'q')
