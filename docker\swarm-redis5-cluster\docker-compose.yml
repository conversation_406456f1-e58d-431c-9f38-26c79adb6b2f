version: '3.7'
services:
  redis_6379:
    image: redis:5
    hostname: redis_6379
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
      - "16379:16379"
    volumes:
      - "redis_6379:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm130
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6379 --cluster-announce-bus-port 16379
    
  redis_6380:
    image: redis:5
    hostname: redis_6380
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6380:6379"
      - "16380:16379"
    volumes:
      - "redis_6380:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm130
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6380 --cluster-announce-bus-port 16380
    
  redis_6381:
    image: redis:5
    hostname: redis_6381
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6381:6379"
      - "16381:16379"
    volumes:
      - "redis_6381:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm131
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6381 --cluster-announce-bus-port 16381
    
  redis_6382:
    image: redis:5
    hostname: redis_6382
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6382:6379"
      - "16382:16379"
    volumes:
      - "redis_6382:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm131
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6382 --cluster-announce-bus-port 16382

  redis_6383:
    image: redis:5
    hostname: redis_6383
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6383:6379"
      - "16383:16379"
    volumes:
      - "redis_6383:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm132
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6383 --cluster-announce-bus-port 16383
    
  redis_6384:
    image: redis:5
    hostname: redis_6384
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6384:6379"
      - "16384:16379"
    volumes:
      - "redis_6384:/data"
    networks:
      - redis_net
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.hostname == vm132
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    command: redis-server --bind 0.0.0.0 --requirepass 123456 --masterauth 123456 --appendonly yes --cluster-enabled yes --cluster-config-file /data/nodes.conf --cluster-announce-ip ************ --cluster-announce-port 6384 --cluster-announce-bus-port 16384

networks:
  redis_net:
    name: redis_net
    driver: overlay

volumes:
  redis_6379:
  redis_6380:
  redis_6381:
  redis_6382:
  redis_6383:
  redis_6384:
