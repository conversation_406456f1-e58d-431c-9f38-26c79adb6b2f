#!/bin/bash

# 安装acme.sh
curl https://get.acme.sh | sh
alias acme.sh=~/.acme.sh/acme.sh

~/.acme.sh/acme.sh --register-account -m <EMAIL>

# 注册
export ACCOUNT_THUMBPRINT='aipoFYsaC9t-WnX3eDnA1U77Q2tFi-OQUt9PGBPZ1Sc'
# 使用ali dns验证证书
export Ali_Key="LTAI5t8GjFvBr6yRf1r8mkG1"
export Ali_Secret="******************************"
# 使用acme.sh申请证书，通配符证书
~/.acme.sh/acme.sh --issue --dns dns_ali -d ibootz.com -d '*.ibootz.com'

# 将证书安装到指定目录，刷新证书之后自动更新
mkdir -p /etc/nginx/certs
~/.acme.sh/acme.sh --install-cert -d "ibootz.com" \
    --key-file       /etc/nginx/certs/ibootz.com.key  \
    --fullchain-file /etc/nginx/certs/ibootz.com.crt \
    --reloadcmd     "systemctl reload nginx"