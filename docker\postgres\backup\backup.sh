#!/bin/bash
# PostgreSQL 数据库备份脚本

# 设置变量
BACKUP_DIR="/backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/postgres_backup_$TIMESTAMP.sql.gz"
BACKUP_RETENTION_DAYS=7

# 确保备份目录存在
mkdir -p $BACKUP_DIR

# 执行备份
echo "开始备份 PostgreSQL 数据库..."
pg_dumpall -U $POSTGRES_USER | gzip > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "备份成功完成: $BACKUP_FILE"
    
    # 设置正确的权限
    chmod 600 $BACKUP_FILE
    
    # 删除旧备份
    echo "清理超过 $BACKUP_RETENTION_DAYS 天的旧备份..."
    find $BACKUP_DIR -name "postgres_backup_*.sql.gz" -type f -mtime +$BACKUP_RETENTION_DAYS -delete
    
    # 列出当前所有备份
    echo "当前备份文件列表:"
    ls -lh $BACKUP_DIR
else
    echo "备份失败！"
    exit 1
fi
