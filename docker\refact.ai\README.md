# Refact.ai 自托管部署

这是基于[Refact.ai官方自托管文档](https://docs.refact.ai/guides/version-specific/self-hosted/)创建的Docker Compose部署配置。

## 前提条件

- 安装Docker并支持NVIDIA GPU
- 如果在Windows上使用，需要先安装WSL 2

## 使用方法

### 启动服务

```bash
docker compose up -d
```

### 查看日志

```bash
docker compose logs -f
```

### 停止服务

```bash
docker compose down
```

### 更新镜像

```bash
docker compose pull
docker compose up -d
```

## 配置说明

- 服务会在`8008`端口上运行，可通过`http://localhost:8008`访问Web界面
- 所有配置文件、下载的权重和日志都存储在`refact-perm-storage`卷中
- 默认使用所有可用的GPU资源

## 高级配置

### GPU分片（Sharding）

如果需要将模型部署到多个GPU上，可以通过Web界面中的sharding菜单选择1、2或4个GPU。

### 共享GPU

要在一个GPU上运行多个较小的模型，可以在Web界面中选择所需模型旁边的"share GPU"选项。

### 连接OpenAI API

如果您有OpenAI API密钥，可以将其连接到Refact并在Refact中使用GPT系列模型。
在设置（右上角）中设置并保存您的API密钥以供服务器使用。

**注意**：使用此集成将向第三方提供商（OpenAI）发送您的数据。
