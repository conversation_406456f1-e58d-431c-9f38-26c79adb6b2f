version: '3.7'
services: 
  elasticsearch: 
    image: elasticsearch:7.17.9
    container_name: elasticsearch
    hostname: elasticsearch
    deploy:
      resources:
        limits:
          memory: 1500M
    ports:    
      - "9200:9200"
    healthcheck:
      test: ["CMD-SHELL", "curl --silent --fail localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ${PWD}/data/data:/usr/share/elasticsearch/data
      - ${PWD}/data/logs:/usr/share/elasticsearch/logs
    networks:
      - net_v1
    environment:
      - TZ=Asia/Shanghai
      - discovery.type=single-node
      - SW_HEALTH_CHECKER=default
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1024m -Xmx1024m"
    restart: unless-stopped
    ulimits:
      memlock:
        soft: -1
        hard: -1
networks:
  net_v1:
    external: true
    
