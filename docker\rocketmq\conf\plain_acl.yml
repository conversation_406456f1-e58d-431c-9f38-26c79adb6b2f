# 该配置文件用于设置RocketMQ的ACL（访问控制列表）规则。主要用途如下：
# 1. 限制和控制不同IP地址或用户对RocketMQ的访问权限。
# 2. 配置白名单IP地址，使特定网络段的IP地址可以访问。
# 3. 定义用户账户及其权限，包括默认主题权限和消费组权限。
# 4. 允许或拒绝特定用户对特定主题或消费组的访问。
# 通过配置此文件，可以增强RocketMQ集群的安全性，确保只有授权的用户或IP地址能够进行访问和操作。

# 在使用Spring等微服务框架适配RocketMQ的ACL规则时，可以通过以下步骤进行配置：
#
# 1. 确保Spring应用程序使用能够与RocketMQ集成的客户端依赖。
#    通常需要在`pom.xml`或`build.gradle`中添加依赖，例如`rocketmq-spring-boot-starter`。
#
# 2. 配置RocketMQ的客户端连接设置，并在其中包含ACL所需的认证信息。
#
# 3. 在应用程序的配置文件中（如`application.properties`或`application.yml`），填写
#    RocketMQ的基本连接信息以及ACL认证信息，如`accessKey`和`secretKey`。
#
# 以下是一个简单的配置示例：
#
# application.yml
# rocketmq:
#   name-server: 127.0.0.1:9876
#   producer:
#     group: producer_group
#     access-key: yourAccessKey
#     secret-key: yourSecretKey
#   consumer:
#     group: consumer_group
#     access-key: yourAccessKey
#     secret-key: yourSecretKey
#
# 4. 使用Spring的`@Service`或其他相关Bean注解在代码中注入`RocketMQTemplate`或
#    `RocketMQConsumerListener`以便发送或接收消息。
#
# 5. 启动时，确保应用程序能够正确连接到RocketMQ并进行ACL认证。
#
# 6. 定期检查日志以确保没有未授权的访问尝试，并根据需求调整ACL配置文件以增强安全性。


# globalWhiteRemoteAddresses: 定义允许访问的白名单IP地址范围。
# springboot 服务的ip如果在globalWhiteRemoteAddresses白名单中，不会走acl鉴权
globalWhiteRemoteAddresses:
- 10.10.103.*  # 允许10.10.103网段的IP地址访问
- 192.168.0.*   # 允许192.168.0网段的IP地址访问

# accounts: 定义用户账户及其权限。
accounts:
- accessKey: developer  # 用户的访问密钥
  secretKey: YQ_q#GBofF1m4eFI  # 用户的密钥
  whiteRemoteAddress:  # 该用户的白名单IP地址（未设置）
  admin: false  # 该用户不是管理员
  defaultTopicPerm: DENY  # 默认主题权限为拒绝
  defaultGroupPerm: SUB  # 默认消费组权限为订阅
  topicPerms:  # 该用户的主题权限
  - test=DENY  # 对于主题'test'的权限为拒绝
  groupPerms:  # 该用户的消费组权限
  - GroupA=DENY  # 对于消费组'GroupA'的权限为拒绝
  - GroupB=PUB|SUB  # 对于消费组'GroupB'的权限为发布和订阅
- accessKey: admin  # 另一个用户的访问密钥
  secretKey: IsMDs0NiJF3o6GBS  # 另一个用户的密钥
  whiteRemoteAddress:  # 该用户的白名单IP地址（未设置）
  admin: true  # 该用户是管理员
