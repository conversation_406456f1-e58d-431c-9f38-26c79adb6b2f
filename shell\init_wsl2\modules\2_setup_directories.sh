#!/usr/bin/env bash

# ==============================================================================
# 2. 精细化的家目录规划
# ==============================================================================
setup_directories() {
    info "--- 开始创建个人化目录结构 ---"
    # 定义所有需要创建的目录
    declare -a dirs=(
        "$HOME/workspace/personal"
        "$HOME/workspace/clients/yxt"
        "$HOME/workspace/study/course"
        "$HOME/workspace/study/books"
        "$HOME/workspace/playground/go"
        "$HOME/workspace/playground/js"
        "$HOME/workspace/playground/java"
        "$HOME/workspace/playground/py"
        "$HOME/workspace/playground/node"
        "$HOME/workspace/playground/ai"
        "$HOME/tools/bin"
        "$HOME/tools/scripts"
        "$HOME/downloads"
        "$HOME/temp"
    )

    # 循环创建目录，-p 保证了幂等性
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done
    success "个人化目录结构创建完成。"
    info "--- 目录规划完成 ---"
}
