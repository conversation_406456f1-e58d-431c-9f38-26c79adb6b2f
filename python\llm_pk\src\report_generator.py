import os
from datetime import datetime
from typing import List, Dict, Any

def generate_report(question: str, all_ratings: Dict[str, List[Dict[str, Any]]], output_dir: str) -> None:
    """
    根据收集到的所有评分数据生成Markdown格式的报告。

    参数:
        question: 原始问题。
        all_ratings: 一个字典，键是裁判模型名称，值是它们的评分列表。
        output_dir: 保存报告的目标目录。
    """
    report_path = os.path.join(output_dir, "report.md")
    print(f"正在生成报告: {report_path}")

    report_content = []
    report_content.append(f"# LLM 评测报告\n")
    report_content.append(f"**运行日期:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    report_content.append(f"**原始问题:**\n> {question}\n")
    report_content.append("---\n")

    # --- 摘要表格 ---
    report_content.append("## 交叉评测摘要\n")

    # 创建表头
    header = "| 参赛模型 |"
    judge_names = list(all_ratings.keys())
    for judge in judge_names:
        header += f" 由 {judge} 评平均分 |"
    header += " 综合平均分 |"
    report_content.append(header)

    # 创建分隔符
    separator = "|---|"
    for _ in judge_names:
        separator += "---|"
    separator += "---|"
    report_content.append(separator)

    # 处理数据以准备制表
    summary_data = {}
    for judge, rating_list in all_ratings.items():
        if not rating_list: continue
        for rating in rating_list:
            model_name = rating.get("model_name")
            if model_name not in summary_data:
                summary_data[model_name] = {j: "N/A" for j in judge_names}

            scores = rating.get("scores", {})
            if scores:
                average_score = sum(scores.values()) / len(scores) if scores else 0
                summary_data[model_name][judge] = f"{average_score:.2f}"

    # 计算总平均分并创建表格行
    for model_name, judge_scores in summary_data.items():
        row = f"| {model_name} |"
        valid_scores = []
        for judge in judge_names:
            score_text = judge_scores.get(judge, "N/A")
            row += f" {score_text} |"
            if score_text != "N/A":
                valid_scores.append(float(score_text))

        total_average = sum(valid_scores) / len(valid_scores) if valid_scores else 0
        row += f" **{total_average:.2f}** |"
        report_content.append(row)

    report_content.append("\n---\n")

    # --- 详细分数 ---
    report_content.append("## 详细评分\n")
    for judge, rating_list in all_ratings.items():
        if not rating_list: continue
        report_content.append(f"### 由 `{judge}` 评判\n")
        for rating in rating_list:
            model_name = rating.get("model_name", "未知模型")
            scores = rating.get("scores", {})
            comment = rating.get("comment", "未提供评语。")

            score_text = ", ".join([f"{dimension}: **{value}**" for dimension, value in scores.items()])

            report_content.append(f"- **{model_name}**")
            report_content.append(f"  - **分数:** {score_text}")
            report_content.append(f"  - **评语:** {comment}\n")

    # 写入文件
    try:
        with open(report_path, "w", encoding="utf-8") as f:
            f.write("\n".join(report_content))
        print("报告生成成功。")
    except IOError as e:
        print(f"写入报告文件时出错: {e}")

if __name__ == '__main__':
    # 示例用法
    example_question = "神经网络是如何学习的？"
    example_ratings = {
        "gpt-4o": [
            {"model_name": "模型A", "scores": {"清晰度": 8, "深度": 7}, "comment": "很好，但有点浅。"},
            {"model_name": "模型B", "scores": {"清晰度": 9, "深度": 9}, "comment": "优秀且详细的解释。"},
        ],
        "claude-3-sonnet": [
            {"model_name": "模型A", "scores": {"清晰度": 9, "深度": 6}, "comment": "非常清晰，但缺乏技术深度。"},
            {"model_name": "模型B", "scores": {"清晰度": 8, "深度": 10}, "comment": "技术上非常深刻。"},
        ]
    }

    # 为示例创建一个临时目录
    temp_dir = "temp_report_output"
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    generate_report(example_question, example_ratings, temp_dir)
    print(f"示例报告已生成在 '{temp_dir}' 目录中。")
