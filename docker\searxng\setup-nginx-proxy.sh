#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# 检查是否以 root 用户运行
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}请以 root 用户运行此脚本${NC}"
  exit 1
fi

# 检查 Nginx 管理脚本是否存在
NGINX_MANAGER_PATH="/root/workspace/scripts/docker/_shell/rockylinux/nginx-manager.sh"
CERT_MANAGER_PATH="/root/workspace/scripts/docker/_shell/rockylinux/cert-manager.sh"
NGINX_SERVICES_CONF="/root/workspace/scripts/docker/_shell/rockylinux/nginx-services.conf"

if [ ! -f "$NGINX_MANAGER_PATH" ] || [ ! -f "$CERT_MANAGER_PATH" ]; then
  echo -e "${RED}错误: Nginx 管理脚本不存在${NC}"
  echo "请确保以下文件存在:"
  echo "- $NGINX_MANAGER_PATH"
  echo "- $CERT_MANAGER_PATH"
  exit 1
fi

# 确保 SearXNG 服务已添加到 nginx-services.conf
if ! grep -q "search.ibootz.com|searxng-service|9191|true" "$NGINX_SERVICES_CONF"; then
  echo -e "${GREEN}添加 SearXNG 服务到 nginx-services.conf...${NC}"
  # 检查文件末尾是否有 ")" 符号
  if grep -q ")" "$NGINX_SERVICES_CONF"; then
    # 在最后一行的 ")" 前添加 SearXNG 服务配置
    sed -i '/)/i \    "search.ibootz.com|searxng-service|9191|true"' "$NGINX_SERVICES_CONF"
  else
    echo -e "${RED}错误: nginx-services.conf 格式不正确${NC}"
    exit 1
  fi
else
  echo -e "${GREEN}SearXNG 服务已存在于 nginx-services.conf 中${NC}"
fi

# 确保 SearXNG 服务已连接到 nginx-proxy 网络
echo -e "${GREEN}确保 SearXNG 服务已连接到 nginx-proxy 网络...${NC}"
cd /root/workspace/scripts/docker/searxng

# 检查 nginx-proxy 网络是否存在，如果不存在则创建
if ! docker network ls | grep -q "nginx-proxy"; then
  echo -e "${GREEN}创建 nginx-proxy 网络...${NC}"
  docker network create nginx-proxy
fi

# 重启 SearXNG 服务以应用网络配置
echo -e "${GREEN}重启 SearXNG 服务以应用网络配置...${NC}"
docker compose down
docker compose up -d

# 运行 Nginx 管理脚本设置反向代理
echo -e "${GREEN}运行 Nginx 管理脚本设置反向代理...${NC}"
cd /root/workspace/scripts/docker/_shell/rockylinux
./nginx-manager.sh setup

# 更新 Nginx 配置文件，使用本地端口映射
echo -e "${GREEN}更新 Nginx 配置文件...${NC}"
NGINX_CONF="/usr/local/nginx/conf/conf.d/search.ibootz.com.conf"

if [ -f "$NGINX_CONF" ]; then
  # 替换 upstream 配置中的服务器地址为本地端口映射
  sed -i "s|server [^:]*:[0-9]*|server 127.0.0.1:9191|" "$NGINX_CONF"
  # 确保使用正确的端口
  if ! grep -q "127.0.0.1:9191" "$NGINX_CONF"; then
    echo -e "${YELLOW}警告: 无法更新 Nginx 配置文件，手动更新...${NC}"
    # 备份原始配置文件
    cp "$NGINX_CONF" "${NGINX_CONF}.bak"
    # 创建新的配置文件
    cat > "$NGINX_CONF" << 'EOF'
# Upstream configuration for search.ibootz.com
upstream backend_search.ibootz.com {
    server 127.0.0.1:9191 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name search.ibootz.com;

    ssl_certificate /etc/letsencrypt/live/search.ibootz.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/search.ibootz.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/search.ibootz.com/chain.pem;

    # SSL configuration
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # modern configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://backend_search.ibootz.com;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

server {
    listen 80;
    listen [::]:80;
    server_name search.ibootz.com;
    return 301 https://$server_name$request_uri;
}
EOF
  fi
  echo -e "${GREEN}Nginx 配置文件已更新，使用本地端口映射 127.0.0.1:9191${NC}"

  # 重新加载 Nginx 配置
  echo -e "${GREEN}重新加载 Nginx 配置...${NC}"
  /usr/local/nginx/sbin/nginx -s reload
else
  echo -e "${RED}错误: Nginx 配置文件不存在: $NGINX_CONF${NC}"
fi

# 申请 SSL 证书
echo -e "${GREEN}为 search.ibootz.com 申请 SSL 证书...${NC}"
./cert-manager.sh obtain search.ibootz.com

echo -e "${GREEN}SearXNG 服务的 Nginx 反向代理和 SSL 证书设置完成!${NC}"
echo -e "${GREEN}现在可以通过以下地址访问 SearXNG 服务:${NC}"
echo -e "- 本地访问: http://localhost:9191"
echo -e "- 外部访问: https://search.ibootz.com"
