#!/bin/bash

# Git项目URL收集脚本
# 功能：递归扫描指定目录，收集所有Git项目的远程URL和目录信息
# 输出：生成可执行的项目还原脚本
# 作者：自动生成脚本
# 日期：$(date '+%Y-%m-%d %H:%M:%S')

set -euo pipefail

# 配置变量
SCAN_ROOT="${1:-$(pwd)}"
OUTPUT_SCRIPT="restore_git_projects.sh"
TEMP_DATA="/tmp/git_projects_$$.txt"

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查目录是否为Git仓库
is_git_repo() {
    local dir="$1"
    [[ -d "$dir/.git" ]] && return 0
    return 1
}

# 获取Git仓库的远程URL
get_git_remote_urls() {
    local repo_dir="$1"
    local urls=""
    
    cd "$repo_dir" || return 1
    
    # 获取所有远程仓库
    local remotes
    remotes=$(git remote 2>/dev/null || echo "")
    
    if [[ -z "$remotes" ]]; then
        echo "NO_REMOTE"
        return 0
    fi
    
    for remote in $remotes; do
        local url
        url=$(git remote get-url "$remote" 2>/dev/null || echo "")
        if [[ -n "$url" ]]; then
            urls="${urls}${remote}:${url};"
        fi
    done
    
    echo "${urls%%;}"  # 移除最后的分号
}

# 获取Git仓库的分支信息
get_git_branch_info() {
    local repo_dir="$1"
    cd "$repo_dir" || return 1
    
    local current_branch
    current_branch=$(git branch --show-current 2>/dev/null || echo "")
    
    if [[ -z "$current_branch" ]]; then
        current_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    fi
    
    echo "$current_branch"
}

# 扫描目录并收集Git项目信息
scan_git_projects() {
    local scan_dir="$1"
    local project_count=0
    
    log_info "开始扫描目录: $scan_dir"
    
    # 清空临时文件
    > "$TEMP_DATA"
    
    # 使用find命令递归查找所有.git目录
    while IFS= read -r -d '' git_dir; do
        local repo_dir
        repo_dir=$(dirname "$git_dir")
        
        # 跳过嵌套的.git目录（如子模块）
        local parent_git
        parent_git=$(dirname "$repo_dir")
        while [[ "$parent_git" != "/" && "$parent_git" != "." ]]; do
            if [[ -d "$parent_git/.git" ]]; then
                log_warning "跳过嵌套仓库: $repo_dir"
                continue 2
            fi
            parent_git=$(dirname "$parent_git")
        done
        
        log_info "发现Git仓库: $repo_dir"
        
        # 获取远程URL
        local remote_urls
        remote_urls=$(get_git_remote_urls "$repo_dir")
        
        # 获取当前分支
        local current_branch
        current_branch=$(get_git_branch_info "$repo_dir")
        
        # 计算相对路径
        local relative_path
        relative_path=$(realpath --relative-to="$SCAN_ROOT" "$repo_dir")
        
        # 保存到临时文件
        echo "$repo_dir|$relative_path|$remote_urls|$current_branch" >> "$TEMP_DATA"
        
        ((project_count++))
        
    done < <(find "$scan_dir" -name ".git" -type d -print0 2>/dev/null)
    
    log_success "扫描完成，共发现 $project_count 个Git项目"
    
    if [[ $project_count -gt 0 ]]; then
        return 0  # 成功，发现了项目
    else
        return 1  # 失败，未发现项目
    fi
}

# 生成还原脚本
generate_restore_script() {
    local output_file="$1"
    
    log_info "生成还原脚本: $output_file"
    
    cat > "$output_file" << 'EOF'
#!/bin/bash

# Git项目还原脚本
# 此脚本由collect_git_projects.sh自动生成
# 功能：根据收集的信息克隆或更新Git项目
# 特性：支持幂等操作，智能冲突处理，安全的仓库更新

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << 'HELP_EOF'
Git项目还原脚本

用法: $0 [基础目录] [预演模式] [冲突策略]

参数:
  基础目录     项目还原的根目录 (默认: 当前目录)
  预演模式     true=仅预览不执行, false=实际执行 (默认: false)
  冲突策略     处理目录冲突的策略 (默认: ask)

冲突处理策略:
  ask      交互式询问如何处理每个冲突 (推荐)
  skip     跳过所有冲突项目
  backup   自动备份冲突目录后继续
  force    强制覆盖冲突目录 (危险)

功能特性:
  ✓ 幂等操作: 可重复执行而不会破坏现有项目
  ✓ URL验证: 检查现有仓库URL是否匹配
  ✓ 安全更新: 检测未提交更改，避免数据丢失
  ✓ 智能合并: 自动执行快进合并
  ✓ 分支管理: 自动切换到目标分支

使用示例:
  $0                                    # 交互模式还原到当前目录
  $0 /home/<USER>/projects                # 交互模式还原到指定目录
  $0 /target true                       # 预览模式，查看将要执行的操作
  $0 /target false skip                 # 自动跳过冲突项目
  $0 /target false backup               # 自动备份冲突后继续

注意事项:
  - 首次使用建议先执行预览模式
  - backup策略会创建带时间戳的备份目录
  - force策略会删除冲突目录，请谨慎使用
  - 脚本会保护有未提交更改的仓库

HELP_EOF
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
esac

# 配置变量
BASE_DIR="${1:-$(pwd)}"
DRY_RUN="${2:-false}"

log_info "Git项目还原脚本启动"
log_info "基础目录: $BASE_DIR"
log_info "预演模式: $DRY_RUN"

# 创建基础目录
if [[ "$DRY_RUN" != "true" ]]; then
    mkdir -p "$BASE_DIR"
fi

EOF

    # 添加项目信息和克隆逻辑
    echo "" >> "$output_file"
    echo "# 项目信息列表" >> "$output_file"
    echo "declare -a PROJECTS=(" >> "$output_file"
    
    # 按目录分组显示项目信息
    local current_dir=""
    while IFS='|' read -r abs_path rel_path remote_urls current_branch; do
        local dir_name
        dir_name=$(dirname "$rel_path")
        
        if [[ "$dir_name" != "$current_dir" ]]; then
            if [[ -n "$current_dir" ]]; then
                echo "" >> "$output_file"
            fi
            echo "    # 目录: $dir_name" >> "$output_file"
            current_dir="$dir_name"
        fi
        
        # 处理远程URL
        if [[ "$remote_urls" == "NO_REMOTE" ]]; then
            echo "    # WARNING: $rel_path (无远程仓库)" >> "$output_file"
        else
            # 解析多个远程仓库
            IFS=';' read -ra REMOTES <<< "$remote_urls"
            local primary_url=""
            for remote_info in "${REMOTES[@]}"; do
                if [[ "$remote_info" =~ ^origin: ]]; then
                    primary_url="${remote_info#origin:}"
                    break
                elif [[ -z "$primary_url" ]]; then
                    primary_url="${remote_info#*:}"
                fi
            done
            
            echo "    \"$rel_path|$primary_url|$current_branch\"" >> "$output_file"
        fi
        
    done < <(sort -t'|' -k2 "$TEMP_DATA")
    
    echo ")" >> "$output_file"
    
    # 添加克隆逻辑
    cat >> "$output_file" << 'EOF'

# 冲突处理策略配置
CONFLICT_STRATEGY="${3:-ask}"  # ask, skip, backup, force
BACKUP_SUFFIX=".backup.$(date +%Y%m%d_%H%M%S)"

# 检查Git仓库URL是否匹配
check_git_url_match() {
    local repo_dir="$1"
    local expected_url="$2"
    
    if [[ ! -d "$repo_dir/.git" ]]; then
        return 1  # 不是Git仓库
    fi
    
    cd "$repo_dir" || return 1
    
    # 获取所有远程URL
    local current_urls
    current_urls=$(git remote -v | grep fetch | awk '{print $2}' | sort | uniq)
    
    # 标准化URL进行比较（移除.git后缀，统一协议格式）
    local normalized_expected
    normalized_expected=$(echo "$expected_url" | sed 's/\.git$//' | sed 's|^git@\([^:]*\):|https://\1/|')
    
    for url in $current_urls; do
        local normalized_current
        normalized_current=$(echo "$url" | sed 's/\.git$//' | sed 's|^git@\([^:]*\):|https://\1/|')
        if [[ "$normalized_current" == "$normalized_expected" ]]; then
            return 0  # URL匹配
        fi
    done
    
    return 1  # URL不匹配
}

# 处理目录冲突
handle_directory_conflict() {
    local target_dir="$1"
    local clone_url="$2"
    local rel_path="$3"
    
    if [[ ! -e "$target_dir" ]]; then
        return 0  # 无冲突
    fi
    
    log_warning "发现目录冲突: $target_dir"
    
    # 检查是否为Git仓库
    if [[ -d "$target_dir/.git" ]]; then
        if check_git_url_match "$target_dir" "$clone_url"; then
            log_info "现有仓库URL匹配，将进行更新"
            return 0  # 可以安全更新
        else
            log_warning "现有仓库URL不匹配！"
            log_info "  期望URL: $clone_url"
            log_info "  当前URL: $(cd "$target_dir" && git remote get-url origin 2>/dev/null || echo '未知')"
        fi
    else
        log_warning "目录存在但不是Git仓库"
    fi
    
    # 根据策略处理冲突
    case "$CONFLICT_STRATEGY" in
        "skip")
            log_info "跳过冲突项目: $rel_path"
            return 1
            ;;
        "backup")
            local backup_dir="${target_dir}${BACKUP_SUFFIX}"
            log_info "创建备份: $backup_dir"
            mv "$target_dir" "$backup_dir"
            return 0
            ;;
        "force")
            log_warning "强制覆盖现有目录"
            rm -rf "$target_dir"
            return 0
            ;;
        "ask")
            echo ""
            log_warning "如何处理冲突目录: $target_dir"
            echo "  1) 跳过 (s/skip)"
            echo "  2) 备份后克隆 (b/backup)"
            echo "  3) 强制覆盖 (f/force)"
            echo "  4) 退出脚本 (q/quit)"
            read -p "请选择 [s/b/f/q]: " choice
            
            case "$choice" in
                s|skip)
                    log_info "跳过项目: $rel_path"
                    return 1
                    ;;
                b|backup)
                    local backup_dir="${target_dir}${BACKUP_SUFFIX}"
                    log_info "创建备份: $backup_dir"
                    mv "$target_dir" "$backup_dir"
                    return 0
                    ;;
                f|force)
                    log_warning "强制覆盖现有目录"
                    rm -rf "$target_dir"
                    return 0
                    ;;
                q|quit)
                    log_info "用户选择退出"
                    exit 0
                    ;;
                *)
                    log_warning "无效选择，跳过此项目"
                    return 1
                    ;;
            esac
            ;;
    esac
    
    return 1
}

# 安全更新Git仓库
safe_update_repo() {
    local repo_dir="$1"
    local branch="$2"
    
    cd "$repo_dir" || return 1
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        log_warning "仓库有未提交的更改，跳过更新: $(basename "$repo_dir")"
        return 1
    fi
    
    # 获取当前分支
    local current_branch
    current_branch=$(git branch --show-current 2>/dev/null || echo "")
    
    log_info "更新仓库: $(basename "$repo_dir")"
    
    # 获取远程更新
    if ! git fetch --all --prune; then
        log_warning "获取远程更新失败"
        return 1
    fi
    
    # 处理分支切换
    if [[ "$branch" != "unknown" && "$branch" != "$current_branch" ]]; then
        if git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
            log_info "切换到分支: $branch"
            git checkout "$branch" || log_warning "分支切换失败: $branch"
        else
            log_warning "远程分支不存在: $branch"
        fi
    fi
    
    # 如果当前分支有对应的远程分支，尝试合并
    local current_branch_after
    current_branch_after=$(git branch --show-current 2>/dev/null || echo "")
    if [[ -n "$current_branch_after" ]] && git show-ref --verify --quiet "refs/remotes/origin/$current_branch_after"; then
        local local_commit remote_commit base_commit
        local_commit=$(git rev-parse HEAD)
        remote_commit=$(git rev-parse "origin/$current_branch_after")
        base_commit=$(git merge-base HEAD "origin/$current_branch_after" 2>/dev/null || echo "")
        
        if [[ "$local_commit" == "$remote_commit" ]]; then
            log_info "仓库已是最新状态"
        elif [[ "$local_commit" == "$base_commit" ]]; then
            log_info "执行快进合并"
            git merge --ff-only "origin/$current_branch_after"
        else
            log_warning "本地有独特提交，跳过自动合并"
        fi
    fi
    
    return 0
}

# 执行项目克隆/更新
for project_info in "${PROJECTS[@]}"; do
    IFS='|' read -r rel_path clone_url branch <<< "$project_info"
    
    target_dir="$BASE_DIR/$rel_path"
    
    log_info "处理项目: $rel_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "  [预演] 目标目录: $target_dir"
        log_info "  [预演] 仓库URL: $clone_url"
        log_info "  [预演] 目标分支: $branch"
        
        if [[ -e "$target_dir" ]]; then
            if [[ -d "$target_dir/.git" ]]; then
                if check_git_url_match "$target_dir" "$clone_url"; then
                    log_info "  [预演] 操作: 更新现有仓库"
                else
                    log_warning "  [预演] 冲突: URL不匹配，需要处理"
                fi
            else
                log_warning "  [预演] 冲突: 目录存在但不是Git仓库"
            fi
        else
            log_info "  [预演] 操作: 克隆新仓库"
        fi
        continue
    fi
    
    # 处理目录冲突
    if ! handle_directory_conflict "$target_dir" "$clone_url" "$rel_path"; then
        continue  # 跳过此项目
    fi
    
    # 执行克隆或更新
    if [[ -d "$target_dir/.git" ]]; then
        # 更新现有仓库
        if safe_update_repo "$target_dir" "$branch"; then
            log_success "仓库更新完成: $rel_path"
        else
            log_warning "仓库更新失败: $rel_path"
        fi
    else
        # 克隆新仓库
        log_info "克隆新仓库: $clone_url"
        mkdir -p "$(dirname "$target_dir")"
        
        if git clone --depth=1 "$clone_url" "$target_dir"; then
            cd "$target_dir"
            
            # 切换到指定分支
            if [[ "$branch" != "unknown" ]] && git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
                git checkout "$branch" || log_warning "切换分支失败: $branch"
            fi
            
            log_success "克隆完成: $rel_path"
        else
            log_error "克隆失败: $clone_url"
        fi
    fi
done

log_success "项目还原完成！"
EOF

    chmod +x "$output_file"
    log_success "还原脚本已生成: $output_file"
}

# 显示统计信息
show_statistics() {
    if [[ ! -f "$TEMP_DATA" ]]; then
        log_error "未找到项目数据文件"
        return 1
    fi
    
    local total_projects
    total_projects=$(wc -l < "$TEMP_DATA")
    
    local projects_with_remote
    projects_with_remote=$(grep -v "NO_REMOTE" "$TEMP_DATA" | wc -l)
    
    local projects_without_remote
    projects_without_remote=$(grep -c "NO_REMOTE" "$TEMP_DATA" || true) # grep -c returns 1 if no match, `|| true` prevents script exit
    if ! [[ "$projects_without_remote" =~ ^[0-9]+$ ]]; then # Sanitize output
        projects_without_remote=0
    fi
    
    echo ""
    log_info "=== 统计信息 ==="
    log_info "总项目数: $total_projects"
    log_info "有远程仓库: $projects_with_remote"
    log_info "无远程仓库: $projects_without_remote"
    
    if [[ $projects_without_remote -gt 0 ]]; then
        echo ""
        log_warning "以下项目没有远程仓库："
        grep "NO_REMOTE" "$TEMP_DATA" | cut -d'|' -f2 | while read -r path; do
            echo "  - $path"
        done
    fi
}

# 清理临时文件
cleanup() {
    [[ -f "$TEMP_DATA" ]] && rm -f "$TEMP_DATA"
}

# 主函数
main() {
    log_info "Git项目URL收集脚本启动"
    log_info "扫描根目录: $SCAN_ROOT"
    log_info "输出脚本: $OUTPUT_SCRIPT"
    
    # 检查扫描目录是否存在
    if [[ ! -d "$SCAN_ROOT" ]]; then
        log_error "扫描目录不存在: $SCAN_ROOT"
        exit 1
    fi
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 扫描Git项目
    if ! scan_git_projects "$SCAN_ROOT"; then
        log_warning "未发现任何Git项目"
        exit 0
    fi
    
    # 生成还原脚本
    generate_restore_script "$OUTPUT_SCRIPT"
    
    # 显示统计信息
    show_statistics
    
    log_success "脚本执行完成！"
    log_info "使用方法："
    log_info "  1. 查看预演效果: ./$OUTPUT_SCRIPT /target/path true"
    log_info "  2. 执行还原（交互模式）: ./$OUTPUT_SCRIPT /target/path"
    log_info "  3. 自动跳过冲突: ./$OUTPUT_SCRIPT /target/path false skip"
    log_info "  4. 自动备份冲突: ./$OUTPUT_SCRIPT /target/path false backup"
    log_info "  5. 强制覆盖（危险）: ./$OUTPUT_SCRIPT /target/path false force"
}

# 帮助信息
show_help() {
    cat << EOF
Git项目URL收集脚本

用法: $0 [扫描目录]

参数:
  扫描目录    要扫描的根目录路径 (默认: 当前目录)

功能:
  - 递归扫描指定目录下的所有Git项目
  - 收集每个项目的远程仓库URL和分支信息
  - 生成可执行的项目还原脚本

输出:
  - restore_git_projects.sh: 项目还原脚本

示例:
  $0                          # 扫描当前目录
  $0 /home/<USER>/projects      # 扫描指定目录

EOF
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
