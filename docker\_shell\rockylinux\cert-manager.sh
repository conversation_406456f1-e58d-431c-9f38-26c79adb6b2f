#!/bin/bash

# Certificate Manager Script for Docker Services (RockyLinux Version)
# This script manages SSL certificates for Docker services using Certbot

# Add debug mode
DEBUG=true

# Function to log debug messages
debug_log() {
    if [ "$DEBUG" = true ]; then
        echo -e "${GREEN}DEBUG:${NC} $1"
    fi
}

# Configuration
CERTBOT_DIR="/etc/letsencrypt"
NGINX_INSTALL_DIR="/usr/local/nginx"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Function to ensure Certbot is installed
ensure_certbot() {
    if ! command -v certbot >/dev/null 2>&1; then
        echo "Installing Certbot..."
        sudo dnf update
        sudo dnf install -y certbot python3-certbot-nginx
    fi
}

# Function to manage SSL certificate for a specific domain
manage_cert() {
    local domain=$1
    local action=${2:-obtain}  # obtain, renew, status, or revoke

    case "$action" in
        obtain)
            echo -e "${GREEN}Checking certificate for $domain...${NC}"

            # Check if certificate already exists
            if [ -f "/etc/letsencrypt/live/${domain}/fullchain.pem" ]; then
                echo -e "${GREEN}✓${NC} Certificate exists for ${domain}"
                return 0
            fi

            echo -e "${GREEN}Obtaining new certificate for $domain...${NC}"

            # 停止Nginx服务以释放80端口
            systemctl stop nginx
            sleep 2  # 等待端口完全释放

            # 尝试获取证书
            if certbot certonly --standalone \
                   -d "$domain" \
                   --non-interactive \
                   --agree-tos \
                   --email <EMAIL> > /var/log/certbot-${domain}.log 2>&1; then
                echo -e "${GREEN}✓${NC} Successfully obtained certificate for ${domain}"

                # 证书获取成功后，先尝试启动nginx（不测试配置，因为可能还没有生成对应的配置文件）
                # nginx配置的测试应该在配置文件生成后进行
                if systemctl start nginx; then
                    echo -e "${GREEN}✓${NC} Nginx started successfully"
                    return 0
                else
                    echo -e "${RED}✗${NC} Failed to start Nginx after certificate acquisition"
                    return 1
                fi
            else
                echo -e "${RED}✗${NC} Failed to obtain certificate for ${domain}"
                echo "Check /var/log/certbot-${domain}.log for details"
                systemctl start nginx  # 尝试重新启动Nginx
                return 1
            fi
            ;;
        renew)
            echo -e "${GREEN}Renewing certificate for $domain...${NC}"

            # 停止Nginx服务以释放80端口
            systemctl stop nginx
            sleep 2  # 等待端口完全释放

            # 尝试更新证书
            local renewal_success=false
            if [ -n "$domain" ]; then
                if certbot renew --cert-name "$domain" --force-renewal --standalone > /var/log/certbot-renew-${domain}.log 2>&1; then
                    renewal_success=true
                fi
            else
                if certbot renew --force-renewal --standalone > /var/log/certbot-renew.log 2>&1; then
                    renewal_success=true
                fi
            fi

            # 检查证书更新结果
            if [ "$renewal_success" = true ]; then
                echo -e "${GREEN}✓${NC} Certificate renewed successfully"

                # 在启动Nginx之前检查配置
                if ${NGINX_INSTALL_DIR}/sbin/nginx -t &>/dev/null; then
                    systemctl start nginx
                    echo -e "${GREEN}✓${NC} Nginx restarted successfully"
                else
                    echo -e "${RED}✗${NC} Nginx configuration test failed. Please check your configuration."
                    ${NGINX_INSTALL_DIR}/sbin/nginx -t  # 显示具体错误
                    return 1
                fi
            else
                echo -e "${RED}✗${NC} Certificate renewal failed"
                echo "Check /var/log/certbot-renew.log for details"
                # 尝试重新启动Nginx，即使证书更新失败
                systemctl start nginx
                return 1
            fi
            ;;
        status)
            if [ -n "$domain" ]; then
                certbot certificates -d "$domain" | grep -E "Domains:|Expiry Date:|VALID:"
            else
                certbot certificates
            fi
            ;;
        revoke)
            echo -e "${RED}Revoking certificate for $domain...${NC}"
            certbot revoke --cert-name "$domain" --non-interactive
            certbot delete --cert-name "$domain" --non-interactive
            echo -e "${GREEN}✓${NC} Certificate revoked and deleted for ${domain}"
            ;;
    esac
}

# Function to setup certificate auto-renewal
setup_cert_renewal() {
    # Create renewal configuration directory
    local renewal_conf_dir="/etc/letsencrypt/renewal-hooks"
    local pre_hook="${renewal_conf_dir}/pre"
    local post_hook="${renewal_conf_dir}/post"
    local deploy_hook="${renewal_conf_dir}/deploy"

    # Create hook directories if they don't exist
    mkdir -p "$pre_hook" "$post_hook" "$deploy_hook"

    # Create pre-hook script to stop nginx
    cat > "${pre_hook}/stop-nginx.sh" << 'EOF'
#!/bin/bash
# Log the action
echo "$(date): Stopping nginx for certificate renewal" >> /var/log/certbot/renewal.log
systemctl stop nginx
# Wait for the port to be released
sleep 5
EOF
    chmod +x "${pre_hook}/stop-nginx.sh"

    # Create post-hook script to start nginx
    cat > "${post_hook}/start-nginx.sh" << 'EOF'
#!/bin/bash
# Log the action
echo "$(date): Starting nginx after certificate renewal" >> /var/log/certbot/renewal.log
# Test nginx configuration before starting
if /usr/local/nginx/sbin/nginx -t; then
    systemctl start nginx
    echo "$(date): Nginx started successfully" >> /var/log/certbot/renewal.log
else
    echo "$(date): ERROR - Nginx configuration test failed" >> /var/log/certbot/renewal.log
    # Try to start nginx anyway to maintain service
    systemctl start nginx
fi
EOF
    chmod +x "${post_hook}/start-nginx.sh"

    # Create deploy-hook script for additional tasks
    cat > "${deploy_hook}/update-permissions.sh" << 'EOF'
#!/bin/bash
# Log the action
echo "$(date): Updating certificate permissions" >> /var/log/certbot/renewal.log
# Ensure proper permissions for nginx
chown -R nginx:nginx /etc/letsencrypt/live
chown -R nginx:nginx /etc/letsencrypt/archive
EOF
    chmod +x "${deploy_hook}/update-permissions.sh"

    # Create log directory
    mkdir -p /var/log/certbot

    # Modify certbot renewal configuration
    cat > /etc/letsencrypt/cli.ini << EOF
# Certbot configuration
authenticator = standalone
agree-tos = True
no-eff-email = True
EOF

    # Enable and start the timer
    if ! systemctl is-enabled certbot-renew.timer &>/dev/null; then
        systemctl enable certbot-renew.timer
        systemctl start certbot-renew.timer
    fi

    echo "Certificate auto-renewal is configured with the following features:"
    echo "- Pre-renewal: Stops nginx"
    echo "- Post-renewal: Starts nginx with configuration test"
    echo "- Deploy-hook: Updates certificate permissions"
    echo "- Logs are stored in /var/log/certbot/renewal.log"
    echo
    echo "Current timer status:"
    systemctl list-timers certbot-renew.timer
}

# Function to check SSL certificates status
check_certs() {
    local domain=$1

    if [ -z "$domain" ]; then
        echo -e "${GREEN}Checking all SSL certificates status:${NC}"
        manage_cert "" status
    else
        echo -e "${GREEN}Checking certificate status for $domain:${NC}"
        manage_cert "$domain" status
    fi

    # Check renewal timer status
    local timer_status=$(systemctl status certbot-renew.timer 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}Next automatic renewal:${NC}"
        echo "$timer_status" | grep "Trigger:" || echo "Timer not active"
    else
        echo -e "\n${RED}Certificate renewal timer is not installed${NC}"
    fi
}

# Parse command line arguments
case "$1" in
    install)
        ensure_certbot
        ;;
    obtain)
        if [ -z "$2" ]; then
            echo "Usage: $0 obtain <domain>"
            exit 1
        fi
        manage_cert "$2" obtain
        ;;
    renew)
        if [ -n "$2" ]; then
            manage_cert "$2" renew
        else
            manage_cert "" renew
        fi
        ;;
    revoke)
        if [ -z "$2" ]; then
            echo "Usage: $0 revoke <domain>"
            exit 1
        fi
        manage_cert "$2" revoke
        ;;
    status)
        if [ -n "$2" ]; then
            check_certs "$2"
        else
            check_certs
        fi
        ;;
    setup-renewal)
        setup_cert_renewal
        ;;
    *)
        echo "Usage: $0 {install|obtain|renew|revoke|status|setup-renewal} [domain]"
        echo "Examples:"
        echo "  $0 install                # Install Certbot"
        echo "  $0 obtain example.com     # Obtain certificate for domain"
        echo "  $0 renew                  # Force renew all certificates"
        echo "  $0 renew example.com      # Force renew specific domain certificate"
        echo "  $0 revoke example.com     # Revoke and delete domain certificate"
        echo "  $0 status                 # Check all certificates status"
        echo "  $0 status example.com     # Check specific domain certificate"
        echo "  $0 setup-renewal          # Setup automatic certificate renewal"
        exit 1
        ;;
esac
