#!/bin/bash

# 检查root权限
[[ $(whoami) != root ]] && {
    echo "警告: 需要root用户运行!"
    [[ $0 == ./uninstall.sh ]] && exit 1 || return 1
}

# 检查是否已卸载
[ ! -d /etc/clash ] && {
    echo "clash: 已卸载过!"
    read -p "按 Enter 键退出，按其他键继续：" answer
    [[ $answer == "" ]] && {
        echo "退出卸载"
        [[ $0 == ./uninstall.sh ]] && exit 1 || return 1
    } || echo "继续卸载..."
}

# 清理环境变量和代理配置
cleanup_env() {
    cat <<EOF >./unenv.sh
# 清除代理环境变量
unset http_proxy https_proxy all_proxy DOCKER_OPTS

# 清除各种工具的代理配置
for cmd in npm git yarn; do
    command -v \$cmd >/dev/null 2>&1 && {
        case \$cmd in
            npm|yarn)
                \$cmd config delete proxy
                \$cmd config delete https-proxy
                ;;
            git)
                \$cmd config --global --unset http.proxy
                \$cmd config --global --unset https.proxy
                ;;
        esac
    }
done

# 清除clash相关函数
unset clashon clashoff clashui clashreset
EOF
    source ./unenv.sh && rm -f ./unenv.sh
}

# 清理shell配置
cleanup_shell_rc() {
    local shell_rc
    if [ -n "$BASH_VERSION" ]; then
        shell_rc="$HOME/.bashrc"
    elif [ -n "$ZSH_VERSION" ]; then
        shell_rc="$HOME/.zshrc"
    else
        shell_rc="$HOME/.bashrc"
    fi

    sed -i '/# 加载clash快捷指令/d;/. \/etc\/clash\/clashctl.sh/d' "$shell_rc"
    source "$shell_rc"
}

# 清理系统服务
cleanup_service() {
    systemctl stop clash >/dev/null 2>&1
    systemctl disable clash >/dev/null 2>&1
    rm -f /etc/systemd/system/clash.service
    systemctl daemon-reload
}

# 执行清理
cleanup_env
cleanup_shell_rc
cleanup_service

# 删除程序文件
rm -rf /etc/clash
rm -f /usr/local/bin/clash

echo "clash: 已卸载，相关配置已清除！"
