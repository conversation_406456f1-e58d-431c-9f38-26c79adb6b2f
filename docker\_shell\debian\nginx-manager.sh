#!/bin/bash

# Nginx Manager Script for Docker Services (Debian Version)
# This script manages Nginx configurations for Docker services with SSL support

# Add debug mode
DEBUG=true

# Function to log debug messages
debug_log() {
    if [ "$DEBUG" = true ]; then
        echo -e "${GREEN}DEBUG:${NC} $1"
    fi
}

# Configuration
NGINX_CONFIG_DIR="/etc/nginx"
NGINX_SITES_DIR="${NGINX_CONFIG_DIR}/conf.d"
CERTBOT_DIR="/etc/letsencrypt"
DOCKER_NETWORK="nginx-proxy"
CONFIG_FILE="nginx-services.conf"
NGINX_VERSION="1.24.0"
NGINX_USER="www-data"
NGINX_GROUP="www-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# Load service configurations
if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
else
    echo -e "${RED}Error: Configuration file $CONFIG_FILE not found${NC}"
    echo "Please create the configuration file and run the script again."
    exit 1
fi

# Validate that SERVICES array is defined and not empty
if [ -z "${SERVICES[*]}" ]; then
    echo -e "${RED}Error: No services defined in $CONFIG_FILE${NC}"
    echo "Please add service configurations to the file and run the script again."
    exit 1
fi

# Function to create Docker network if it doesn't exist
create_network() {
    if ! docker network inspect "$DOCKER_NETWORK" >/dev/null 2>&1; then
        echo "Creating Docker network: $DOCKER_NETWORK"
        docker network create "$DOCKER_NETWORK"
    fi
}

# Function to ensure Nginx is installed
ensure_nginx() {
    if ! command -v nginx >/dev/null 2>&1; then
        echo "Installing Nginx..."
        apt-get update
        apt-get install -y nginx
    fi

    # Create necessary directories
    mkdir -p ${NGINX_SITES_DIR}
    mkdir -p ${NGINX_CONFIG_DIR}/ssl

    # Ensure proper permissions
    chown -R ${NGINX_USER}:${NGINX_GROUP} ${NGINX_CONFIG_DIR}

    # Basic nginx configuration
    cat > ${NGINX_CONFIG_DIR}/nginx.conf << EOF
user www-data;
worker_processes auto;
worker_rlimit_nofile 65535;
pid /run/nginx.pid;

events {
    use epoll;
    worker_connections 65535;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    include       /etc/nginx/conf.d/*.conf;

    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                      '\$status \$body_bytes_sent "\$http_referer" '
                      '"\$http_user_agent" "\$http_x_forwarded_for" '
                      '\$request_time \$upstream_response_time';

    access_log  /var/log/nginx/access.log  main buffer=16k;
    error_log   /var/log/nginx/error.log  warn;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    keepalive_requests 100;
    client_max_body_size 20m;

    proxy_http_version 1.1;
    proxy_set_header Connection "";
    upstream_keepalive_timeout 60s;
    upstream_keepalive_requests 100;

    gzip  on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    server_tokens off;
}
EOF

    systemctl restart nginx
}

# Function to ensure Certbot is installed
ensure_certbot() {
    if ! command -v certbot >/dev/null 2>&1; then
        echo "Installing Certbot..."
        apt-get update
        apt-get install -y certbot python3-certbot-nginx
    fi
}

# Function to manage SSL certificate for a specific domain
manage_ssl() {
    local domain=$1
    if [ "$2" = "true" ]; then
        echo "Managing SSL certificate for $domain"
        certbot --nginx -d "$domain" --non-interactive --agree-tos --email admin@"$domain" \
            --redirect --keep-until-expiring
    fi
}

# Function to configure a single service
configure_service() {
    local domain container_name container_port ssl
    IFS='|' read -r domain container_name container_port ssl <<< "$1"

    echo "Configuring service: $domain"

    # Create Nginx configuration
    local config_file="${NGINX_SITES_DIR}/${domain}.conf"

    cat > "$config_file" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $domain;

    location / {
        proxy_pass http://${container_name}:${container_port};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # Manage SSL if required
    if [ "$ssl" = "true" ]; then
        manage_ssl "$domain" true
    fi
}

# Function to setup all services
setup_services() {
    create_network
    ensure_nginx
    ensure_certbot

    for service in "${SERVICES[@]}"; do
        configure_service "$service"
    done

    setup_cert_renewal
    reload_nginx
}

# Function to reload Nginx configuration
reload_nginx() {
    systemctl reload nginx
}

# Function to add a new service
add_service() {
    local service_config=$1
    SERVICES+=("$service_config")
    configure_service "$service_config"
    reload_nginx
}

# Function to remove a service
remove_service() {
    local domain=$1
    rm -f "${NGINX_SITES_DIR}/${domain}.conf"
    reload_nginx
}

# Function to list all configured services
list_services() {
    echo "Configured services:"
    for service in "${SERVICES[@]}"; do
        echo "$service"
    done
}

# Function to setup certificate auto-renewal
setup_cert_renewal() {
    # Ensure cron is installed
    if ! command -v cron >/dev/null 2>&1; then
        apt-get update
        apt-get install -y cron
    fi

    # Add certbot renewal to cron
    echo "0 0 * * * root certbot renew --quiet --deploy-hook 'systemctl reload nginx'" > /etc/cron.d/certbot-renewal
    chmod 644 /etc/cron.d/certbot-renewal
}

# Function to check SSL certificates status
check_certs() {
    echo "Checking SSL certificates status..."
    certbot certificates
}

# Parse command line arguments
case "$1" in
    setup)
        setup_services
        ;;
    add)
        if [ -z "$2" ]; then
            echo "Usage: $0 add domain|container|port|ssl"
            exit 1
        fi
        add_service "$2"
        ;;
    remove)
        if [ -z "$2" ]; then
            echo "Usage: $0 remove domain"
            exit 1
        fi
        remove_service "$2"
        ;;
    list)
        list_services
        ;;
    check-certs)
        check_certs
        ;;
    reload)
        reload_nginx
        ;;
    *)
        echo "Usage: $0 {setup|add|remove|list|check-certs|reload}"
        exit 1
        ;;
esac
