# PowerShell 7 别名配置文件
# 作者: 张强
# 版本: 2.0
# 更新: 2025-07-17
# 描述: 为开发者提供便捷的命令别名，提高终端操作效率
# =============================================================================

#Requires -Version 7.0

# 模块初始化（静默，使用 -Verbose 查看详细信息）
Write-Verbose "正在加载 PowerShell 别名配置..."

# =============================================================================
# 核心工具函数
# =============================================================================

# 安全别名注册器
function Register-SafeAlias {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)][string]$Name,
        [Parameter(Mandatory)][string]$Command,
        [string]$Description = "",
        [switch]$Force
    )

    try {
        if (Get-Alias -Name $Name -ErrorAction SilentlyContinue) {
            if ($Force) {
                Remove-Alias -Name $Name -Force -Scope Global
            } else {
                Write-Warning "别名 '$Name' 已存在，跳过注册"
                return
            }
        }

        Set-Alias -Name $Name -Value $Command -Scope Global -Description $Description
        Write-Verbose "已注册别名: $Name -> $Command"
    }
    catch {
        Write-Error "注册别名 '$Name' 失败: $($_.Exception.Message)"
    }
}

# 命令存在性检查
function Test-CommandExists {
    [CmdletBinding()]
    param([Parameter(Mandatory)][string]$Command)

    return [bool](Get-Command $Command -ErrorAction SilentlyContinue)
}

# 批量注册别名
function Register-AliasGroup {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)][hashtable]$Aliases,
        [string]$GroupName = "未知组",
        [string]$RequiredCommand = $null
    )

    if ($RequiredCommand -and -not (Test-CommandExists $RequiredCommand)) {
        Write-Verbose "跳过 $GroupName 别名组: 未找到命令 '$RequiredCommand'"
        return
    }

    Write-Verbose "加载 $GroupName 别名组..."

    foreach ($alias in $Aliases.GetEnumerator()) {
        $name = [string]$alias.Key
        $value = [string]$alias.Value

        # 单词（无空格/固定参数）的场景，直接用 Alias（只要不含空白字符即可）
        if ($value -notmatch '\s') {
            Register-SafeAlias -Name $name -Command $value -Force
            continue
        }

        # 多词（包含空格/固定参数）的场景，注册为函数并透传用户参数
        try {
            # 清理同名 alias / function 防止冲突
            if (Get-Alias -Name $name -ErrorAction SilentlyContinue) {
                Remove-Alias -Name $name -Force -Scope Global -ErrorAction SilentlyContinue
            }
            if (Test-Path -Path "function:\$name") {
                Remove-Item -Path "function:\$name" -Force -ErrorAction SilentlyContinue
            }

            # 解析固定命令及其参数（支持简单引号/双引号）
            $parts = [System.Text.RegularExpressions.Regex]::Matches($value, '("[^"]+"|''[^'']+''|\S+)') | ForEach-Object { $_.Value.Trim('"').Trim("'") }
            if ($parts.Count -eq 0) { continue }
            $exe = $parts[0]
            $fixed = @()
            if ($parts.Count -gt 1) { $fixed = $parts | Select-Object -Skip 1 }

            # 将固定参数安全拼接为字面量数组表达式
            $fixedLiteral = ($fixed | ForEach-Object { "'" + ($_ -replace "'", "''") + "'" }) -join ", "
            $invokeLine = if ($fixed.Count -gt 0) { "& '" + ($exe -replace "'", "''") + "' @(" + $fixedLiteral + ") @args" } else { "& '" + ($exe -replace "'", "''") + "' @args" }
            $script = $invokeLine

            # 注册为全局函数，支持后续追加用户参数
            Set-Item -Path ("function:\global:{0}" -f $name) -Value ([scriptblock]::Create($script)) -ErrorAction Stop
            Write-Verbose "已注册函数别名: $name -> $value"
        }
        catch {
            Write-Error "注册函数别名 '$name' 失败: $($_.Exception.Message)"
        }
    }
}

# =============================================================================
# 系统与文件操作别名
# =============================================================================

# 增强的文件操作函数
function global:ll { Get-ChildItem @args | Format-Table -AutoSize }
function global:la { Get-ChildItem @args -Force | Format-Table -AutoSize }
function global:lt { Get-ChildItem @args | Sort-Object LastWriteTime -Descending | Select-Object -First 10 }
function global:lsize { Get-ChildItem @args | Sort-Object Length -Descending | Select-Object Name, @{Name="Size(MB)";Expression={[math]::Round($_.Length/1MB,2)}} }

# 安全的文件操作（带确认）
function global:rms { Remove-Item @args -Recurse -Force -Confirm }
function global:cps { Copy-Item @args -Recurse -Confirm }
function global:mvs { Move-Item @args -Confirm }

# 快速目录导航
function global:.. { Set-Location .. }
function global:... { Set-Location ../.. }
function global:.... { Set-Location ../../.. }
function global:~ { Set-Location $HOME }
function global:desktop { Set-Location "$HOME\Desktop" }
function global:docs { Set-Location "$HOME\Documents" }
function global:downloads { Set-Location "$HOME\Downloads" }

$systemAliases = @{
    'h' = 'Get-History'
    'cls' = 'Clear-Host'
    'grep' = 'Select-String'
    'which' = 'Get-Command'
    'touch' = 'New-Item'
    'cat' = 'Get-Content'
    'head' = 'Get-Content -TotalCount 10'
    'tail' = 'Get-Content -Tail 10'
    'ps' = 'Get-Process'
    'kill' = 'Stop-Process'
    'env' = 'Get-ChildItem Env:'
    'path' = '$env:PATH -split ";"'
}

Register-AliasGroup -Aliases $systemAliases -GroupName "系统工具"

# =============================================================================
# Git 版本控制别名
# =============================================================================

$gitAliases = @{
    # 基础操作
    'g' = 'git'
    'gs' = 'git status'
    'ga' = 'git add .'
    'gaa' = 'git add --all'
    'gc' = 'git commit'
    'gcm' = 'git commit -m'
    'gca' = 'git commit --amend'
    'gp' = 'git push'
    'gpl' = 'git pull'
    'gf' = 'git fetch'

    # 分支操作
    'gb' = 'git branch'
    'gba' = 'git branch -a'
    'gbd' = 'git branch -d'
    'gbdf' = 'git branch -D'
    'gco' = 'git checkout'
    'gcb' = 'git checkout -b'
    'gm' = 'git merge'

    # 日志与差异
    'gl' = 'git log --oneline --decorate --graph'
    'gla' = 'git log --oneline --decorate --graph --all'
    'gd' = 'git diff'
    'gds' = 'git diff --staged'
    'gshow' = 'git show'

    # 暂存操作
    'gst' = 'git stash'
    'gsta' = 'git stash apply'
    'gstp' = 'git stash pop'
    'gstl' = 'git stash list'
    'gstd' = 'git stash drop'

    # 远程操作
    'gr' = 'git remote'
    'grv' = 'git remote -v'
    'gcl' = 'git clone'
    'grao' = 'git remote add origin'

    # 重置与清理
    'grh' = 'git reset --hard'
    'grs' = 'git reset --soft'
    'gclean' = 'git clean -fd'
}

Register-AliasGroup -Aliases $gitAliases -GroupName "Git 版本控制" -RequiredCommand "git"

# =============================================================================
# Docker 容器化别名
# =============================================================================

$dockerAliases = @{
    # 基础操作
    'd' = 'docker'
    'dps' = 'docker ps'
    'dpsa' = 'docker ps -a'
    'dimg' = 'docker images'
    'drun' = 'docker run'
    'dexec' = 'docker exec -it'
    'dlogs' = 'docker logs -f'
    'dinspect' = 'docker inspect'

    # 容器管理
    'dstart' = 'docker start'
    'dstop' = 'docker stop'
    'drestart' = 'docker restart'
    'drm' = 'docker rm'
    'drmi' = 'docker rmi'

    # 清理操作
    'dprune' = 'docker system prune'
    'dcprune' = 'docker container prune'
    'diprune' = 'docker image prune'
    'dvprune' = 'docker volume prune'
    'dnprune' = 'docker network prune'

    # Docker Compose
    'dc' = 'docker compose'
    'dcu' = 'docker compose up'
    'dcud' = 'docker compose up -d'
    'dcd' = 'docker compose down'
    'dcr' = 'docker compose restart'
    'dcb' = 'docker compose build'
    'dcps' = 'docker compose ps'
    'dclogs' = 'docker compose logs -f'
    'dcexec' = 'docker compose exec'
}

Register-AliasGroup -Aliases $dockerAliases -GroupName "Docker 容器" -RequiredCommand "docker"

# =============================================================================
# Node.js 开发别名
# =============================================================================

# NPM 别名
$npmAliases = @{
    'n' = 'npm'
    'ni' = 'npm install'
    'nid' = 'npm install --save-dev'
    'nig' = 'npm install -g'
    'nu' = 'npm uninstall'
    'nup' = 'npm update'
    'nr' = 'npm run'
    'ns' = 'npm start'
    'nt' = 'npm test'
    'nb' = 'npm run build'
    'ndev' = 'npm run dev'
    'nlint' = 'npm run lint'
    'nls' = 'npm list'
    'nout' = 'npm outdated'
    'naudit' = 'npm audit'
    'nfix' = 'npm audit fix'
}

Register-AliasGroup -Aliases $npmAliases -GroupName "NPM 包管理" -RequiredCommand "npm"

# PNPM 别名
$pnpmAliases = @{
    'pn' = 'pnpm'
    'pni' = 'pnpm install'
    'pna' = 'pnpm add'
    'pnad' = 'pnpm add -D'
    'pnag' = 'pnpm add -g'
    'pnrm' = 'pnpm remove'
    'pnup' = 'pnpm update'
    'pnr' = 'pnpm run'
    'pns' = 'pnpm start'
    'pnt' = 'pnpm test'
    'pnb' = 'pnpm run build'
    'pndev' = 'pnpm run dev'
    'pnls' = 'pnpm list'
    'pnout' = 'pnpm outdated'
    'pnaudit' = 'pnpm audit'
}

Register-AliasGroup -Aliases $pnpmAliases -GroupName "PNPM 包管理" -RequiredCommand "pnpm"

# Yarn 别名
$yarnAliases = @{
    'y' = 'yarn'
    'ya' = 'yarn add'
    'yad' = 'yarn add --dev'
    'yag' = 'yarn global add'
    'yrm' = 'yarn remove'
    'yup' = 'yarn upgrade'
    'yr' = 'yarn run'
    'ys' = 'yarn start'
    'yt' = 'yarn test'
    'yb' = 'yarn build'
    'ydev' = 'yarn dev'
    'yls' = 'yarn list'
    'yout' = 'yarn outdated'
    'yaudit' = 'yarn audit'
}

Register-AliasGroup -Aliases $yarnAliases -GroupName "Yarn 包管理" -RequiredCommand "yarn"

# =============================================================================
# Python 开发别名
# =============================================================================

$pythonAliases = @{
    'py' = 'python'
    'py3' = 'python3'
    'pip' = 'python -m pip'
    'pipi' = 'python -m pip install'
    'pipu' = 'python -m pip uninstall'
    'pipup' = 'python -m pip install --upgrade'
    'pipls' = 'python -m pip list'
    'pipout' = 'python -m pip list --outdated'
    'pipf' = 'python -m pip freeze'
    'venv' = 'python -m venv'
    'activate' = '.\\venv\\Scripts\\Activate.ps1'
}

Register-AliasGroup -Aliases $pythonAliases -GroupName "Python 开发" -RequiredCommand "python"

# UV 包管理器别名
$uvAliases = @{
    'uv' = 'uv'
    'uvi' = 'uv install'
    'uva' = 'uv add'
    'uvrm' = 'uv remove'
    'uvs' = 'uv sync'
    'uvr' = 'uv run'
    'uvt' = 'uv tool'
    'uvti' = 'uv tool install'
    'uvtu' = 'uv tool uninstall'
    'uvtl' = 'uv tool list'
    'uvp' = 'uv python'
    'uvpi' = 'uv python install'
    'uvpl' = 'uv python list'
}

Register-AliasGroup -Aliases $uvAliases -GroupName "UV 包管理" -RequiredCommand "uv"

# =============================================================================
# Kubernetes 容器编排别名
# =============================================================================

$k8sAliases = @{
    # 基础操作
    'k' = 'kubectl'
    'kget' = 'kubectl get'
    'kdesc' = 'kubectl describe'
    'kdel' = 'kubectl delete'
    'kapp' = 'kubectl apply'
    'kcreate' = 'kubectl create'
    'kedit' = 'kubectl edit'

    # 资源查看
    'kgp' = 'kubectl get pods'
    'kgs' = 'kubectl get services'
    'kgd' = 'kubectl get deployments'
    'kgn' = 'kubectl get nodes'
    'kgns' = 'kubectl get namespaces'
    'kgcm' = 'kubectl get configmaps'
    'kgsec' = 'kubectl get secrets'
    'kgpv' = 'kubectl get pv'
    'kgpvc' = 'kubectl get pvc'

    # 详细信息
    'kdp' = 'kubectl describe pod'
    'kds' = 'kubectl describe service'
    'kdd' = 'kubectl describe deployment'
    'kdn' = 'kubectl describe node'

    # 日志与调试
    'klogs' = 'kubectl logs'
    'klogsf' = 'kubectl logs -f'
    'kexec' = 'kubectl exec -it'
    'kport' = 'kubectl port-forward'
    'ktop' = 'kubectl top'
    'ktopn' = 'kubectl top nodes'
    'ktopp' = 'kubectl top pods'

    # 上下文与配置
    'kctx' = 'kubectl config current-context'
    'kctxs' = 'kubectl config get-contexts'
    'kctxu' = 'kubectl config use-context'
    'kns' = 'kubectl config set-context --current --namespace'
}

Register-AliasGroup -Aliases $k8sAliases -GroupName "Kubernetes" -RequiredCommand "kubectl"

# =============================================================================
# 云服务与工具别名
# =============================================================================

# Terraform 别名
$terraformAliases = @{
    'tf' = 'terraform'
    'tfi' = 'terraform init'
    'tfp' = 'terraform plan'
    'tfa' = 'terraform apply'
    'tfd' = 'terraform destroy'
    'tfv' = 'terraform validate'
    'tff' = 'terraform fmt'
    'tfs' = 'terraform show'
    'tfstate' = 'terraform state'
    'tfws' = 'terraform workspace'
    'tfout' = 'terraform output'
}

Register-AliasGroup -Aliases $terraformAliases -GroupName "Terraform" -RequiredCommand "terraform"

# 网络工具别名
$networkAliases = @{
    'ping' = 'Test-NetConnection'
    'wget' = 'Invoke-WebRequest'
    'curl' = 'Invoke-RestMethod'
    'netstat' = 'Get-NetTCPConnection'
    'ports' = 'Get-NetTCPConnection | Where-Object State -eq Listen'
}

Register-AliasGroup -Aliases $networkAliases -GroupName "网络工具"

# =============================================================================
# 初始化完成
# =============================================================================

# 静默结束（使用 -Verbose 可查看统计信息）
Write-Verbose "别名配置加载完成"

# 导出模块函数（仅在作为模块导入时）
if ($ExecutionContext.SessionState.Module) {
    Export-ModuleMember -Function Register-SafeAlias, Test-CommandExists, Register-AliasGroup
}
