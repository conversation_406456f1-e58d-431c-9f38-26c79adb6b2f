# Flutter Gradle 代理问题修复说明

## 问题描述

在使用代理的环境下运行 `flutter run` 时，可能会遇到以下错误：

```
'127.0.0.1' 不是内部或外部命令，也不是可运行的程序或批处理文件
```

这个错误通常是由于 Gradle 代理配置不当导致的，特别是环境变量 `GRADLE_OPTS` 与配置文件设置冲突。

## 修复方案

### 1. 主要修复内容

- **移除 GRADLE_OPTS 环境变量设置**：避免与 gradlew 脚本冲突
- **完善 nonProxyHosts 配置**：更好地排除本地地址
- **分别配置全局和项目级别的代理**：确保 Flutter Android 项目正确使用代理
- **添加 Flutter 项目检测**：自动识别并给出相应提示

### 2. 使用方法

#### 方法一：使用修复后的脚本

```powershell
# 加载脚本
. .\pwsh\switch_proxy.ps1

# 启用代理（会自动配置Gradle）
proxyon

# 或者指定代理地址
proxyon "http://127.0.0.1:7890"

# 专门修复Flutter项目的Gradle代理
Fix-FlutterGradleProxy -proxyUrl "http://127.0.0.1:7890" -enable $true
```

#### 方法二：使用测试脚本

```powershell
# 运行测试脚本（仅检查，不修复）
.\pwsh\test_gradle_proxy_fix.ps1 -TestOnly

# 运行测试脚本并应用修复
.\pwsh\test_gradle_proxy_fix.ps1 -ProxyUrl "http://127.0.0.1:7890"
```

### 3. 修复后的配置

修复后，Gradle 代理配置将通过以下方式生效：

1. **全局配置文件** (`~/.gradle/gradle.properties`)：
   ```properties
   systemProp.http.proxyHost=127.0.0.1
   systemProp.http.proxyPort=7890
   systemProp.https.proxyHost=127.0.0.1
   systemProp.https.proxyPort=7890
   systemProp.http.nonProxyHosts=localhost|127.*|[::1]|*.local|*.localdomain|10.*|192.168.*|172.16.*|...
   systemProp.https.nonProxyHosts=localhost|127.*|[::1]|*.local|*.localdomain|10.*|192.168.*|172.16.*|...
   ```

2. **Flutter Android 项目配置文件** (`android/gradle.properties`)：
   ```properties
   # 同样的代理配置
   ```

3. **环境变量**：
   - `GRADLE_OPTS`: 不设置（避免冲突）

### 4. 验证修复

修复后，执行以下命令验证：

```powershell
# 检查代理状态
proxystate

# 清理Flutter缓存
flutter clean

# 清理Android Gradle缓存
cd android
./gradlew clean
cd ..

# 重新运行Flutter项目
flutter run
```

### 5. 故障排除

如果仍然遇到问题：

1. **检查代理服务是否正常运行**
2. **确认代理地址和端口正确**
3. **清除所有相关环境变量**：
   ```powershell
   Remove-Item Env:GRADLE_OPTS -ErrorAction SilentlyContinue
   Remove-Item Env:http_proxy -ErrorAction SilentlyContinue
   Remove-Item Env:https_proxy -ErrorAction SilentlyContinue
   ```
4. **手动检查配置文件**：
   - `~/.gradle/gradle.properties`
   - `android/gradle.properties`

### 6. 关闭代理

```powershell
# 关闭所有代理
proxyoff

# 或者只关闭Gradle代理
Fix-FlutterGradleProxy -enable $false
```

## 技术细节

### 问题根因

1. **环境变量冲突**：`GRADLE_OPTS` 环境变量可能被 gradlew 脚本错误解析
2. **nonProxyHosts 不完整**：本地地址没有被正确排除
3. **配置层级混乱**：全局配置和项目配置不一致

### 修复原理

1. **统一使用配置文件**：避免环境变量的解析问题
2. **完善排除列表**：确保本地地址不走代理
3. **分层配置**：全局和项目级别都正确配置

这样修复后，Flutter 项目应该能够正常使用代理进行构建和运行。
