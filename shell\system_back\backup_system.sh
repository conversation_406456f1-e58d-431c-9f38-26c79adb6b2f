#! /bin/bash

# 本脚本使用rsnapshot备份系统

# 1. 检查是否以root用户运行
if [ $UID -ne 0 ]; then
    echo "请以root用户运行本脚本"
    exit 1
fi

# 2. 检查是否存在/backup目录,如果不存在创建一个
if [ ! -d /backup ]; then
    mkdir -p /backup
fi

# 3. 选择备份类型
echo "请选择备份类型:"
echo "1) 每日备份 (默认)"
echo "2) 每周备份"
echo "3) 每月备份"
read -p "请输入选项 (1-3): " backup_type

case $backup_type in
    2)
        backup_option="weekly"
        ;;
    3)
        backup_option="monthly"
        ;;
    *)
        backup_option="daily"
        ;;
esac

echo "开始执行 $backup_option 备份..."
rsnapshot $backup_option

echo "备份完成"