#!/bin/bash

# 检查是否以 root 身份运行
if [ "$(id -u)" = "0" ]; then
    echo "错误: 请不要以 root 身份运行此脚本"
    exit 1
fi

# 检查代理配置是否生效
if [ -z "$http_proxy" ] || [ -z "$https_proxy" ]; then
    echo "警告: 代理环境变量未设置,请检查 ~/.bashrc 和 ~/.zshrc 中是否正确配置了代理"
    exit 1
fi

# 更新系统
update_system() {
    echo "更新系统..."
    sudo apt update && sudo apt upgrade -y || { echo "更新系统失败"; exit 1; }
}

# 安装基础开发工具
install_basic_dev_tools() {
    echo "安装基础开发工具..."
    sudo apt install -y \
        git \
        vim \
        curl \
        wget \
        htop \
        net-tools \
        build-essential \
        cmake \
        python3-pip || { echo "安装基础开发工具失败"; exit 1; }
}

# 配置开发环境
setup_dev_env() {
    echo "配置开发环境..."
    # Git配置
    git config --global core.editor vim || { echo "配置 Git 编辑器失败"; exit 1; }
    
    # 配置pip源
    mkdir -p ~/.pip
    echo "[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple" > ~/.pip/pip.conf || { echo "配置 pip 源失败"; exit 1; }
}

# 系统优化
optimize_system() {
    echo "优化系统设置..."
    # 增加文件描述符限制
    echo "fs.file-max = 2097152" | sudo tee -a /etc/sysctl.conf || { echo "增加文件描述符限制失败"; exit 1; }
    echo "* soft nofile 655350" | sudo tee -a /etc/security/limits.conf || { echo "设置软限制失败"; exit 1; }
    echo "* hard nofile 655350" | sudo tee -a /etc/security/limits.conf || { echo "设置硬限制失败"; exit 1; }
    
    # 配置swap使用
    echo "vm.swappiness = 10" | sudo tee -a /etc/sysctl.conf || { echo "配置 swap 使用失败"; exit 1; }
}

# 安装常用工具
install_common_tools() {
    echo "安装常用工具..."
    
    sudo apt install -y \
        tree \
        neofetch \
        flameshot || { echo "安装常用工具失败"; exit 1; }
}

# 安装docker和docker compose
install_docker() {
    echo "安装 Docker..."
    
    # 移除可能存在的旧版本
    sudo apt remove -y docker docker-engine docker.io containerd runc || true
    
    # 安装依赖
    sudo apt update
    sudo apt install -y \
        ca-certificates \
        curl \
        gnupg \
        lsb-release \
        apt-transport-https \
        software-properties-common || { echo "安装 Docker 依赖失败"; exit 1; }
        
    # 添加 Docker 官方 GPG 密钥
    sudo install -m 0755 -d /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg || { 
        echo "添加 Docker GPG 密钥失败"; 
        exit 1; 
    }
    sudo chmod a+r /etc/apt/keyrings/docker.gpg
    
    # 使用 Ubuntu 的 Docker 仓库（因为 Deepin 基于 Ubuntu）
    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
        jammy stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null || {
            echo "设置 Docker 仓库失败";
            exit 1;
        }
    
    # 更新软件包索引
    sudo apt update
    
    # 安装 Docker Engine 和 Docker Compose
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin || {
        echo "安装 Docker 失败";
        exit 1;
    }
    
    # 将当前用户添加到 docker 组
    sudo usermod -aG docker $USER || { echo "将用户添加到 docker 组失败"; exit 1; }

    newgrp docker
    
    # 配置 Docker 镜像加速
    sudo mkdir -p /etc/docker
    echo '{
        "registry-mirrors": [
            "https://mirror.ccs.tencentyun.com",
            "https://hub-mirror.c.163.com",
            "https://mirror.baidubce.com"
        ]
    }' | sudo tee /etc/docker/daemon.json > /dev/null || { echo "配置 Docker 镜像加速失败"; exit 1; }
    
    # 重启 Docker 服务
    sudo systemctl restart docker || { echo "重启 Docker 服务失败"; exit 1; }
    
    echo "Docker 安装完成"
}

# 安装 NVM 和 Node.js
install_node() {
    echo "安装 NVM 和 Node.js..."
    # 下载并安装 NVM
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash -s -- --skip-repo || {
        echo "安装 NVM 失败"; exit 1;
    }

    # 确保 NVM 环境变量和配置被加载
    export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" # This loads nvm

    echo "安装最新的 LTS 版本"
    nvm install --lts || { echo "安装 Node.js LTS 版本失败"; exit 1; }
    nvm use --lts || { echo "使用 Node.js LTS 版本失败"; exit 1; }

    echo "设置国内镜像"
    npm config set registry https://registry.npmmirror.com

    echo "安装 yarn & pnpm"
    npm install -g yarn pnpm || { echo "安装 yarn & pnpm 失败"; exit 1; }

    echo "安装 yarn & pnpm 完成"
}

# 安装 Miniconda 和 Python 环境
install_miniconda() {
    echo "安装 Miniconda..."
    
    # 首先安装必要的系统依赖
    sudo apt update
    sudo apt install -y libarchive13 || { echo "安装 libarchive13 失败"; exit 1; }
    
    # 检查 Miniconda 是否已安装
    if [ -d "$HOME/DevTools/miniconda" ]; then
        echo "Miniconda 已安装，跳过安装步骤。"
        return
    fi  # 这里修复了语法错误，将 } 改为 fi

    wget https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh || { echo "下载 Miniconda 失败"; exit 1; }
    bash miniconda.sh -b -p $HOME/DevTools/miniconda || { echo "安装 Miniconda 失败"; exit 1; }
    rm miniconda.sh
    
    # 配置环境变量
    if ! grep -q 'export PATH="$HOME/DevTools/miniconda/bin:$PATH"' ~/.bashrc; then
        echo 'export PATH="$HOME/DevTools/miniconda/bin:$PATH"' >> ~/.bashrc || { echo "配置 Miniconda 环境变量失败"; exit 1; }
    fi
    if ! grep -q 'export PATH="$HOME/DevTools/miniconda/bin:$PATH"' ~/.zshrc; then
        echo 'export PATH="$HOME/DevTools/miniconda/bin:$PATH"' >> ~/.zshrc || { echo "配置 Miniconda 环境变量失败"; exit 1; }
    fi
    source ~/.bashrc
    source ~/.zshrc
    
    # 初始化 conda 并设置使用 classic solver
    conda init --all || { echo "初始化 conda 失败"; exit 1; }
    conda config --set solver classic || { echo "设置 conda solver 失败"; exit 1; }
    
    # 配置 conda 源
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
    conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/
    conda config --set show_channel_urls yes
}

# 安装 Java 环境
install_java() {
    echo "安装 Java 环境..."
    
    # 检查是否已安装 jenv
    if [ ! -d "$HOME/.jenv" ]; then
        git clone https://github.com/jenv/jenv.git ~/.jenv || { echo "安装 jenv 失败"; exit 1; }
    else
        echo "jenv 已安装，跳过克隆步骤"
    fi
    
    # 配置 jenv 环境变量
    if ! grep -q 'export PATH="$HOME/.jenv/bin:$PATH"' ~/.bashrc; then
        echo 'export PATH="$HOME/.jenv/bin:$PATH"' >> ~/.bashrc
        echo 'eval "$(jenv init -)"' >> ~/.bashrc
    fi
    if ! grep -q 'export PATH="$HOME/.jenv/bin:$PATH"' ~/.zshrc; then
        echo 'export PATH="$HOME/.jenv/bin:$PATH"' >> ~/.zshrc
        echo 'eval "$(jenv init -)"' >> ~/.zshrc
    fi
    
    # 刷新环境变量
    export PATH="$HOME/.jenv/bin:$PATH"
    eval "$(jenv init -)"
    
    # 安装 JDK 17 和 21
    sudo apt install -y openjdk-17-jdk openjdk-21-jdk || { echo "安装 JDK 失败"; exit 1; }
    
    # 添加 Java 版本到 jenv（如果尚未添加）
    if ! jenv versions | grep -q "17"; then
        jenv add /usr/lib/jvm/java-17-openjdk-amd64
    fi
    if ! jenv versions | grep -q "21"; then
        jenv add /usr/lib/jvm/java-21-openjdk-amd64
    fi
    
    # 设置全局 Java 21 为默认版本
    jenv global 21 || { echo "设置默认 Java 版本失败"; exit 1; }
    
    # 启用 jenv 插件（如果尚未启用）
    for plugin in export maven gradle groovy scala; do
        if [ ! -f "$HOME/.jenv/plugins/$plugin/etc/jenv.d/exec/$plugin.plugin" ]; then
            jenv enable-plugin $plugin
        fi
    done
    
    echo "Java 环境安装完成，当前版本："
    jenv version
    
    # 安装和配置 Maven
    if ! command -v mvn &> /dev/null; then
        echo "安装 Maven"
        sudo apt install -y maven || { echo "安装 Maven 失败"; exit 1; }
    else
        echo "Maven 已安装"
    fi
    
    echo "配置 Maven 镜像"
    mkdir -p ~/.m2
    if [ ! -f ~/.m2/settings.xml ] || ! grep -q "maven.aliyun.com" ~/.m2/settings.xml; then
        echo '<settings>
    <mirrors>
        <mirror>
            <id>aliyun</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Maven Mirror</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </mirror>
    </mirrors>
</settings>' > ~/.m2/settings.xml || { echo "配置 Maven 镜像失败"; exit 1; }
    else
        echo "Maven 镜像已配置"
    fi
}

# 安装 Vagrant
install_vagrant() {
    echo "安装 Vagrant..."
    
    # 使用 Ubuntu Jammy (22.04) 的源
    curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com jammy main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
    
    # 更新源并安装
    sudo apt update && sudo apt install -y vagrant || { 
        echo "尝试直接下载 deb 包安装..."
        # 如果源安装失败，尝试直接下载 deb 包
        wget https://releases.hashicorp.com/vagrant/2.4.1/vagrant_2.4.1-1_amd64.deb -O vagrant.deb && \
        sudo apt install -y ./vagrant.deb || { echo "安装 Vagrant 失败"; exit 1; }
        rm -f vagrant.deb
    }
    
    echo "安装 VirtualBox（Vagrant 依赖）"
    sudo apt install -y virtualbox || { echo "安装 VirtualBox 失败"; exit 1; }
}

# 配置 Android SDK
setup_android_sdk() {
    echo "配置 Android SDK..."
    
    # 确保基本工具已安装
    sudo apt install -y grep wget || {
        echo "安装基本工具失败";
        exit 1;
    }
    
    # 首先检查Android Studio的默认SDK路径
    local default_sdk_paths=(
        "$HOME/Android/Sdk"                   # Android Studio 默认路径
        "$HOME/.android/sdk"                  # 备选路径1
        "$HOME/Library/Android/sdk"           # 备选路径2
    )
    
    # 查找已存在的SDK路径
    ANDROID_HOME=""
    for path in "${default_sdk_paths[@]}"; do
        if [ -d "$path" ]; then
            ANDROID_HOME="$path"
            echo "找到现有 Android SDK: $ANDROID_HOME"
            break
        fi
    done
    
    # 如果没找到SDK，提示错误
    if [ -z "$ANDROID_HOME" ]; then
        echo "错误: 未找到 Android SDK，请确保已正确安装 Android Studio"
        exit 1
    fi
    
    # 配置环境变量（如果尚未配置）
    if ! /bin/grep -q "export ANDROID_HOME=" ~/.bashrc; then
        echo "export ANDROID_HOME=$ANDROID_HOME" >> ~/.bashrc
        echo 'export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"' >> ~/.bashrc
    fi
    if ! /bin/grep -q "export ANDROID_HOME=" ~/.zshrc; then
        echo "export ANDROID_HOME=$ANDROID_HOME" >> ~/.zshrc
        echo 'export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"' >> ~/.zshrc
    fi
    
    # 刷新环境变量
    export ANDROID_HOME="$ANDROID_HOME"
    export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"
    
    # 检查并接受许可证
    echo "检查 Android SDK 许可证..."
    echo "y" | sdkmanager --licenses > /dev/null 2>&1 || true
    
    # 清理临时文件
    echo "清理临时文件..."
    /bin/rm -rf cmdline-tools cmdline-tools.zip 2>/dev/null || true
    
    echo "Android SDK 配置完成"
}

# 修改 install_flutter 函数
install_flutter() {
    #!/bin/bash
    echo "安装 Flutter..."
    
    # 确保基本工具已安装
    sudo apt install -y wget curl git unzip xz-utils tar || {
        echo "安装基本工具失败";
        exit 1;
    }
    
    # 创建 DevTools 目录
    mkdir -p "$HOME/DevTools"
    
    # 检查是否已安装
    if [ -d "$HOME/DevTools/flutter" ]; then
        echo "Flutter 已安装，跳过安装步骤。"
        return
    fi
    
    # 安装基础依赖
    sudo apt install -y \
        zip \
        libglu1-mesa || { echo "安装 Flutter 基础依赖失败"; exit 1; }
    
    # 安装 Linux 开发所需依赖
    echo "安装 Linux 开发依赖..."
    
    # 添加 Google Chrome 仓库
    echo "添加 Google Chrome 仓库..."
    /usr/bin/wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo gpg --dearmor -o /usr/share/keyrings/google-chrome.gpg
    echo "deb [arch=amd64 signed-by=/usr/share/keyrings/google-chrome.gpg] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
    sudo apt update
    
    # 安装依赖
    sudo apt install -y \
        clang \
        cmake \
        ninja-build \
        pkg-config \
        libgtk-3-dev \
        liblzma-dev \
        libstdc++-12-dev \
        libgstreamer1.0-dev \
        libgstreamer-plugins-base1.0-dev \
        google-chrome-stable \
        || {
            echo "尝试安装 Chromium 作为替代..."
            sudo apt install -y chromium
            # 更新 Chrome 可执行文件路径为 Chromium
            if ! grep -q 'export CHROME_EXECUTABLE=/usr/bin/chromium' ~/.bashrc; then
                echo 'export CHROME_EXECUTABLE=/usr/bin/chromium' >> ~/.bashrc
            fi
            if ! grep -q 'export CHROME_EXECUTABLE=/usr/bin/chromium' ~/.zshrc; then
                echo 'export CHROME_EXECUTABLE=/usr/bin/chromium' >> ~/.zshrc
            fi
            export CHROME_EXECUTABLE=/usr/bin/chromium
        }
    
    # 配置 Android SDK
    setup_android_sdk
    
    # 下载 Flutter SDK（使用国内镜像）
    echo "下载 Flutter SDK..."
    FLUTTER_STORAGE_URL="https://storage.flutter-io.cn"
    FLUTTER_VERSION="3.24.4"
    /usr/bin/wget "${FLUTTER_STORAGE_URL}/flutter_infra_release/releases/stable/linux/flutter_linux_${FLUTTER_VERSION}-stable.tar.xz" -O flutter.tar.xz || {
        echo "从主镜像下载失败，尝试备用镜像..."
        /usr/bin/wget "https://mirrors.tuna.tsinghua.edu.cn/flutter/flutter_infra_release/releases/stable/linux/flutter_linux_${FLUTTER_VERSION}-stable.tar.xz" -O flutter.tar.xz || {
            echo "下载 Flutter SDK 失败";
            exit 1;
        }
    }
    
    # 解压 Flutter SDK
    echo "解压 Flutter SDK..."
    # 确保命令可用
    export PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:$PATH"

    # 先安装xz-utils（用于处理.xz文件）
    sudo apt install -y xz-utils || { echo "安装 xz-utils 失败"; exit 1; }

    # 使用完整的命令路径，并添加错误检查
    if ! /bin/tar xJf flutter.tar.xz -C "$HOME/DevTools"; then
        echo "解压 Flutter SDK 失败"
        /bin/rm -f flutter.tar.xz
        exit 1
    fi

    # 清理下载文件
    /bin/rm -f flutter.tar.xz || echo "警告：清理临时文件失败，请手动删除 flutter.tar.xz"
    
    # 配置环境变量
    if ! grep -q 'export PATH="$HOME/DevTools/flutter/bin:$PATH"' ~/.bashrc; then
        echo 'export PATH="$HOME/DevTools/flutter/bin:$PATH"' >> ~/.bashrc
    fi
    if ! grep -q 'export PATH="$HOME/DevTools/flutter/bin:$PATH"' ~/.zshrc; then
        echo 'export PATH="$HOME/DevTools/flutter/bin:$PATH"' >> ~/.zshrc
    fi
    
    # 配置Flutter国内镜像
    if ! grep -q 'export PUB_HOSTED_URL="https://pub.flutter-io.cn"' ~/.bashrc; then
        echo 'export PUB_HOSTED_URL="https://pub.flutter-io.cn"' >> ~/.bashrc
        echo 'export FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"' >> ~/.bashrc
    fi
    if ! grep -q 'export PUB_HOSTED_URL="https://pub.flutter-io.cn"' ~/.zshrc; then
        echo 'export PUB_HOSTED_URL="https://pub.flutter-io.cn"' >> ~/.zshrc
        echo 'export FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"' >> ~/.zshrc
    fi
    
    # 刷新环境变量
    export PATH="$HOME/DevTools/flutter/bin:$PATH"
    export PUB_HOSTED_URL="https://pub.flutter-io.cn"
    export FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"
    
    # 禁用 Flutter 遥测收集
    flutter config --no-analytics
    
    # 运行flutter doctor
    echo "运行 flutter doctor 检查环境..."
    flutter doctor -v || { echo "Flutter 环境检查失败"; exit 1; }
    
    echo "Flutter 安装完成！"
    echo "请运行 'source ~/.bashrc' 或 'source ~/.zshrc' 以更新环境变量"
}

# 修改 main 函数
main() {
    echo "请选择要安装的组件："
    echo "0. 全部安装"
    echo "1. 更新系统"
    echo "2. 安装基础开发工具"
    echo "3. 配置开发环境"
    echo "4. 优化系统"
    echo "5. 安装常用工具"
    echo "6. 安装 Docker"
    echo "7. 安装 Node.js"
    echo "8. 安装 Miniconda"
    echo "9. 安装 Java"
    echo "10. 安装 Vagrant 和 VirtualBox"
    echo "11. 安装 Flutter"
    
    echo "请输入序号（0-11）： "
    read choice
    
    case $choice in
        0) 
            update_system
            install_basic_dev_tools
            setup_dev_env
            optimize_system
            install_common_tools
            install_docker
            install_node
            install_miniconda
            install_java
            install_vagrant
            install_flutter ;;
        1) update_system ;;
        2) install_basic_dev_tools ;;
        3) setup_dev_env ;;
        4) optimize_system ;;
        5) install_common_tools ;;
        6) install_docker ;;
        7) install_node ;;
        8) install_miniconda ;;
        9) install_java ;;
        10) install_vagrant ;;
        11) install_flutter ;;
        *) echo "无效的选择，请输入 0 到 11 之间的数字。" ;;
    esac
    
    echo "初始化完成！请重启系统以应用所有更改。"
    echo "注意事项："
    echo "1. Java 环境已配置完成，可使用 jenv 切换版本"
    echo "2. Python 环境在 conda 环境 'py3' 中"
    echo "3. 请运行 'source ~/.bashrc' 或 'source ~/.zshrc' 以激活环境变量"
}
main
