#!/bin/bash

docker network create --driver overlay --subnet 10.0.0.0/24 dbnet

DISCOVERY_URL=$(curl -sw "\n" 'https://discovery.etcd.io/new?size=1' | sed 's/https/http/')

echo "DISCOVERY_URL:$DISCOVERY_URL"

docker service create --name etcd --replicas 1 --network dbnet -p 2379:2379 -p 2380:2380 -p 7001:7001 \
	ibootz/coreos-etcd:v3.4.1 etcd --name etcd --enable-v2 --data-dir /etcd-data \
	--listen-peer-urls http://0.0.0.0:2380 --listen-client-urls http://0.0.0.0:2379 --advertise-client-urls http://0.0.0.0:2379 \
	--discovery $DISCOVERY_URL

sleep 15s

IP=$(docker service inspect etcd | grep "Addr\": \"10.0.0");
IP=$(echo $IP);
IP=$(echo ${IP:9} | awk -F '/' '{print $1}');

echo "IP:$IP"

docker volume create db_data

docker service create --name db --mount type=volume,source=db_data,target=/var/lib/mysql --mode global -p 3306:3306 --network dbnet -e MYSQL_ROOT_PASSWORD=123456 -e DISCOVERY_SERVICE=etcd:2379 -e XTRABACKUP_PASSWORD=123456 -e CLUSTER_NAME=galera perconalab/percona-xtradb-cluster

