global:
  common:
    labels: {}
  image: {}
  pod:
    labels: {}
  edition: ee
  application:
    create: false
    links: []
    allowClusterRoles: true
  hosts:
    domain: ibootz.com
    hostSuffix: null
    https: false
    externalIP: null
    ssh: null
    gitlab: {}
    minio: {}
    registry: {}
    tls: {}
    smartcard: {}
    kas: {}
    pages: {}
  ingress:
    apiVersion: ''
    configureCertmanager: false
    provider: nginx
	class: nginx
    annotations:
		kubernetes.io/ingress.class: 'nginx'
    enabled: true
    tls:
      enabled: false
      secretName: RELEASE-ingress-tls
    path: /
    pathType: Prefix
  hpa:
    apiVersion: ''
  pdb:
    apiVersion: ''
  batch:
    cronJob:
      apiVersion: ''
  gitlab:
    license: {}
  initialRootPassword: {}
  psql:
    connectTimeout: null
    keepalives: null
    keepalivesIdle: null
    keepalivesInterval: null
    keepalivesCount: null
    tcpUserTimeout: null
    password: {}
  redis:
    password:
      enabled: true
  gitaly:
    enabled: true
    authToken: {}
    internal:
      names:
        - default
    external: []
    service:
      name: gitaly
      type: ClusterIP
      externalPort: 8075
      internalPort: 8075
      tls:
        externalPort: 8076
        internalPort: 8076
    tls:
      enabled: false
      secretName: RELEASE-gitlab-tls
  praefect:
    enabled: false
    replaceInternalGitaly: true
    authToken: {}
    autoMigrate: true
    dbSecret: {}
    virtualStorages:
      - name: default
        gitalyReplicas: 3
        maxUnavailable: 1
    psql:
      sslMode: disable
    service:
      name: praefect
      type: ClusterIP
      externalPort: 8075
      internalPort: 8075
      tls:
        externalPort: 8076
        internalPort: 8076
    tls:
      enabled: false
  minio:
    enabled: true
    ingress:
      tls:
        secretName: RELEASE-minio-tls
    credentials: {}
  grafana:
    enabled: false
  appConfig:
    enableUsagePing: true
    enableSeatLink: true
    enableImpersonation: null
    applicationSettingsCacheSeconds: 60
    defaultCanCreateGroup: true
    usernameChangingEnabled: true
    issueClosingPattern: null
    defaultTheme: null
    defaultProjectsFeatures:
      issues: true
      mergeRequests: true
      wiki: true
      snippets: true
      builds: true
    webhookTimeout: null
    maxRequestDurationSeconds: null
    cron_jobs: {}
    contentSecurityPolicy:
      enabled: false
      report_only: true
    gravatar:
      plainUrl: null
      sslUrl: null
    extra:
      googleAnalyticsId: null
      matomoUrl: null
      matomoSiteId: null
      matomoDisableCookies: null
      oneTrustId: null
      googleTagManagerNonceId: null
      bizible: null
    object_store:
      enabled: false
      proxy_download: true
      storage_options: {}
      connection: {}
    lfs:
      enabled: true
      proxy_download: true
      bucket: git-lfs
      connection: {}
    artifacts:
      enabled: true
      proxy_download: true
      bucket: gitlab-artifacts
      connection: {}
    uploads:
      enabled: true
      proxy_download: true
      bucket: gitlab-uploads
      connection: {}
    packages:
      enabled: true
      proxy_download: true
      bucket: gitlab-packages
      connection: {}
    externalDiffs:
      enabled: false
      when: null
      proxy_download: true
      bucket: gitlab-mr-diffs
      connection: {}
    terraformState:
      enabled: false
      bucket: gitlab-terraform-state
      connection: {}
    ciSecureFiles:
      enabled: false
      bucket: gitlab-ci-secure-files
      connection: {}
    dependencyProxy:
      enabled: false
      proxy_download: true
      bucket: gitlab-dependency-proxy
      connection: {}
    backups:
      bucket: gitlab-backups
      tmpBucket: tmp
    incomingEmail:
      enabled: false
      address: ''
      host: imap.gmail.com
      port: 993
      ssl: true
      startTls: false
      user: ''
      password:
        secret: ''
        key: password
      expungeDeleted: false
      logger:
        logPath: /dev/stdout
      mailbox: inbox
      idleTimeout: 60
      inboxMethod: imap
      clientSecret:
        key: secret
      pollInterval: 60
      deliveryMethod: webhook
      authToken: {}
    serviceDeskEmail:
      enabled: false
      address: ''
      host: imap.gmail.com
      port: 993
      ssl: true
      startTls: false
      user: ''
      password:
        secret: ''
        key: password
      expungeDeleted: false
      logger:
        logPath: /dev/stdout
      mailbox: inbox
      idleTimeout: 60
      inboxMethod: imap
      clientSecret:
        key: secret
      pollInterval: 60
      deliveryMethod: webhook
      authToken: {}
    ldap:
      preventSignin: false
      servers: {}
    gitlab_kas: {}
    omniauth:
      enabled: false
      autoSignInWithProvider: null
      syncProfileFromProvider: []
      syncProfileAttributes:
        - email
      allowSingleSignOn:
        - saml
      blockAutoCreatedUsers: true
      autoLinkLdapUser: false
      autoLinkSamlUser: false
      autoLinkUser: []
      externalProviders: []
      allowBypassTwoFactor: []
      providers: []
    kerberos:
      enabled: false
      keytab:
        key: keytab
      servicePrincipalName: ''
      krb5Config: ''
      dedicatedPort:
        enabled: false
        port: 8443
        https: true
      simpleLdapLinkingAllowedRealms: []
    sentry:
      enabled: false
      dsn: null
      clientside_dsn: null
      environment: null
    gitlab_docs:
      enabled: false
      host: ''
    smartcard:
      enabled: false
      CASecret: null
      clientCertificateRequiredHost: null
      sanExtensions: false
      requiredForGitAccess: false
    sidekiq:
      routingRules: []
    initialDefaults: {}
  oauth:
    gitlab-pages: {}
  geo:
    enabled: false
    role: primary
    nodeName: null
    psql:
      password: {}
    registry:
      replication:
        enabled: false
        primaryApiUrl: null
  kas:
    enabled: true
    service:
      apiExternalPort: 8153
  spamcheck:
    enabled: false
  shell:
    authToken: {}
    hostKeys: {}
    tcp:
      proxyProtocol: false
  railsSecrets: {}
  rails:
    bootsnap:
      enabled: true
  registry:
    bucket: registry
    certificate: {}
    httpSecret: {}
    notificationSecret: {}
    tls:
      enabled: false
      secretName: RELEASE-registry-tls
    redis:
      cache:
        password: {}
    notifications: {}
  pages:
    enabled: false
    accessControl: false
    path: null
    host: null
    port: null
    https: null
    externalHttp: []
    externalHttps: []
    artifactsServer: true
    localStore:
      enabled: false
    objectStore:
      enabled: true
      bucket: gitlab-pages
      connection: {}
    apiSecret: {}
    authSecret: {}
  runner:
    registrationToken: {}
  smtp:
    enabled: false
    address: smtp.mailgun.org
    port: 2525
    user_name: ''
    password:
      secret: ''
      key: password
    authentication: plain
    starttls_auto: false
    openssl_verify_mode: peer
    pool: false
  email:
    from: ''
    display_name: GitLab
    reply_to: ''
    subject_suffix: ''
    smime:
      enabled: false
      secretName: ''
      keyName: tls.key
      certName: tls.crt
  time_zone: Asia/Shanghai
  service:
    labels: {}
    annotations: {}
  deployment:
    annotations: {}
  antiAffinity: soft
  affinity:
    podAntiAffinity:
      topologyKey: kubernetes.io/hostname
  workhorse:
    serviceName: webservice-default
    tls:
      enabled: false
  webservice:
    workerTimeout: 60
  certificates:
    image:
      repository: registry.jihulab.com/gitlab-cn/build/cng-images/alpine-certificates
      tag: >-
        ********-r2@sha256:f94bf5bbb8fd4599559dac95c36f689858e0b86a3b412cd6f511014a413ecff0
    customCAs: []
  kubectl:
    image:
      repository: registry.jihulab.com/gitlab-cn/build/cng-images/kubectl
      tag: >-
        1.18.20@sha256:86d6c2111fa427e693e01c27004092192c4733f0f13656d8283765fd7f8bc665
    securityContext:
      runAsUser: 65534
      fsGroup: 65534
  busybox:
    image:
      repository: registry.jihulab.com/gitlab-cn/cloud-native/mirror/images/busybox
      tag: latest
  serviceAccount:
    enabled: false
    create: true
    annotations: {}
  tracing:
    connection:
      string: ''
    urlTemplate: ''
  extraEnv: {}
  extraEnvFrom: {}
upgradeCheck:
  enabled: true
  image: {}
  securityContext:
    runAsUser: 65534
    fsGroup: 65534
  tolerations: []
  annotations: {}
  resources:
    requests:
      cpu: 50m
certmanager:
  installCRDs: true
  nameOverride: certmanager
  install: false
  rbac:
    create: true
nginx-ingress:
  enabled: false
  tcpExternalConfig: 'true'
  controller:
    addHeaders:
      Referrer-Policy: strict-origin-when-cross-origin
    allowSnippetAnnotations: false
    config:
      hsts: 'true'
      hsts-include-subdomains: 'false'
      hsts-max-age: '********'
      server-name-hash-bucket-size: '256'
      use-http2: 'true'
      ssl-ciphers: >-
        ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4
      ssl-protocols: TLSv1.3 TLSv1.2
      server-tokens: 'false'
    service:
      externalTrafficPolicy: Local
    ingressClassByName: false
    ingressClassResource:
      name: '{{ include "ingress.class.name" $ }}'
    resources:
      requests:
        cpu: 100m
        memory: 100Mi
    publishService:
      enabled: true
    replicaCount: 2
    minAvailable: 1
    scope:
      enabled: true
    metrics:
      enabled: true
      service:
        annotations:
          gitlab.com/prometheus_scrape: 'true'
          gitlab.com/prometheus_port: '10254'
          prometheus.io/scrape: 'true'
          prometheus.io/port: '10254'
    admissionWebhooks:
      enabled: false
  defaultBackend:
    resources:
      requests:
        cpu: 5m
        memory: 5Mi
  rbac:
    create: true
    scope: false
  serviceAccount:
    create: true
prometheus:
  install: false
  rbac:
    create: true
  alertmanager:
    enabled: false
  alertmanagerFiles:
    alertmanager.yml: {}
  kubeStateMetrics:
    enabled: false
  nodeExporter:
    enabled: false
  pushgateway:
    enabled: false
  server:
    retention: 15d
    strategy:
      type: Recreate
  serverFiles:
    prometheus.yml:
      scrape_configs:
        - job_name: prometheus
          static_configs:
            - targets:
                - 'localhost:9090'
        - job_name: kubernetes-apiservers
          kubernetes_sd_configs:
            - role: endpoints
          scheme: https
          tls_config:
            ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
            insecure_skip_verify: true
          bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
          relabel_configs:
            - source_labels:
                - __meta_kubernetes_namespace
                - __meta_kubernetes_service_name
                - __meta_kubernetes_endpoint_port_name
              action: keep
              regex: default;kubernetes;https
        - job_name: kubernetes-pods
          kubernetes_sd_configs:
            - role: pod
          relabel_configs:
            - source_labels:
                - __meta_kubernetes_pod_annotation_gitlab_com_prometheus_scrape
              action: keep
              regex: true
            - source_labels:
                - __meta_kubernetes_pod_annotation_gitlab_com_prometheus_scheme
              action: replace
              regex: (https?)
              target_label: __scheme__
            - source_labels:
                - __meta_kubernetes_pod_annotation_gitlab_com_prometheus_path
              action: replace
              target_label: __metrics_path__
              regex: (.+)
            - source_labels:
                - __address__
                - __meta_kubernetes_pod_annotation_gitlab_com_prometheus_port
              action: replace
              regex: '([^:]+)(?::\d+)?;(\d+)'
              replacement: '$1:$2'
              target_label: __address__
            - action: labelmap
              regex: __meta_kubernetes_pod_label_(.+)
            - source_labels:
                - __meta_kubernetes_namespace
              action: replace
              target_label: kubernetes_namespace
            - source_labels:
                - __meta_kubernetes_pod_name
              action: replace
              target_label: kubernetes_pod_name
        - job_name: kubernetes-service-endpoints
          kubernetes_sd_configs:
            - role: endpoints
          relabel_configs:
            - action: keep
              regex: true
              source_labels:
                - >-
                  __meta_kubernetes_service_annotation_gitlab_com_prometheus_scrape
            - action: replace
              regex: (https?)
              source_labels:
                - >-
                  __meta_kubernetes_service_annotation_gitlab_com_prometheus_scheme
              target_label: __scheme__
            - action: replace
              regex: (.+)
              source_labels:
                - >-
                  __meta_kubernetes_service_annotation_gitlab_com_prometheus_path
              target_label: __metrics_path__
            - action: replace
              regex: '([^:]+)(?::\d+)?;(\d+)'
              replacement: '$1:$2'
              source_labels:
                - __address__
                - >-
                  __meta_kubernetes_service_annotation_gitlab_com_prometheus_port
              target_label: __address__
            - action: labelmap
              regex: __meta_kubernetes_service_label_(.+)
            - action: replace
              source_labels:
                - __meta_kubernetes_namespace
              target_label: kubernetes_namespace
            - action: replace
              source_labels:
                - __meta_kubernetes_service_name
              target_label: kubernetes_name
            - action: replace
              source_labels:
                - __meta_kubernetes_pod_node_name
              target_label: kubernetes_node
        - job_name: kubernetes-services
          metrics_path: /probe
          params:
            module:
              - http_2xx
          kubernetes_sd_configs:
            - role: service
          relabel_configs:
            - source_labels:
                - >-
                  __meta_kubernetes_service_annotation_gitlab_com_prometheus_probe
              action: keep
              regex: true
            - source_labels:
                - __address__
              target_label: __param_target
            - target_label: __address__
              replacement: blackbox
            - source_labels:
                - __param_target
              target_label: instance
            - action: labelmap
              regex: __meta_kubernetes_service_label_(.+)
            - source_labels:
                - __meta_kubernetes_namespace
              target_label: kubernetes_namespace
            - source_labels:
                - __meta_kubernetes_service_name
              target_label: kubernetes_name
redis:
  install: true
  existingSecret: gitlab-redis-secret
  existingSecretKey: redis-password
  usePasswordFile: true
  cluster:
    enabled: false
  metrics:
    enabled: true
postgresql:
  postgresqlUsername: gitlab
  postgresqlPostgresPassword: bogus
  install: true
  postgresqlDatabase: gitlabhq_production
  image:
    tag: 12.7.0
  usePasswordFile: true
  existingSecret: bogus
  initdbScriptsConfigMap: bogus
  master:
    extraVolumeMounts:
      - name: custom-init-scripts
        mountPath: /docker-entrypoint-preinitdb.d/init_revision.sh
        subPath: init_revision.sh
    podAnnotations:
      postgresql.gitlab/init-revision: '1'
  metrics:
    enabled: true
registry:
  enabled: true
  ingress:
    tls:
      secretName: RELEASE-registry-tls
shared-secrets:
  enabled: true
  rbac:
    create: true
  selfsign:
    image:
      repository: registry.jihulab.com/gitlab-cn/build/cng-images/cfssl-self-sign
      tag: >-
        1.6.1@sha256:d12d92211107b52027ed7420976b3c3509598f8b3304de7ce5b84659bfa0d8e8
    keyAlgorithm: rsa
    keySize: '4096'
    expiry: 3650d
    caSubject: GitLab Helm Chart
  env: production
  serviceAccount:
    enabled: true
    create: true
    name: null
  resources:
    requests:
      cpu: 50m
  securityContext:
    runAsUser: 65534
    fsGroup: 65534
  tolerations: []
  podLabels: {}
  annotations: {}
gitlab-runner:
  install: false
  rbac:
    create: true
  runners:
    locked: false
    config: |
      [[runners]]
        [runners.kubernetes]
        image = "ubuntu:18.04"
        {{- if .Values.global.minio.enabled }}
        [runners.cache]
          Type = "s3"
          Path = "gitlab-runner"
          Shared = true
          [runners.cache.s3]
            ServerAddress = {{ include "gitlab-runner.cache-tpl.s3ServerAddress" . }}
            BucketName = "runner-cache"
            BucketLocation = "us-east-1"
            Insecure = false
        {{ end }}
  podAnnotations:
    gitlab.com/prometheus_scrape: 'true'
    gitlab.com/prometheus_port: 9252
grafana:
  image:
    tag: 7.5.16
  nameOverride: grafana-app
  admin:
    existingSecret: bogus
  env:
    GF_SECURITY_ADMIN_USER: bogus
    GF_SECURITY_ADMIN_PASSWORD: bogus
  command:
    - sh
    - '-x'
    - /tmp/scripts/import-secret.sh
  sidecar:
    dashboards:
      enabled: true
      label: gitlab_grafana_dashboard
    datasources:
      enabled: true
      label: gitlab_grafana_datasource
  grafana.ini:
    server:
      serve_from_sub_path: true
      root_url: 'http://localhost/-/grafana/'
    auth:
      login_cookie_name: gitlab_grafana_session
  extraSecretMounts:
    - name: initial-password
      mountPath: /tmp/initial
      readOnly: true
      secretName: gitlab-grafana-initial-password
      defaultMode: 400
  extraConfigmapMounts:
    - name: import-secret
      mountPath: /tmp/scripts
      configMap: gitlab-grafana-import-secret
      readOnly: true
  testFramework:
    enabled: false
gitlab:
  toolbox:
    replicas: 1
    antiAffinityLabels:
      matchLabels:
        app: gitaly
  webservice:
    enabled: true
    ingress:
      tls:
        enabled: false
        secretName: RELEASE-gitlab-tls
  kas:
    ingress:
      tls:
        secretName: RELEASE-kas-tls
