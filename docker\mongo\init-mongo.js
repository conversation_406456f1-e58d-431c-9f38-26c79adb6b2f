/**
 * MongoDB 初始化脚本
 * 在容器首次启动时执行，用于创建应用数据库和用户
 * 注意：此脚本由 MongoDB 容器在初始化时自动执行
 */

// 连接到 admin 数据库进行认证
const adminDb = db.getSiblingDB('admin');

// 获取环境变量
const rootUsername = 'root';
const rootPassword = '123456';
const appDbName = 'appdb';
const appUsername = 'appuser';
const appPassword = '123456';

// 检查是否已经初始化
try {
  // 尝试认证
  adminDb.auth(rootUsername, rootPassword);
  
  // 认证成功，创建应用数据库和用户
  print('==> 成功连接到 MongoDB');
  
  // 创建应用数据库
  const appDb = db.getSiblingDB(appDbName);
  
  // 检查用户是否已存在
  const userExists = adminDb.getUser(appUsername) || appDb.getUser(appUsername);
  
  if (!userExists) {
    // 创建应用数据库用户
    appDb.createUser({
      user: appUsername,
      pwd: appPassword,
      roles: [
        { role: 'readWrite', db: appDbName },
        { role: 'read', db: 'local' }
      ]
    });
    
    // 创建测试集合和索引
    appDb.createCollection('users');
    appDb.users.createIndex({ email: 1 }, { unique: true });
    
    print('==> 成功创建应用数据库和用户');
  } else {
    print('==> 应用数据库用户已存在，跳过创建');
  }
  
  print('==> 应用数据库初始化完成');
} catch (error) {
  print(`==> 错误: ${error.message}`);
  print('==> 警告: 数据库初始化失败，请检查凭据和连接设置');
}
