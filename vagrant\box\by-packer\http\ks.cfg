# License agreement
eula --agreed

# Use network installation
# url --url="https://download.rockylinux.org/pub/rocky/9/BaseOS/x86_64/os/"
# repo --name="AppStream" --baseurl=https://download.rockylinux.org/pub/rocky/9/AppStream/x86_64/os/

# 使用文本模式安装
text

# 禁用首次启动配置
firstboot --disabled

# 设置系统语言为英语（美国）
lang en_US.UTF-8

# 设置键盘布局为美式键盘
keyboard --vckeymap=us --xlayouts='us'

# 配置网络使用DHCP自动获取IP
network --bootproto=dhcp

# 设置root用户密码：123456
rootpw --iscrypted $6$0Lr1vew7dCMJT64e$xBN/lzC8Y8QpD74t7uif5ABDxB9JpIggVGd6b7ILQdhbwYjIaJMLDVsh9Pd35xENdBKIp76GsX/RieKedeheJ0

# 禁用防火墙
firewall --disabled

# 禁用SELinux
selinux --disabled

# 设置时区为亚洲/上海
timezone Asia/Shanghai --utc

# 跳过X Window系统安装
skipx

# 启用影子密码并使用SHA-512算法进行密码加密
authselect select sssd with-sha512 --force

# 设置主机名
network --device=ens33 --bootproto=dhcp --ipv6=auto --activate
network --hostname=localhost.localdomain

# 添加一个用户vagrant，密码为vagrant，用户执行后处理脚本，即scripts/目录下的脚本文件
user --groups=wheel --name=vagrant --password=$6$0Lr1vew7dCMJT64e$xBN/lzC8Y8QpD74t7uif5ABDxB9JpIggVGd6b7ILQdhbwYjIaJMLDVsh9Pd35xENdBKIp76GsX/RieKedeheJ0 --iscrypted --gecos="vagrant"

# 设置引导加载器安装在主引导记录（MBR）
bootloader --location=mbr --append="crashkernel=auto"

# 清除MBR
zerombr

# 清除所有分区并初始化磁盘标签
clearpart --all --initlabel

# 自动分区
autopart --type=lvm

# # Reboot after successful installation
# reboot

%packages --ignoremissing
# dnf group info minimal-environment
@core
kernel-devel
kernel-headers
# Exclude unnecessary firmwares
-iwl*firmware
%end

# 配置post安装脚本
%post --nochroot --logfile=/mnt/sysimage/root/ks-post.log
echo -e "\n\n\033[1;36m=========== 安装后脚本运行中 ===============\033[0m"
# Disable quiet boot and splash screen
sed --follow-symlinks -i "s/ rhgb quiet//" /mnt/sysimage/etc/default/grub
sed --follow-symlinks -i "s/ rhgb quiet//" /mnt/sysimage/boot/grub2/grubenv

# Passwordless sudo for the user 'vagrant'
echo "vagrant ALL=(ALL) NOPASSWD: ALL" >> /mnt/sysimage/etc/sudoers.d/vagrant

dnf install epel-release -y
dnf update --refresh -y
dnf install -y dkms kernel-devel kernel-headers

# 重启系统以应用新的内核
echo "Rebooting the system to apply the new kernel..."
reboot

%end
