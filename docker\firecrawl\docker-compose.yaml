name: firecrawl

x-common-service: &common-service
    image: ibootz/firecrawl:v1.8.0

    ulimits:
        nofile:
            soft: 65535
            hard: 65535
    networks:
        - backend
    extra_hosts:
        - "host.docker.internal:host-gateway"

services:
    playwright-service:
        image: ibootz/playwright-service-ts:v1.8.0
        environment:
            PORT: 3000
            # PROXY_SERVER: ${PROXY_SERVER}
            # PROXY_USERNAME: ${PROXY_USERNAME}
            # PROXY_PASSWORD: ${PROXY_PASSWORD}
            BLOCK_MEDIA: ${BLOCK_MEDIA}
        networks:
            - backend

    api:
        <<: *common-service
        environment:
            FLY_PROCESS_GROUP: app
        env_file:
            - .env
        depends_on:
            - redis
            - playwright-service
        ports:
            - "${PORT:-3002}:${INTERNAL_PORT:-3002}"
        command: ["pnpm", "run", "start:production"]

    worker:
        <<: *common-service
        environment:
            FLY_PROCESS_GROUP: worker
        env_file:
            - .env
        depends_on:
            - redis
            - playwright-service
            - api
        command: ["pnpm", "run", "workers"]

    redis:
        # NOTE: If you want to use <PERSON><PERSON> (open source) instead of <PERSON><PERSON> (source available),
        # uncomment the Valkey statement and comment out the Redis statement.
        # Using Valkey with Firecrawl is untested and not guaranteed to work. Use with caution.
        image: redis:alpine
        # image: valkey/valkey:alpine
        networks:
            - backend
        command: redis-server --bind 0.0.0.0

networks:
    backend:
        driver: bridge
