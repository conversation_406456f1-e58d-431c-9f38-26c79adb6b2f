#!/bin/bash
# Refact.ai 自托管服务管理脚本

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 显示标题
echo -e "${GREEN}====================================${NC}"
echo -e "${GREEN}   Refact.ai 自托管服务管理脚本    ${NC}"
echo -e "${GREEN}====================================${NC}"

# 检测是否在WSL2环境中
is_wsl() {
    if grep -q Microsoft /proc/version || grep -q microsoft /proc/version; then
        return 0  # 是WSL
    else
        return 1  # 不是WSL
    fi
}

# 检查Docker是否已安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装。请先安装Docker。${NC}"
    if is_wsl; then
        echo -e "${YELLOW}检测到WSL2环境，建议安装Docker Desktop for Windows并启用WSL2集成。${NC}"
        echo -e "${YELLOW}访问: https://docs.docker.com/desktop/install/windows-install/${NC}"
    fi
    exit 1
fi

# 检查Docker Compose是否已安装
if ! command -v docker compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装。请先安装Docker Compose。${NC}"
    exit 1
fi

# 检查NVIDIA容器工具包是否已安装
if ! command -v nvidia-smi &> /dev/null; then
    echo -e "${YELLOW}警告: 未检测到NVIDIA GPU或驱动。Refact.ai可能无法正常工作。${NC}"

    if is_wsl; then
        echo -e "${YELLOW}检测到WSL2环境，需要特殊的GPU配置。${NC}"
    fi

    echo -e "${YELLOW}是否需要查看GPU驱动安装指南? (y/n)${NC}"
    read -p "" install_guide
    if [[ "$install_guide" == "y" || "$install_guide" == "Y" ]]; then
        show_gpu_install_guide
    fi
fi

# 当前目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

# 函数: 显示GPU驱动安装指南
show_gpu_install_guide() {
    echo -e "${GREEN}====================================${NC}"
    echo -e "${GREEN}      NVIDIA GPU 驱动安装指南      ${NC}"
    echo -e "${GREEN}====================================${NC}"
    echo ""
    echo -e "${YELLOW}根据您的操作系统，请按照以下步骤安装NVIDIA GPU驱动:${NC}"
    echo ""
    echo -e "${GREEN}Ubuntu/Debian系统:${NC}"
    echo "1. 添加NVIDIA软件源:"
    echo "   sudo add-apt-repository ppa:graphics-drivers/ppa"
    echo "   sudo apt update"
    echo ""
    echo "2. 安装推荐的驱动:"
    echo "   sudo apt install nvidia-driver-535 nvidia-utils-535"
    echo "   (或使用更新的版本号)"
    echo ""
    echo "3. 安装NVIDIA容器工具包:"
    echo "   distribution=\$(. /etc/os-release;echo \$ID\$VERSION_ID)"
    echo "   curl -s -L https://nvidia.github.io/nvidia-docker/\$distribution/nvidia-docker.list | \\"
    echo "   sudo tee /etc/apt/sources.list.d/nvidia-docker.list"
    echo "   sudo apt update"
    echo "   sudo apt install -y nvidia-container-toolkit"
    echo ""
    echo "4. 重启Docker服务:"
    echo "   sudo systemctl restart docker"
    echo ""
    echo -e "${GREEN}CentOS/RHEL系统:${NC}"
    echo "1. 添加EPEL仓库:"
    echo "   sudo yum install -y epel-release"
    echo "   sudo yum update -y"
    echo ""
    echo "2. 安装NVIDIA驱动:"
    echo "   sudo yum install -y gcc kernel-devel kernel-headers"
    echo "   sudo yum install -y kmod-nvidia-latest-dkms nvidia-driver-latest-dkms"
    echo ""
    echo "3. 安装NVIDIA容器工具包:"
    echo "   distribution=\$(. /etc/os-release;echo \$ID\$VERSION_ID)"
    echo "   curl -s -L https://nvidia.github.io/nvidia-docker/\$distribution/nvidia-docker.repo | \\"
    echo "   sudo tee /etc/yum.repos.d/nvidia-docker.repo"
    echo "   sudo yum install -y nvidia-container-toolkit"
    echo ""
    echo "4. 重启Docker服务:"
    echo "   sudo systemctl restart docker"
    echo ""
    echo -e "${GREEN}WSL2 (Windows Subsystem for Linux):${NC}"
    echo "1. 在Windows主机上安装最新的NVIDIA驱动"
    echo "   访问: https://www.nvidia.com/Download/index.aspx"
    echo ""
    echo "2. 确保Windows主机上安装了CUDA工具包"
    echo "   访问: https://developer.nvidia.com/cuda-downloads"
    echo ""
    echo "3. 启用WSL2的NVIDIA GPU支持"
    echo "   在Windows PowerShell中运行:"
    echo "   New-ItemProperty -Path \"HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WSLX\" -Name \"EnableGPU\" -Value 1 -PropertyType DWORD -Force"
    echo ""
    echo "4. 在WSL2中安装NVIDIA容器工具包:"
    echo "   distribution=\$(. /etc/os-release;echo \$ID\$VERSION_ID)"
    echo "   curl -s -L https://nvidia.github.io/nvidia-docker/\$distribution/nvidia-docker.list | \\"
    echo "   sudo tee /etc/apt/sources.list.d/nvidia-docker.list"
    echo "   sudo apt update"
    echo "   sudo apt install -y nvidia-container-toolkit"
    echo ""
    echo "5. 在WSL2中配置Docker:"
    echo "   如果使用Docker Desktop for Windows:"
    echo "   - 打开Docker Desktop设置"
    echo "   - 启用'Use the WSL 2 based engine'"
    echo "   - 启用'Enable NVIDIA GPU support for WSL 2'"
    echo ""
    echo "   如果使用WSL2原生Docker:"
    echo "   sudo service docker start"
    echo ""
    echo -e "${YELLOW}安装完成后，请运行 'nvidia-smi' 命令验证驱动是否正确安装${NC}"
    echo -e "${YELLOW}然后重新运行此脚本启动Refact.ai服务${NC}"
    echo ""
    read -p "按Enter键继续..."
}

# 函数: 检查Docker服务状态
check_docker_service() {
    if is_wsl; then
        # 在WSL2中检查Docker服务状态
        if ! docker info &>/dev/null; then
            echo -e "${YELLOW}WSL2中Docker服务可能未启动。${NC}"
            echo -e "${YELLOW}如果使用Docker Desktop for Windows，请确保它已启动。${NC}"
            echo -e "${YELLOW}如果使用WSL2原生Docker，请运行: sudo service docker start${NC}"

            read -p "是否尝试启动Docker服务? (y/n) " start_docker
            if [[ "$start_docker" == "y" || "$start_docker" == "Y" ]]; then
                sudo service docker start
                sleep 2
                if ! docker info &>/dev/null; then
                    echo -e "${RED}启动Docker服务失败。请手动检查Docker状态。${NC}"
                    return 1
                fi
            else
                return 1
            fi
        fi
    fi
    return 0
}

# 函数: 启动服务
start_service() {
    # 首先检查Docker服务
    check_docker_service || return 1

    echo -e "${GREEN}正在启动Refact.ai服务...${NC}"
    docker compose up -d

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Refact.ai服务已成功启动!${NC}"
        echo -e "${GREEN}可通过 http://localhost:8008 访问Web界面${NC}"

        if is_wsl; then
            echo -e "${YELLOW}在WSL2环境中，您也可以使用Windows主机的IP地址访问:${NC}"
            echo -e "${GREEN}http://$(hostname -I | awk '{print $1}'):8008${NC}"
        fi
    else
        echo -e "${RED}启动服务失败，请检查错误信息。${NC}"
    fi
}

# 函数: 停止服务
stop_service() {
    echo -e "${YELLOW}正在停止Refact.ai服务...${NC}"
    docker compose down

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Refact.ai服务已停止。${NC}"
    else
        echo -e "${RED}停止服务失败，请检查错误信息。${NC}"
    fi
}

# 函数: 重启服务
restart_service() {
    stop_service
    echo ""
    start_service
}

# 函数: 查看日志
view_logs() {
    echo -e "${GREEN}正在查看Refact.ai服务日志...${NC}"
    echo -e "${YELLOW}按 Ctrl+C 退出日志查看${NC}"
    docker compose logs -f
}

# 函数: 更新服务
update_service() {
    echo -e "${GREEN}正在更新Refact.ai服务...${NC}"
    docker compose pull

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}镜像已更新，正在重启服务...${NC}"
        docker compose up -d

        if [ $? -eq 0 ]; then
            echo -e "${GREEN}Refact.ai服务已更新并重启!${NC}"
        else
            echo -e "${RED}重启服务失败，请检查错误信息。${NC}"
        fi
    else
        echo -e "${RED}更新镜像失败，请检查错误信息。${NC}"
    fi
}

# 函数: 显示状态
show_status() {
    echo -e "${GREEN}Refact.ai服务状态:${NC}"
    docker compose ps
}

# 显示菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1) 启动服务"
    echo "2) 停止服务"
    echo "3) 重启服务"
    echo "4) 查看日志"
    echo "5) 更新服务"
    echo "6) 查看状态"
    echo "7) GPU驱动安装指南"
    echo "0) 退出"
    echo ""
    read -p "请输入选项 [0-6]: " choice

    case $choice in
        1) start_service ;;
        2) stop_service ;;
        3) restart_service ;;
        4) view_logs ;;
        5) update_service ;;
        6) show_status ;;
        7) show_gpu_install_guide ;;
        0) exit 0 ;;
        *) echo -e "${RED}无效选项，请重试${NC}" ;;
    esac

    # 返回菜单
    echo ""
    read -p "按Enter键返回菜单..."
    show_menu
}

# 检查命令行参数
if [ $# -eq 0 ]; then
    # 无参数，显示菜单
    show_menu
else
    # 有参数，直接执行对应操作
    case "$1" in
        start) start_service ;;
        stop) stop_service ;;
        restart) restart_service ;;
        logs) view_logs ;;
        update) update_service ;;
        status) show_status ;;
        gpu-guide) show_gpu_install_guide ;;
        *)
            echo -e "${RED}无效的参数: $1${NC}"
            echo "可用参数: start, stop, restart, logs, update, status, gpu-guide"
            exit 1
            ;;
    esac
fi
