# Flutter Gradle 代理修复测试脚本
# 用于测试和验证gradle代理设置是否正确

param(
    [string]$ProxyUrl = "http://127.0.0.1:7890",
    [switch]$TestOnly = $false
)

Write-Host "Flutter Gradle 代理修复测试脚本" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 导入主脚本的函数
$scriptPath = Join-Path $PSScriptRoot "switch_proxy.ps1"
if (Test-Path $scriptPath) {
    . $scriptPath
} else {
    Write-Host "❌ 找不到 switch_proxy.ps1 脚本" -ForegroundColor Red
    exit 1
}

function Test-GradleProxyConfig {
    Write-Host "`n🔍 检查Gradle代理配置..." -ForegroundColor Yellow
    
    # 检查全局gradle.properties
    $gradleUserHome = if ($env:GRADLE_USER_HOME) { $env:GRADLE_USER_HOME } else { "$env:USERPROFILE\.gradle" }
    $gradlePropertiesPath = Join-Path $gradleUserHome "gradle.properties"
    
    Write-Host "全局Gradle配置文件: $gradlePropertiesPath"
    if (Test-Path $gradlePropertiesPath) {
        $content = Get-Content $gradlePropertiesPath
        $proxyLines = $content | Where-Object { $_ -match 'systemProp.*proxy' }
        if ($proxyLines) {
            Write-Host "✅ 找到代理配置:" -ForegroundColor Green
            $proxyLines | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
        } else {
            Write-Host "⚠️  未找到代理配置" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  配置文件不存在" -ForegroundColor Yellow
    }
    
    # 检查Flutter项目的android/gradle.properties
    if (Test-Path "android/gradle.properties") {
        Write-Host "`nFlutter Android项目配置文件: android/gradle.properties"
        $androidContent = Get-Content "android/gradle.properties"
        $androidProxyLines = $androidContent | Where-Object { $_ -match 'systemProp.*proxy' }
        if ($androidProxyLines) {
            Write-Host "✅ 找到代理配置:" -ForegroundColor Green
            $androidProxyLines | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
        } else {
            Write-Host "⚠️  未找到代理配置" -ForegroundColor Yellow
        }
    }
    
    # 检查环境变量
    Write-Host "`n环境变量检查:"
    if ($env:GRADLE_OPTS) {
        Write-Host "⚠️  GRADLE_OPTS: $env:GRADLE_OPTS" -ForegroundColor Yellow
        Write-Host "   建议清除此环境变量" -ForegroundColor Yellow
    } else {
        Write-Host "✅ GRADLE_OPTS: 未设置（推荐）" -ForegroundColor Green
    }
}

function Test-FlutterProject {
    Write-Host "`n🔍 检查Flutter项目结构..." -ForegroundColor Yellow
    
    if (Test-Path "pubspec.yaml") {
        Write-Host "✅ 检测到Flutter项目" -ForegroundColor Green
        
        if (Test-Path "android") {
            Write-Host "✅ 找到android目录" -ForegroundColor Green
            
            if ((Test-Path "android/gradlew.bat") -or (Test-Path "android/gradlew")) {
                Write-Host "✅ 找到Gradle Wrapper" -ForegroundColor Green
            } else {
                Write-Host "⚠️  未找到Gradle Wrapper" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ 未找到android目录" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  当前目录不是Flutter项目根目录" -ForegroundColor Yellow
    }
}

function Apply-Fix {
    Write-Host "`n🔧 应用Gradle代理修复..." -ForegroundColor Yellow
    
    # 清除可能存在的环境变量
    Remove-Item Env:GRADLE_OPTS -ErrorAction SilentlyContinue
    Write-Host "✅ 已清除GRADLE_OPTS环境变量" -ForegroundColor Green
    
    # 应用修复
    Fix-FlutterGradleProxy -proxyUrl $ProxyUrl -enable $true
    
    Write-Host "`n✨ 修复完成！建议执行以下命令：" -ForegroundColor Green
    Write-Host "flutter clean" -ForegroundColor White
    Write-Host "cd android && ./gradlew clean && cd .." -ForegroundColor White
    Write-Host "flutter run" -ForegroundColor White
}

# 主执行逻辑
Test-FlutterProject
Test-GradleProxyConfig

if (-not $TestOnly) {
    $response = Read-Host "`n是否要应用修复？(y/N)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Apply-Fix
    } else {
        Write-Host "跳过修复应用" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n仅测试模式，跳过修复应用" -ForegroundColor Yellow
}

Write-Host "`n测试完成！" -ForegroundColor Green
