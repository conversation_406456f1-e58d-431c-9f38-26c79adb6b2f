services:
  openhands:
    image: docker.all-hands.dev/all-hands-ai/openhands:0.15
    container_name: openhands
    environment:
      - SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.15-nikolaik
      - SANDBOX_USER_ID=${UID}
      - WORKSPACE_MOUNT_PATH=${OPENHANDS_WORKSPACE_BASE}
      - LOG_ALL_EVENTS=true
    volumes:
      - ${OPENHANDS_WORKSPACE_BASE}:/opt/workspace_base
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "13000:3000"
    extra_hosts:
      # 将 Docker 容器中的 "host.docker.internal" 映射到宿主机的网关地址，以便容器可以访问宿主机的服务。
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
