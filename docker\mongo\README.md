# MongoDB Docker 环境

这是一个预配置的 MongoDB Docker 环境，包含 MongoDB 服务器和可选的 MongoDB Express Web 管理界面。

## 功能特点

- 使用官方 MongoDB 最新镜像
- 数据持久化存储
- 安全的认证配置
- 健康检查
- 可选的 Web 管理界面 (MongoDB Express)
- 初始化脚本自动创建数据库和用户

## 快速开始

1. 复制环境变量示例文件并配置：
   ```bash
   cp .env.example .env
   ```
   然后编辑 `.env` 文件，设置您的配置。

2. 构建并启动容器：
   ```bash
   docker-compose up -d
   ```

3. 访问服务：
   - MongoDB: `mongodb://<username>:<password>@localhost:27017`
   - MongoDB Express (Web 界面): http://localhost:8081

## 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| MONGO_INITDB_ROOT_USERNAME | root | MongoDB root 用户名 |
| MONGO_INITDB_ROOT_PASSWORD | example | MongoDB root 密码 |
| MONGO_INITDB_DATABASE | admin | 初始数据库 |
| TZ | Asia/Shanghai | 时区设置 |

## 数据持久化

MongoDB 数据存储在名为 `mongodb_data` 的 Docker 卷中，确保数据在容器重启后不会丢失。

## 备份与恢复

### 备份数据库
```bash
docker exec mongodb sh -c 'mongodump --archive' > mongodb_backup_$(date +%Y%m%d).archive
```

### 恢复数据库
```bash
docker exec -i mongodb sh -c 'mongorestore --archive' < backup_file.archive
```

## 维护命令

- 查看日志: `docker-compose logs -f`
- 停止容器: `docker-compose down`
- 停止并删除所有数据: `docker-compose down -v`

## 安全提示

1. 不要在生产环境中使用默认凭据
2. 确保 .env 文件不被提交到版本控制系统
3. 定期备份重要数据

## 许可证

MIT
