version: '3.7'
services: 
    xx: 
        image: xx
        container_name: xx
        hostname: xx
        command: xx
        deploy:
            resources:
                limits:
                    memory: 1024M
        ports:
            - "outter_ip:inner_ip"
        volumes:
            - ~/docker-compose/data/xx:xx
        networks:
            - net_v1
        environment:
          xx: xx
          TZ: Asia/Shanghai
        restart: unless-stopped
        logging:
          driver: json-file
          options: 
            max-size: "100M"
            max-file: "10"
networks:
    net_v1:
        external: true
    
