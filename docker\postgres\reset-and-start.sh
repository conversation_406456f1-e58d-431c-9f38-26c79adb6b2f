#!/bin/bash
#
# 该脚本用于完全重置并重新启动 PostgreSQL 服务。
# 它会删除所有现有数据，请谨慎使用！
#

# 如果任何命令失败，则立即退出脚本
set -e

# 切换到脚本所在的目录，以确保 docker-compose.yml 文件可以被找到
cd "$(dirname "$0")"

# 1. 停止并删除现有容器
echo "INFO: Stopping and removing existing containers..."
docker compose down

# 2. 删除旧的数据卷
echo "INFO: Removing old data volume..."
docker volume rm postgres_data || true

# 3. 启动服务
echo "INFO: Starting PostgreSQL container in detached mode..."
docker compose up -d

# 4. 等待并显示日志
echo "INFO: Waiting for container to initialize..."
sleep 15 # 等待15秒，给数据库足够的时间来完成初始化

echo "INFO: Displaying container logs:"
docker compose logs --tail=50 # 显示最后50行日志

echo "
SUCCESS: PostgreSQL reset and startup process completed."