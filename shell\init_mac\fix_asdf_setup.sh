#!/usr/bin/env bash

# ==============================================================================
# macOS asdf环境修复脚本
# ==============================================================================

# --- 辅助函数定义 ---
info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 获取Homebrew路径
get_brew_path() {
    if [[ "$(uname -m)" == "arm64" ]]; then
        echo "/opt/homebrew"
    else
        echo "/usr/local"
    fi
}

fix_asdf_setup() {
    info "--- 开始修复asdf环境配置 ---"

    BREW_PATH=$(get_brew_path)

    # 1. 初始化asdf环境
    info "初始化asdf环境..."
    if [ -f "$BREW_PATH/opt/asdf/libexec/asdf.sh" ]; then
        source "$BREW_PATH/opt/asdf/libexec/asdf.sh"
        success "asdf环境已初始化"
    else
        error "asdf未正确安装"
        return 1
    fi

    # 2. 设置已安装语言的全局版本
    info "设置已安装语言的全局版本..."

    # Java
    if asdf list java 2>/dev/null | grep -q "temurin"; then
        java_version=$(asdf list java | grep "temurin" | head -1 | xargs)
        info "设置Java全局版本: $java_version"
        asdf set java "$java_version"
        success "Java全局版本已设置"
    fi

    # Node.js
    if asdf list nodejs 2>/dev/null | grep -q "lts"; then
        nodejs_version=$(asdf list nodejs | head -1 | xargs)
        info "设置Node.js全局版本: $nodejs_version"
        asdf set nodejs "$nodejs_version"
        success "Node.js全局版本已设置"
    fi

    # Go
    if asdf list golang 2>/dev/null | grep -q "1.24"; then
        go_version=$(asdf list golang | head -1 | xargs)
        info "设置Go全局版本: $go_version"
        asdf set golang "$go_version"
        success "Go全局版本已设置"
    fi

    # Maven
    if asdf list maven 2>/dev/null | grep -q "."; then
        maven_version=$(asdf list maven | head -1 | xargs)
        info "设置Maven全局版本: $maven_version"
        asdf set maven "$maven_version"
        success "Maven全局版本已设置"
    fi

    # 3. 安装Xcode Command Line Tools（如果需要）
    info "检查Xcode Command Line Tools..."
    if ! xcode-select -p &>/dev/null; then
        warn "Xcode Command Line Tools未安装，这是编译Python等语言所必需的"
        info "请运行以下命令安装: xcode-select --install"
        info "安装完成后，可以重新运行Python安装: asdf install python latest"
    else
        success "Xcode Command Line Tools已安装"

        # 尝试重新安装Python
        info "尝试重新安装Python..."
        if ! asdf list python 2>/dev/null | grep -q "."; then
            info "正在安装Python latest..."
            if asdf install python latest; then
                python_version=$(asdf list python | head -1 | xargs)
                asdf set python "$python_version"
                success "Python安装成功: $python_version"
            else
                warn "Python安装失败，可能需要手动安装依赖"
            fi
        else
            python_version=$(asdf list python | head -1 | xargs)
            asdf set python "$python_version"
            success "Python已安装: $python_version"
        fi
    fi

    # 4. 重新生成shims
    info "重新生成asdf shims..."
    asdf reshim
    success "shims已重新生成"

    # 5. 显示当前状态
    info "--- 当前安装状态 ---"
    echo ""
    asdf current
    echo ""

    success "asdf环境修复完成！"
}

# 主执行逻辑
main() {
    echo "========================================"
    echo "🔧 macOS asdf环境修复脚本"
    echo "========================================"
    echo ""

    # 检查是否在macOS上运行
    if [[ "$(uname)" != "Darwin" ]]; then
        error "此脚本仅适用于macOS系统"
        exit 1
    fi

    # 执行修复
    fix_asdf_setup

    echo ""
    echo "========================================"
    echo "✅ 修复完成！"
    echo "========================================"
    echo ""
    echo "下一步操作："
    echo "1. 重新启动终端或运行: source ~/.zshrc"
    echo "2. 验证安装: asdf current"
    echo "3. 如果Python未安装，请先运行: xcode-select --install"
    echo "   然后运行: asdf install python latest && asdf set python latest"
    echo ""
}

# 如果脚本被直接执行，则运行main函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
