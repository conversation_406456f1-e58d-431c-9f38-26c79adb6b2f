# NOTE:v2.5.0后注册中心的相关配置已移除，使用注册中心请通过后台的注册配置（注册中心 > 注册配置）功能进行设置

spring:
  datasource:
    # 数据库连接
    url: *******************************************************************************************************************************************************************************************************************************************
    # 数据库用户名
    username: root
    # 数据库密码
    password: 123456
  mail:
    # 邮箱host
    host: smtp.163.com
    # 邮箱用户名
    username: <EMAIL>
    # 邮箱密码
    password: SBIKDJOSIPKXCXVD
    # 邮箱port
    port: 465
    properties:
      # 邮箱发送人地址
      from: <EMAIL>
      mail:
        smtp:
          # 使用鉴权
          auth: true
          ssl:
            # 不开启SSL
            enable: false
          # 超时时间
          timeout: 30000
  redis:
    # redis配置，需要和fizz-gateway保持一致（包括database、host、password、port）
    database: 2
    host: redis
    password: 123456
    port: 6379
    ssl: false
  servlet:
    multipart:
      # 上传文件临时目录
      location: /data/webapps/fizz-manager-professional/logs
params:
  # fizz-gateway(网关服务)地址列表，当不使用注册中心时可配置该参数，网关缓存功能会读取该列表显示，多个实例用逗号分隔
  # gateway-urls: http://localhost:8600,http://localhost:8600,http://localhost:8600
  gateway-urls: http://localhost:8600
  # fizz-dedicated-line（网关专线服务）地址列表，当不使用注册中心时可配置该参数，配对文档测试请求将转发到该地址，多个实例用逗号分隔
  # gateway-urls: http://localhost:8601,http://localhost:8601,http://localhost:8601
  gateway-dedicated-line-urls: http://localhost:8601
  aggregate:
    test:
      # fizz-gateway地址，聚合接口测试页面使用该地址发送请求
      gateway-url: http://localhost:8600
  # fizz-manager地址
  fizz-manager-url: http://localhost:8000/
  # 网关日志保存天数，网关保存日志量影响页面查询速度，可根据实际情况调整该参数
  fizz-log-save-days: 2
  # 开放文档默认访问地址
  open-doc-default-url: ${params.fizz-manager-url}module/open-document/index.html

gateway:
  # 自定义网关前缀, 默认值为/proxy
  prefix: 
