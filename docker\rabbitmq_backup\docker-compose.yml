services:
  rabbitmq:
    image: heidiks/rabbitmq-delayed-message-exchange:3.13.3-management
    container_name: rabbitmq
    hostname: rabbitmq
    environment:
      TZ: Asia/Shanghai
      LANG: en_US.UTF-8
    ports:
      - "15672:15672"
      - "5672:5672"
    volumes:
      - ./conf:/etc/rabbitmq
      - ./data:/var/lib/rabbitmq
      - ./conf/init.sh:/docker-entrypoint.d/init.sh
    networks:
      - net_v1
    restart: unless-stopped
    sysctls:
      - net.core.somaxconn=1024
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 40000
networks:
  net_v1:
    external: true
