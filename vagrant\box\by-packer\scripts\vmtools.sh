#!/bin/bash

echo -e "\n\n\033[1;36m=========== 安装vmtools ===============\033[0m"

if [[ $PACKER_BUILDER_TYPE =~ virtualbox ]]; then
  # 如果不存在从网络下载并重命名成VBoxGuestAdditions.iso
  if [ ! -f /tmp/VBoxGuestAdditions.iso ]; then
    curl -L -o /tmp/VBoxGuestAdditions.iso https://mirrors.tuna.tsinghua.edu.cn/virtualbox/7.0.20/VBoxGuestAdditions_7.0.20.iso
  fi

  echo "Kernel information:"
  uname -r

  echo "Kernel-devel version:"
  rpm -q kernel-devel

  echo "Kernel headers version:"
  rpm -q kernel-headers

  mount -o loop /tmp/VBoxGuestAdditions.iso /mnt
  sudo sh /mnt/VBoxLinuxAdditions.run
  umount /mnt
  rm -rf /tmp/vagrant/VBoxGuestAdditions_*.iso
fi

if [[ $PACKER_BUILDER_TYPE =~ vmware ]]; then
  dnf -y install open-vm-tools
fi
