# Copyright VMware, Inc.
# SPDX-License-Identifier: APACHE-2.0

version: '2'
services:
  redis-node-0:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6380:6380
      - 16380:16380
    volumes:
      - redis-cluster_data-0:/bitnami/redis/data
    networks:
      - net_v1
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_PORT_NUMBER=6380'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6380'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16380'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'

  redis-node-1:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6381:6381
      - 16381:16381
    volumes:
      - redis-cluster_data-1:/bitnami/redis/data
    networks:
      - net_v1
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_PORT_NUMBER=6381'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6381'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16381'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'

  redis-node-2:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6382:6382
      - 16382:16382
    volumes:
      - redis-cluster_data-2:/bitnami/redis/data
    networks:
      - net_v1
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_PORT_NUMBER=6382'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6382'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16382'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'

  redis-node-3:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6383:6383
      - 16383:16383
    volumes:
      - redis-cluster_data-3:/bitnami/redis/data
    networks:
      - net_v1
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_PORT_NUMBER=6383'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6383'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16383'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'

  redis-node-4:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6384:6384
      - 16384:16384
    volumes:
      - redis-cluster_data-4:/bitnami/redis/data
    networks:
      - net_v1
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_PORT_NUMBER=6384'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6384'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16384'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'

  redis-node-5:
    image: docker.io/bitnami/redis-cluster:7.2
    ports:
      - 6385:6385
      - 16385:16385
    volumes:
      - redis-cluster_data-5:/bitnami/redis/data
    networks:
      - net_v1
    depends_on:
      - redis-node-0
      - redis-node-1
      - redis-node-2
      - redis-node-3
      - redis-node-4
    environment:
      - 'REDIS_PASSWORD=XKH4lCe3Tr9Tpiil'
      - 'REDISCLI_AUTH=XKH4lCe3Tr9Tpiil'
      - 'REDIS_CLUSTER_REPLICAS=1'
      - 'REDIS_PORT_NUMBER=6385'
      - 'REDIS_CLUSTER_DYNAMIC_IPS=no'
      - 'REDIS_CLUSTER_ANNOUNCE_IP=**************'
      - 'REDIS_CLUSTER_ANNOUNCE_PORT=6385'
      - 'REDIS_CLUSTER_ANNOUNCE_BUS_PORT=16385'
      - 'REDIS_CLUSTER_PREFERRED_ENDPOINT_TYPE=ip'
      - 'REDIS_NODES=**************:6380 **************:6381 **************:6382 **************:6383 **************:6384 **************:6385'
      - 'REDIS_CLUSTER_CREATOR=yes'

networks:
  net_v1:
    external: true
volumes:
  redis-cluster_data-0:
    driver: local
  redis-cluster_data-1:
    driver: local
  redis-cluster_data-2:
    driver: local
  redis-cluster_data-3:
    driver: local
  redis-cluster_data-4:
    driver: local
  redis-cluster_data-5:
    driver: local
