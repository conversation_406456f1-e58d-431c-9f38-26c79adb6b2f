# PostgreSQL 生产级部署方案

本项目提供了一个生产级的 PostgreSQL 数据库部署方案，使用 Docker Compose 进行容器化管理。

## 目录结构

```
postgres/
├── .env.example        # 环境变量示例文件
├── docker-compose.yml  # Docker Compose 配置文件
├── config/             # PostgreSQL 配置文件目录
│   ├── postgresql.conf # PostgreSQL 主配置文件
│   └── pg_hba.conf     # PostgreSQL 访问控制配置
├── init/               # 初始化脚本目录
│   └── 01-init-db.sh   # 数据库初始化脚本
├── backup/             # 备份目录
│   └── backup.sh       # 备份脚本
└── README.md           # 说明文档
```

## 功能特性

- 基于官方 PostgreSQL 15 镜像
- 包含 pgAdmin 4 Web 管理界面
- 自动初始化数据库和用户
- 数据持久化存储
- 健康检查和自动重启
- 优化的性能配置
- 安全的访问控制
- 自动备份方案
- 资源限制和监控

## 快速开始

### 1. 准备环境变量

复制环境变量示例文件并根据需要修改：

```bash
cp .env.example .env
```

### 2. 启动服务

```bash
docker-compose up -d
```

### 3. 验证服务状态

```bash
docker-compose ps
```

### 4. 访问 pgAdmin

打开浏览器访问：`http://localhost:5050`（或您在 .env 中配置的端口）

使用在 .env 文件中设置的 PGADMIN_DEFAULT_EMAIL 和 PGADMIN_DEFAULT_PASSWORD 登录。

## 配置说明

### 环境变量

主要环境变量说明：

- `POSTGRES_USER`: PostgreSQL 超级用户名
- `POSTGRES_PASSWORD`: PostgreSQL 超级用户密码
- `POSTGRES_DB`: 默认数据库名
- `APP_DB`: 应用数据库名
- `APP_USER`: 应用数据库用户名
- `APP_PASSWORD`: 应用数据库用户密码
- `POSTGRES_PORT`: PostgreSQL 端口映射
- `POSTGRES_MEMORY_LIMIT`: PostgreSQL 容器内存限制

### PostgreSQL 配置

`postgresql.conf` 文件包含了针对生产环境优化的配置参数：

- 内存设置已针对中等规模服务器优化
- WAL 日志配置适合生产环境
- 自动清理参数已调整为适合高负载环境
- 查询性能参数已优化

可以根据实际硬件资源和负载情况进行调整。

### 访问控制

`pg_hba.conf` 文件配置了严格的访问控制规则：

- 默认只允许本地和容器网络内的连接
- 使用 scram-sha-256 加密方式进行认证
- 可以根据需要开放特定网络的访问权限

## 备份与恢复

### 手动备份

进入容器执行备份脚本：

```bash
docker-compose exec postgres bash /backup/backup.sh
```

### 自动备份

可以设置 cron 任务定期执行备份：

```bash
# 每天凌晨 2 点执行备份
0 2 * * * docker-compose -f /path/to/docker-compose.yml exec -T postgres bash /backup/backup.sh
```

### 恢复备份

```bash
# 从备份文件恢复
docker-compose exec postgres bash -c "gunzip -c /backup/postgres_backup_YYYYMMDD_HHMMSS.sql.gz | psql -U postgres"
```

## 安全建议

1. 修改所有默认密码，使用强密码
2. 限制 PostgreSQL 和 pgAdmin 的访问范围
3. 定期更新 Docker 镜像
4. 定期备份数据
5. 监控数据库性能和安全日志

## 性能调优

根据服务器硬件资源，您可能需要调整 `postgresql.conf` 中的以下参数：

- `shared_buffers`: 建议为系统内存的 1/4
- `effective_cache_size`: 建议为系统内存的 1/2-3/4
- `work_mem`: 根据复杂查询和并发连接数调整
- `maintenance_work_mem`: 根据数据库大小调整

## 故障排除

### 常见问题

1. 无法连接到数据库
   - 检查端口映射是否正确
   - 确认 pg_hba.conf 中的访问控制规则

2. 性能问题
   - 检查 postgresql.conf 中的内存参数设置
   - 查看数据库日志中的慢查询

3. 容器无法启动
   - 检查 Docker 日志
   - 确认环境变量设置正确

## 许可证

MIT
