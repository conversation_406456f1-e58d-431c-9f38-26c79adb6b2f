## GitLab configuration settings
##! This file is generated during initial installation and **is not** modified
##! during upgrades.
##! Check out the latest version of this file to know about the different
##! settings that can be configured, when they were introduced and why:
##! https://gitlab.com/gitlab-org/omnibus-gitlab/blame/master/files/gitlab-config-template/gitlab.rb.template

##! Locally, the complete template corresponding to the installed version can be found at:
##! /opt/gitlab/etc/gitlab.rb.template

##! You can run `gitlab-ctl diff-config` to compare the contents of the current gitlab.rb with
##! the gitlab.rb.template from the currently running version.

##! You can run `gitlab-ctl show-config` to display the configuration that will be generated by
##! running `gitlab-ctl reconfigure`

##! In general, the values specified here should reflect what the default value of the attribute will be.
##! There are instances where this behavior is not possible or desired. For example, when providing passwords,
##! or connecting to third party services.
##! In those instances, we endeavour to provide an example configuration.

## GitLab URL
##! URL on which Git<PERSON><PERSON> will be reachable.
##! For more details on configuring external_url see:
##! https://docs.gitlab.com/omnibus/settings/configuration.html#configuring-the-external-url-for-gitlab
##!
##! Note: During installation/upgrades, the value of the environment variable
##! EXTERNAL_URL will be used to populate/replace this value.
##! On AWS EC2 instances, we also attempt to fetch the public hostname/IP
##! address from AWS. For more details, see:
##! https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instancedata-data-retrieval.html
# external_url 'GENERATED_EXTERNAL_URL'

## Roles for multi-instance GitLab
##! The default is to have no roles enabled, which results in GitLab running as an all-in-one instance.
##! Options:
##!   redis_sentinel_role redis_master_role redis_replica_role geo_primary_role geo_secondary_role
##!   postgres_role consul_role application_role monitoring_role
##! For more details on each role, see:
##! https://docs.gitlab.com/omnibus/roles/index.html#roles
##!
# roles ['redis_sentinel_role', 'redis_master_role']

## Legend
##! The following notations at the beginning of each line may be used to
##! differentiate between components of this file and to easily select them using
##! a regex.
##! ## Titles, subtitles etc
##! ##! More information - Description, Docs, Links, Issues etc.
##! Configuration settings have a single # followed by a single space at the
##! beginning; Remove them to enable the setting.

##! **Configuration settings below are optional.**


################################################################################
################################################################################
##                Configuration Settings for GitLab CE and EE                 ##
################################################################################
################################################################################

################################################################################
## gitlab.yml configuration
##! Docs: https://gitlab.com/gitlab-org/omnibus-gitlab/blob/master/doc/settings/gitlab.yml.md
################################################################################
# gitlab_rails['gitlab_ssh_host'] = 'ssh.ibootz.com'
# gitlab_rails['gitlab_ssh_user'] = 'root'
# gitlab_rails['time_zone'] = 'UTC'

### Rails asset / CDN host
###! Defines a url for a host/cdn to use for the Rails assets
###! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#set-a-content-delivery-network-url
# gitlab_rails['cdn_host'] = 'https://mycdnsubdomain.fictional-cdn.com'

### Request duration
###! Tells the rails application how long it has to complete a request
###! This value needs to be lower than the worker timeout set in puma.
###! By default, we'll allow 95% of the the worker timeout
# gitlab_rails['max_request_duration_seconds'] = 57

### GitLab email server settings
###! Docs: https://docs.gitlab.com/omnibus/settings/smtp.html
###! **Use smtp instead of sendmail/postfix.**

# gitlab_rails['smtp_enable'] = true
# gitlab_rails['smtp_address'] = "smtp.server"
# gitlab_rails['smtp_port'] = 465
# gitlab_rails['smtp_user_name'] = "smtp user"
# gitlab_rails['smtp_password'] = "smtp password"
# gitlab_rails['smtp_domain'] = "example.com"
# gitlab_rails['smtp_authentication'] = "login"
# gitlab_rails['smtp_enable_starttls_auto'] = true
# gitlab_rails['smtp_tls'] = false
# gitlab_rails['smtp_pool'] = false

###! **Can be: 'none', 'peer', 'client_once', 'fail_if_no_peer_cert'**
###! Docs: http://api.rubyonrails.org/classes/ActionMailer/Base.html
# gitlab_rails['smtp_openssl_verify_mode'] = 'none'

# gitlab_rails['smtp_ca_path'] = "/etc/ssl/certs"
# gitlab_rails['smtp_ca_file'] = "/etc/ssl/certs/ca-certificates.crt"

### Email Settings

# gitlab_rails['gitlab_email_enabled'] = true

##! If your SMTP server does not like the default 'From: <EMAIL>'
##! can change the 'From' with this setting.
# gitlab_rails['gitlab_email_from'] = '<EMAIL>'
# gitlab_rails['gitlab_email_display_name'] = 'Example'
# gitlab_rails['gitlab_email_reply_to'] = '<EMAIL>'
# gitlab_rails['gitlab_email_subject_suffix'] = ''
# gitlab_rails['gitlab_email_smime_enabled'] = false
# gitlab_rails['gitlab_email_smime_key_file'] = '/etc/gitlab/ssl/gitlab_smime.key'
# gitlab_rails['gitlab_email_smime_cert_file'] = '/etc/gitlab/ssl/gitlab_smime.crt'
# gitlab_rails['gitlab_email_smime_ca_certs_file'] = '/etc/gitlab/ssl/gitlab_smime_cas.crt'

### GitLab user privileges
# gitlab_rails['gitlab_default_can_create_group'] = true
# gitlab_rails['gitlab_username_changing_enabled'] = true

### Default Theme
### Available values:
##! `1`  for Indigo
##! `2`  for Dark
##! `3`  for Light
##! `4`  for Blue
##! `5`  for Green
##! `6`  for Light Indigo
##! `7`  for Light Blue
##! `8`  for Light Green
##! `9`  for Red
##! `10` for Light Red
# gitlab_rails['gitlab_default_theme'] = 2

### Default project feature settings
# gitlab_rails['gitlab_default_projects_features_issues'] = true
# gitlab_rails['gitlab_default_projects_features_merge_requests'] = true
# gitlab_rails['gitlab_default_projects_features_wiki'] = true
# gitlab_rails['gitlab_default_projects_features_snippets'] = true
# gitlab_rails['gitlab_default_projects_features_builds'] = true
# gitlab_rails['gitlab_default_projects_features_container_registry'] = true

### Automatic issue closing
###! See https://docs.gitlab.com/ee/customization/issue_closing.html for more
###! information about this pattern.
# gitlab_rails['gitlab_issue_closing_pattern'] = "\b((?:[Cc]los(?:e[sd]?|ing)|\b[Ff]ix(?:e[sd]|ing)?|\b[Rr]esolv(?:e[sd]?|ing)|\b[Ii]mplement(?:s|ed|ing)?)(:?) +(?:(?:issues? +)?%{issue_ref}(?:(?:, *| +and +)?)|([A-Z][A-Z0-9_]+-\d+))+)"

### Download location
###! When a user clicks e.g. 'Download zip' on a project, a temporary zip file
###! is created in the following directory.
###! Should not be the same path, or a sub directory of any of the `git_data_dirs`
# gitlab_rails['gitlab_repository_downloads_path'] = 'tmp/repositories'

### Gravatar Settings
# gitlab_rails['gravatar_plain_url'] = 'http://www.gravatar.com/avatar/%{hash}?s=%{size}&d=identicon'
# gitlab_rails['gravatar_ssl_url'] = 'https://secure.gravatar.com/avatar/%{hash}?s=%{size}&d=identicon'

### Auxiliary jobs
###! Periodically executed jobs, to self-heal Gitlab, do external
###! synchronizations, etc.
###! Docs: https://github.com/ondrejbartas/sidekiq-cron#adding-cron-job
###!       https://docs.gitlab.com/ee/ci/yaml/index.html#artifactsexpire_in
# gitlab_rails['stuck_ci_jobs_worker_cron'] = "0 0 * * *"
# gitlab_rails['expire_build_artifacts_worker_cron'] = "*/7 * * * *"
# gitlab_rails['environments_auto_stop_cron_worker_cron'] = "24 * * * *"
# gitlab_rails['pipeline_schedule_worker_cron'] = "19 * * * *"
# gitlab_rails['ci_archive_traces_cron_worker_cron'] = "17 * * * *"
# gitlab_rails['repository_check_worker_cron'] = "20 * * * *"
# gitlab_rails['admin_email_worker_cron'] = "0 0 * * 0"
# gitlab_rails['personal_access_tokens_expiring_worker_cron'] = "0 1 * * *"
# gitlab_rails['personal_access_tokens_expired_notification_worker_cron'] = "0 2 * * *"
# gitlab_rails['repository_archive_cache_worker_cron'] = "0 * * * *"
# gitlab_rails['pages_domain_verification_cron_worker'] = "*/15 * * * *"
# gitlab_rails['pages_domain_ssl_renewal_cron_worker'] = "*/10 * * * *"
# gitlab_rails['pages_domain_removal_cron_worker'] = "47 0 * * *"
# gitlab_rails['remove_unaccepted_member_invites_cron_worker'] = "10 15 * * *"
# gitlab_rails['schedule_migrate_external_diffs_worker_cron'] = "15 * * * *"
# gitlab_rails['ci_platform_metrics_update_cron_worker'] = '47 9 * * *'
# gitlab_rails['analytics_usage_trends_count_job_trigger_worker_cron'] = "50 23 */1 * *"
# gitlab_rails['member_invitation_reminder_emails_worker_cron'] = "0 0 * * *"
# gitlab_rails['user_status_cleanup_batch_worker_cron'] = "* * * * *"
# gitlab_rails['namespaces_in_product_marketing_emails_worker_cron'] = "0 9 * * *"
# gitlab_rails['ssh_keys_expired_notification_worker_cron'] = "0 2 * * *"
# gitlab_rails['ssh_keys_expiring_soon_notification_worker_cron'] = "0 1 * * *"
# gitlab_rails['loose_foreign_keys_cleanup_worker_cron'] = "*/5 * * * *"
# gitlab_rails['ci_runner_versions_reconciliation_worker_cron'] = "20 * * * *"

### Webhook Settings
###! Number of seconds to wait for HTTP response after sending webhook HTTP POST
###! request (default: 10)
# gitlab_rails['webhook_timeout'] = 10

### GraphQL Settings
###! Tells the rails application how long it has to complete a GraphQL request.
###! We suggest this value to be higher than the database timeout value
###! and lower than the worker timeout set in puma. (default: 30)
# gitlab_rails['graphql_timeout'] = 30

### Trusted proxies
###! Customize if you have GitLab behind a reverse proxy which is running on a
###! different machine.
###! **Add the IP address for your reverse proxy to the list, otherwise users
###!   will appear signed in from that address.**
# gitlab_rails['trusted_proxies'] = []

### Content Security Policy
####! Customize if you want to enable the Content-Security-Policy header, which
####! can help thwart JavaScript cross-site scripting (XSS) attacks.
####! See: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP
# gitlab_rails['content_security_policy'] = {
#  'enabled' => false,
#  'report_only' => false,
#  # Each directive is a String (e.g. "'self'").
#  'directives' => {
#    'base_uri' => nil,
#    'child_src' => nil,
#    'connect_src' => nil,
#    'default_src' => nil,
#    'font_src' => nil,
#    'form_action' => nil,
#    'frame_ancestors' => nil,
#    'frame_src' => nil,
#    'img_src' => nil,
#    'manifest_src' => nil,
#    'media_src' => nil,
#    'object_src' => nil,
#    'script_src' => nil,
#    'style_src' => nil,
#    'worker_src' => nil,
#    'report_uri' => nil,
#  }
# }

### Allowed hosts
###! Customize the `host` headers that should be catered by the Rails
###! application. By default, everything is allowed.
# gitlab_rails['allowed_hosts'] = []

### Monitoring settings
###! IP whitelist controlling access to monitoring endpoints
# gitlab_rails['monitoring_whitelist'] = ['*********/8', '::1/128']

### Shutdown settings
###! Defines an interval to block healthcheck,
###! but continue accepting application requests.
# gitlab_rails['shutdown_blackout_seconds'] = 10

### Reply by email
###! Allow users to comment on issues and merge requests by replying to
###! notification emails.
###! Docs: https://docs.gitlab.com/ee/administration/reply_by_email.html
# gitlab_rails['incoming_email_enabled'] = true

#### Incoming Email Address
####! The email address including the `%{key}` placeholder that will be replaced
####! to reference the item being replied to.
####! **The placeholder can be omitted but if present, it must appear in the
####!   "user" part of the address (before the `@`).**
# gitlab_rails['incoming_email_address'] = "gitlab-incoming+%{key}@gmail.com"

#### Email account username
####! **With third party providers, this is usually the full email address.**
####! **With self-hosted email servers, this is usually the user part of the
####!   email address.**
# gitlab_rails['incoming_email_email'] = "<EMAIL>"

#### Email account password
# gitlab_rails['incoming_email_password'] = "[REDACTED]"

#### IMAP Settings
# gitlab_rails['incoming_email_host'] = "imap.gmail.com"
# gitlab_rails['incoming_email_port'] = 993
# gitlab_rails['incoming_email_ssl'] = true
# gitlab_rails['incoming_email_start_tls'] = false

#### Incoming Mailbox Settings (via `mail_room`)
####! The mailbox where incoming mail will end up. Usually "inbox".
# gitlab_rails['incoming_email_mailbox_name'] = "inbox"
####! The IDLE command timeout.
# gitlab_rails['incoming_email_idle_timeout'] = 60
####! The file name for internal `mail_room` JSON logfile
# gitlab_rails['incoming_email_log_file'] = "/var/log/gitlab/mailroom/mail_room_json.log"
####! Permanently remove messages from the mailbox when they are marked as deleted after delivery
# gitlab_rails['incoming_email_expunge_deleted'] = false

#### Inbox options (for Microsoft Graph)
# gitlab_rails['incoming_email_inbox_method'] = 'microsoft_graph'
# gitlab_rails['incoming_email_inbox_options'] = {
#    'tenant_id': 'YOUR-TENANT-ID',
#    'client_id': 'YOUR-CLIENT-ID',
#    'client_secret': 'YOUR-CLIENT-SECRET',
#    'poll_interval': 60  # Optional
# }

#### How incoming emails are delivered to Rails process. Accept either sidekiq
#### or webhook. The default config is webhook.
# gitlab_rails['incoming_email_delivery_method'] = "webhook"

#### Token to authenticate webhook requests. The token must be exactly 32 bytes,
#### encoded with base64
# gitlab_rails['incoming_email_auth_token'] = nil

####! The format of mail_room crash logs
# mailroom['exit_log_format'] = "plain"

### Consolidated (simplified) object storage configuration
###! This uses a single credential for object storage with multiple buckets.
###! It also enables Workhorse to upload files directly with its own S3 client
###! instead of using pre-signed URLs.
###!
###! This configuration will only take effect if the object_store
###! sections are not defined within the types. For example, enabling
###! gitlab_rails['artifacts_object_store_enabled'] or
###! gitlab_rails['lfs_object_store_enabled'] will prevent the
###! consolidated settings from being used.
###!
###! Be sure to use different buckets for each type of object.
###! Docs: https://docs.gitlab.com/ee/administration/object_storage.html
# gitlab_rails['object_store']['enabled'] = false
# gitlab_rails['object_store']['connection'] = {}
# gitlab_rails['object_store']['storage_options'] = {}
# gitlab_rails['object_store']['proxy_download'] = false
# gitlab_rails['object_store']['objects']['artifacts']['bucket'] = nil
# gitlab_rails['object_store']['objects']['external_diffs']['bucket'] = nil
# gitlab_rails['object_store']['objects']['lfs']['bucket'] = nil
# gitlab_rails['object_store']['objects']['uploads']['bucket'] = nil
# gitlab_rails['object_store']['objects']['packages']['bucket'] = nil
# gitlab_rails['object_store']['objects']['dependency_proxy']['bucket'] = nil
# gitlab_rails['object_store']['objects']['terraform_state']['bucket'] = nil
# gitlab_rails['object_store']['objects']['ci_secure_files']['bucket'] = nil

### Job Artifacts
# gitlab_rails['artifacts_enabled'] = true
# gitlab_rails['artifacts_path'] = "/var/opt/gitlab/gitlab-rails/shared/artifacts"
####! Job artifacts Object Store
####! Docs: https://docs.gitlab.com/ee/administration/job_artifacts.html#using-object-storage
# gitlab_rails['artifacts_object_store_enabled'] = false
# gitlab_rails['artifacts_object_store_proxy_download'] = false
# gitlab_rails['artifacts_object_store_remote_directory'] = "artifacts"
# gitlab_rails['artifacts_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'host' => 's3.amazonaws.com',
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### External merge request diffs
# gitlab_rails['external_diffs_enabled'] = false
# gitlab_rails['external_diffs_when'] = nil
# gitlab_rails['external_diffs_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/external-diffs"
# gitlab_rails['external_diffs_object_store_enabled'] = false
# gitlab_rails['external_diffs_object_store_proxy_download'] = false
# gitlab_rails['external_diffs_object_store_remote_directory'] = "external-diffs"
# gitlab_rails['external_diffs_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'host' => 's3.amazonaws.com',
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### Git LFS
# gitlab_rails['lfs_enabled'] = true
# gitlab_rails['lfs_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/lfs-objects"
# gitlab_rails['lfs_object_store_enabled'] = false
# gitlab_rails['lfs_object_store_proxy_download'] = false
# gitlab_rails['lfs_object_store_remote_directory'] = "lfs-objects"
# gitlab_rails['lfs_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'host' => 's3.amazonaws.com',
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### GitLab uploads
###! Docs: https://docs.gitlab.com/ee/administration/uploads.html
# gitlab_rails['uploads_directory'] = "/var/opt/gitlab/gitlab-rails/uploads"
# gitlab_rails['uploads_storage_path'] = "/opt/gitlab/embedded/service/gitlab-rails/public"
# gitlab_rails['uploads_base_dir'] = "uploads/-/system"
# gitlab_rails['uploads_object_store_enabled'] = false
# gitlab_rails['uploads_object_store_proxy_download'] = false
# gitlab_rails['uploads_object_store_remote_directory'] = "uploads"
# gitlab_rails['uploads_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### Terraform state
###! Docs: https://docs.gitlab.com/ee/administration/terraform_state
# gitlab_rails['terraform_state_enabled'] = true
# gitlab_rails['terraform_state_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/terraform_state"
# gitlab_rails['terraform_state_object_store_enabled'] = false
# gitlab_rails['terraform_state_object_store_remote_directory'] = "terraform"
# gitlab_rails['terraform_state_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### CI Secure Files
# gitlab_rails['ci_secure_files_enabled'] = false
# gitlab_rails['ci_secure_files_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/ci_secure_files"
# gitlab_rails['ci_secure_files_object_store_enabled'] = false
# gitlab_rails['ci_secure_files_object_store_remote_directory'] = "ci-secure-files"
# gitlab_rails['ci_secure_files_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

### GitLab Pages
# gitlab_rails['pages_object_store_enabled'] = false
# gitlab_rails['pages_object_store_remote_directory'] = "pages"
# gitlab_rails['pages_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }
# gitlab_rails['pages_local_store_enabled'] = true
# gitlab_rails['pages_local_store_path'] = "/var/opt/gitlab/gitlab-rails/shared/pages"

### Impersonation settings
# gitlab_rails['impersonation_enabled'] = true

### Application settings cache expiry in seconds. (default: 60)
# gitlab_rails['application_settings_cache_seconds'] = 60

### Usage Statistics
# gitlab_rails['usage_ping_enabled'] = true

### GitLab Mattermost
###! These settings are void if Mattermost is installed on the same omnibus
###! install
# gitlab_rails['mattermost_host'] = "https://mattermost.example.com"

### LDAP Settings
###! Docs: https://docs.gitlab.com/omnibus/settings/ldap.html
###! **Be careful not to break the indentation in the ldap_servers block. It is
###!   in yaml format and the spaces must be retained. Using tabs will not work.**

# gitlab_rails['ldap_enabled'] = false
# gitlab_rails['prevent_ldap_sign_in'] = false

###! **remember to close this block with 'EOS' below**
# gitlab_rails['ldap_servers'] = YAML.load <<-'EOS'
#   main: # 'main' is the GitLab 'provider ID' of this LDAP server
#     label: 'LDAP'
#     host: '_your_ldap_server'
#     port: 389
#     uid: 'sAMAccountName'
#     bind_dn: '_the_full_dn_of_the_user_you_will_bind_with'
#     password: '_the_password_of_the_bind_user'
#     encryption: 'plain' # "start_tls" or "simple_tls" or "plain"
#     verify_certificates: true
#     smartcard_auth: false
#     active_directory: true
#     allow_username_or_email_login: false
#     lowercase_usernames: false
#     block_auto_created_users: false
#     base: ''
#     user_filter: ''
#     ## EE only
#     group_base: ''
#     admin_group: ''
#     sync_ssh_keys: false
#
#   secondary: # 'secondary' is the GitLab 'provider ID' of second LDAP server
#     label: 'LDAP'
#     host: '_your_ldap_server'
#     port: 389
#     uid: 'sAMAccountName'
#     bind_dn: '_the_full_dn_of_the_user_you_will_bind_with'
#     password: '_the_password_of_the_bind_user'
#     encryption: 'plain' # "start_tls" or "simple_tls" or "plain"
#     verify_certificates: true
#     smartcard_auth: false
#     active_directory: true
#     allow_username_or_email_login: false
#     lowercase_usernames: false
#     block_auto_created_users: false
#     base: ''
#     user_filter: ''
#     ## EE only
#     group_base: ''
#     admin_group: ''
#     sync_ssh_keys: false
# EOS

### Smartcard authentication settings
###! Docs: https://docs.gitlab.com/ee/administration/auth/smartcard.html
# gitlab_rails['smartcard_enabled'] = false
# gitlab_rails['smartcard_ca_file'] = "/etc/gitlab/ssl/CA.pem"
# gitlab_rails['smartcard_client_certificate_required_host'] = 'smartcard.gitlab.example.com'
# gitlab_rails['smartcard_client_certificate_required_port'] = 3444
# gitlab_rails['smartcard_required_for_git_access'] = false
# gitlab_rails['smartcard_san_extensions'] = false

### OmniAuth Settings
###! Docs: https://docs.gitlab.com/ee/integration/omniauth.html
# gitlab_rails['omniauth_enabled'] = nil
# gitlab_rails['omniauth_allow_single_sign_on'] = ['saml']
# gitlab_rails['omniauth_sync_email_from_provider'] = 'saml'
# gitlab_rails['omniauth_sync_profile_from_provider'] = ['saml']
# gitlab_rails['omniauth_sync_profile_attributes'] = ['email']
# gitlab_rails['omniauth_auto_sign_in_with_provider'] = 'saml'
# gitlab_rails['omniauth_block_auto_created_users'] = true
# gitlab_rails['omniauth_auto_link_ldap_user'] = false
# gitlab_rails['omniauth_auto_link_saml_user'] = false
# gitlab_rails['omniauth_auto_link_user'] = ['saml']
# gitlab_rails['omniauth_external_providers'] = ['twitter', 'google_oauth2']
# gitlab_rails['omniauth_allow_bypass_two_factor'] = ['google_oauth2']
# gitlab_rails['omniauth_providers'] = [
#   {
#     "name" => "google_oauth2",
#     "app_id" => "YOUR APP ID",
#     "app_secret" => "YOUR APP SECRET",
#     "args" => { "access_type" => "offline", "approval_prompt" => "" }
#   }
# ]
# gitlab_rails['omniauth_cas3_session_duration'] = 28800
# gitlab_rails['omniauth_saml_message_max_byte_size'] = 250000

### FortiAuthenticator authentication settings
# gitlab_rails['forti_authenticator_enabled'] = false
# gitlab_rails['forti_authenticator_host'] = 'forti_authenticator.example.com'
# gitlab_rails['forti_authenticator_port'] = 443
# gitlab_rails['forti_authenticator_username'] = 'admin'
# gitlab_rails['forti_authenticator_access_token'] = 's3cr3t'

### FortiToken Cloud authentication settings
# gitlab_rails['forti_token_cloud_enabled'] = false
# gitlab_rails['forti_token_cloud_client_id'] = 'forti_token_cloud_client_id'
# gitlab_rails['forti_token_cloud_client_secret'] = 's3cr3t'

### Backup Settings
###! Docs: https://docs.gitlab.com/omnibus/settings/backups.html

# gitlab_rails['manage_backup_path'] = true
# gitlab_rails['backup_path'] = "/var/opt/gitlab/backups"
# gitlab_rails['backup_gitaly_backup_path'] = "/opt/gitlab/embedded/bin/gitaly-backup"

###! Docs: https://docs.gitlab.com/ee/raketasks/backup_restore.html#backup-archive-permissions
# gitlab_rails['backup_archive_permissions'] = 0644

# gitlab_rails['backup_pg_schema'] = 'public'

###! The duration in seconds to keep backups before they are allowed to be deleted
# gitlab_rails['backup_keep_time'] = 604800

# gitlab_rails['backup_upload_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AKIAKIAKI',
#   'aws_secret_access_key' => 'secret123',
#   # # If IAM profile use is enabled, remove aws_access_key_id and aws_secret_access_key
#   'use_iam_profile' => false
# }
# gitlab_rails['backup_upload_remote_directory'] = 'my.s3.bucket'
# gitlab_rails['backup_multipart_chunk_size'] = 104857600

###! **Turns on AWS Server-Side Encryption with Amazon S3-Managed Keys for
###!   backups**
# gitlab_rails['backup_encryption'] = 'AES256'
###! The encryption key to use with AWS Server-Side Encryption.
###! Setting this value will enable Server-Side Encryption with customer provided keys;
###!   otherwise S3-managed keys are used.
# gitlab_rails['backup_encryption_key'] = '<base64-encoded encryption key>'

###! **Turns on AWS Server-Side Encryption with Amazon SSE-KMS (AWS managed but customer-master key)
# gitlab_rails['backup_upload_storage_options'] = {
#  'server_side_encryption' => 'aws:kms',
#  'server_side_encryption_kms_key_id' => 'arn:aws:kms:YOUR-KEY-ID-HERE'
# }

###! **Specifies Amazon S3 storage class to use for backups. Valid values
###!   include 'STANDARD', 'STANDARD_IA', and 'REDUCED_REDUNDANCY'**
# gitlab_rails['backup_storage_class'] = 'STANDARD'

###! Skip parts of the backup. Comma separated.
###! Docs: https://docs.gitlab.com/ee/raketasks/backup_restore.html#excluding-specific-directories-from-the-backup
#gitlab_rails['env'] = {
#    "SKIP" => "db,uploads,repositories,builds,artifacts,lfs,registry,pages"
#}

### For setting up different data storing directory
###! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#store-git-data-in-an-alternative-directory
###! **If you want to use a single non-default directory to store git data use a
###!   path that doesn't contain symlinks.**
# git_data_dirs({
#   "default" => {
#     "path" => "/mnt/nfs-01/git-data"
#    }
# })

### Gitaly settings
# gitlab_rails['gitaly_token'] = 'secret token'

### For storing GitLab application uploads, eg. LFS objects, build artifacts
###! Docs: https://docs.gitlab.com/ee/development/shared_files.html
# gitlab_rails['shared_path'] = '/var/opt/gitlab/gitlab-rails/shared'

### For storing encrypted configuration files
###! Docs: https://docs.gitlab.com/ee/administration/encrypted_configuration.html
# gitlab_rails['encrypted_settings_path'] = '/var/opt/gitlab/gitlab-rails/shared/encrypted_settings'

### Wait for file system to be mounted
###! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#only-start-omnibus-gitlab-services-after-a-given-file-system-is-mounted
# high_availability['mountpoint'] = ["/var/opt/gitlab/git-data", "/var/opt/gitlab/gitlab-rails/shared"]

### GitLab Shell settings for GitLab
# gitlab_rails['gitlab_shell_ssh_port'] = 22
# gitlab_rails['gitlab_shell_git_timeout'] = 800

### Extra customization
# gitlab_rails['extra_google_analytics_id'] = '_your_tracking_id'
# gitlab_rails['extra_google_tag_manager_id'] = '_your_tracking_id'
# gitlab_rails['extra_one_trust_id'] = '_your_one_trust_id'
# gitlab_rails['extra_google_tag_manager_nonce_id'] = '_your_google_tag_manager_id'
# gitlab_rails['extra_bizible'] = false
# gitlab_rails['extra_matomo_url'] = '_your_matomo_url'
# gitlab_rails['extra_matomo_site_id'] = '_your_matomo_site_id'
# gitlab_rails['extra_matomo_disable_cookies'] = false

##! Docs: https://docs.gitlab.com/omnibus/settings/environment-variables.html
# gitlab_rails['env'] = {
#   'BUNDLE_GEMFILE' => "/opt/gitlab/embedded/service/gitlab-rails/Gemfile",
#   'PATH' => "/opt/gitlab/bin:/opt/gitlab/embedded/bin:/bin:/usr/bin"
# }

# gitlab_rails['rack_attack_git_basic_auth'] = {
#   'enabled' => false,
#   'ip_whitelist' => ["127.0.0.1"],
#   'maxretry' => 10,
#   'findtime' => 60,
#   'bantime' => 3600
# }

# gitlab_rails['dir'] = "/var/opt/gitlab/gitlab-rails"
# gitlab_rails['log_directory'] = "/var/log/gitlab/gitlab-rails"

#### Change the initial default admin password and shared runner registration tokens.
####! **Only applicable on initial setup, changing these settings after database
####!   is created and seeded won't yield any change.**
# gitlab_rails['initial_root_password'] = "password"
# gitlab_rails['initial_shared_runners_registration_token'] = "token"

#### Toggle if root password should be printed to STDOUT during initialization
# gitlab_rails['display_initial_root_password'] = false

#### Toggle if initial root password should be written to /etc/gitlab/initial_root_password
# gitlab_rails['store_initial_root_password'] = true

#### Set path to an initial license to be used while bootstrapping GitLab.
####! **Only applicable on initial setup, future license updations need to be done via UI.
####! Updating the file specified in this path won't yield any change after the first reconfigure run.
# gitlab_rails['initial_license_file'] = '/etc/gitlab/company.gitlab-license'

#### Enable or disable automatic database migrations
# gitlab_rails['auto_migrate'] = true

#### This is advanced feature used by large gitlab deployments where loading
#### whole RAILS env takes a lot of time.
# gitlab_rails['rake_cache_clear'] = true

### GitLab database settings
###! Docs: https://docs.gitlab.com/omnibus/settings/database.html
###! **Only needed if you use an external database.**
# gitlab_rails['db_adapter'] = "postgresql"
# gitlab_rails['db_encoding'] = "unicode"
# gitlab_rails['db_collation'] = nil
# gitlab_rails['db_database'] = "gitlabhq_production"
# gitlab_rails['db_username'] = "gitlab"
# gitlab_rails['db_password'] = nil
# gitlab_rails['db_host'] = nil
# gitlab_rails['db_port'] = 5432
# gitlab_rails['db_socket'] = nil
# gitlab_rails['db_sslmode'] = nil
# gitlab_rails['db_sslcompression'] = 0
# gitlab_rails['db_sslrootcert'] = nil
# gitlab_rails['db_sslcert'] = nil
# gitlab_rails['db_sslkey'] = nil
# gitlab_rails['db_prepared_statements'] = false
# gitlab_rails['db_statements_limit'] = 1000
# gitlab_rails['db_connect_timeout'] = nil
# gitlab_rails['db_keepalives'] = nil
# gitlab_rails['db_keepalives_idle'] = nil
# gitlab_rails['**********************'] = nil
# gitlab_rails['db_keepalives_count'] = nil
# gitlab_rails['db_tcp_user_timeout'] = nil
# gitlab_rails['db_application_name'] = nil
# gitlab_rails['db_database_tasks'] = true


### GitLab Redis settings
###! Connect to your own Redis instance
###! Docs: https://docs.gitlab.com/omnibus/settings/redis.html

#### Redis TCP connection
# gitlab_rails['redis_host'] = "127.0.0.1"
# gitlab_rails['redis_port'] = 6379
# gitlab_rails['redis_ssl'] = false
# gitlab_rails['redis_password'] = nil
# gitlab_rails['redis_database'] = 0
# gitlab_rails['redis_enable_client'] = true

#### Redis local UNIX socket (will be disabled if TCP method is used)
# gitlab_rails['redis_socket'] = "/var/opt/gitlab/redis/redis.socket"

#### Sentinel support
####! To have Sentinel working, you must enable Redis TCP connection support
####! above and define a few Sentinel hosts below (to get a reliable setup
####! at least 3 hosts).
####! **You don't need to list every sentinel host, but the ones not listed will
####!   not be used in a fail-over situation to query for the new master.**
# gitlab_rails['redis_sentinels'] = [
#   {'host' => '127.0.0.1', 'port' => 26379},
# ]

#### Separate instances support
###! Docs: https://docs.gitlab.com/omnibus/settings/redis.html#running-with-multiple-redis-instances
# gitlab_rails['redis_cache_instance'] = nil
# gitlab_rails['redis_cache_sentinels'] = nil
# gitlab_rails['redis_queues_instance'] = nil
# gitlab_rails['redis_queues_sentinels'] = nil
# gitlab_rails['redis_shared_state_instance'] = nil
# gitlab_rails['redis_shared_state_sentinels'] = nil
# gitlab_rails['redis_trace_chunks_instance'] = nil
# gitlab_rails['redis_trace_chunks_sentinels'] = nil
# gitlab_rails['redis_actioncable_instance'] = nil
# gitlab_rails['redis_actioncable_sentinels'] = nil
# gitlab_rails['redis_rate_limiting_instance'] = nil
# gitlab_rails['redis_rate_limiting_sentinels'] = nil
# gitlab_rails['redis_sessions_instance'] = nil
# gitlab_rails['redis_sessions_sentinels'] = nil

################################################################################
## Container Registry settings
##! Docs: https://docs.gitlab.com/ee/administration/packages/container_registry.html
################################################################################

# registry_external_url 'https://registry.example.com'

### Settings used by GitLab application
# gitlab_rails['registry_enabled'] = true
# gitlab_rails['registry_host'] = "registry.gitlab.example.com"
# gitlab_rails['registry_port'] = "5005"
# gitlab_rails['registry_path'] = "/var/opt/gitlab/gitlab-rails/shared/registry"

# Notification secret, it's used to authenticate notification requests to GitLab application
# You only need to change this when you use external Registry service, otherwise
# it will be taken directly from notification settings of your Registry
# gitlab_rails['registry_notification_secret'] = nil

###! **Do not change the following 3 settings unless you know what you are
###!   doing**
# gitlab_rails['registry_api_url'] = "http://127.0.0.1:5000"
# gitlab_rails['registry_key_path'] = "/var/opt/gitlab/gitlab-rails/certificate.key"
# gitlab_rails['registry_issuer'] = "*********************"

### Settings used by Registry application
# registry['enable'] = true
# registry['username'] = "registry"
# registry['group'] = "registry"
# registry['uid'] = nil
# registry['gid'] = nil
# registry['dir'] = "/var/opt/gitlab/registry"
# registry['registry_http_addr'] = "127.0.0.1:5000"
# registry['debug_addr'] = "localhost:5001"
# registry['log_directory'] = "/var/log/gitlab/registry"
# registry['env_directory'] = "/opt/gitlab/etc/registry/env"
# registry['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
# registry['log_level'] = "info"
# registry['log_formatter'] = "text"
# registry['rootcertbundle'] = "/var/opt/gitlab/registry/certificate.crt"
# registry['health_storagedriver_enabled'] = true
# registry['middleware'] = nil
# registry['storage_delete_enabled'] = true
# registry['validation_enabled'] = false
# registry['autoredirect'] = false
# registry['compatibility_schema1_enabled'] = false

### Registry backend storage
###! Docs: https://docs.gitlab.com/ee/administration/packages/container_registry.html#configure-storage-for-the-container-registry
# registry['storage'] = {
#   's3' => {
#     'accesskey' => 's3-access-key',
#     'secretkey' => 's3-secret-key-for-access-key',
#     'bucket' => 'your-s3-bucket',
#     'region' => 'your-s3-region',
#     'regionendpoint' => 'your-s3-regionendpoint'
#   },
#   'redirect' => {
#     'disable' => false
#   }
# }

### Registry notifications endpoints
# registry['notifications'] = [
#   {
#     'name' => 'test_endpoint',
#     'url' => 'https://gitlab.example.com/notify2',
#     'timeout' => '500ms',
#     'threshold' => 5,
#     'backoff' => '1s',
#     'headers' => {
#       "Authorization" => ["AUTHORIZATION_EXAMPLE_TOKEN"]
#     }
#   }
# ]
### Default registry notifications
# registry['default_notifications_timeout'] = "500ms"
# registry['default_notifications_threshold'] = 5
# registry['default_notifications_backoff'] = "1s"
# registry['default_notifications_headers'] = {}

################################################################################
## Error Reporting and Logging with Sentry
################################################################################
# gitlab_rails['sentry_enabled'] = false
# gitlab_rails['sentry_dsn'] = 'https://<key>@sentry.io/<project>'
# gitlab_rails['sentry_clientside_dsn'] = 'https://<key>@sentry.io/<project>'
# gitlab_rails['sentry_environment'] = 'production'

################################################################################
## CI_JOB_JWT
################################################################################
##! RSA private key used to sign CI_JOB_JWT
# gitlab_rails['ci_jwt_signing_key'] = nil # Will be generated if not set.

################################################################################
## GitLab Workhorse
##! Docs: https://gitlab.com/gitlab-org/gitlab/-/blob/master/workhorse/README.md
################################################################################

# gitlab_workhorse['enable'] = true
# gitlab_workhorse['ha'] = false
# gitlab_workhorse['alt_document_root'] = nil

##! Duration to wait for all requests to finish (e.g. "10s" for 10
##! seconds). By default this is disabled to preserve the existing
##! behavior of fast shutdown. This should not be set higher than 30
##! seconds, since gitlab-ctl will wait up to 30 seconds (as defined by
##! the SVWAIT variable) and report a timeout error if the process has
##! not shut down.
# gitlab_workhorse['shutdown_timeout'] = nil
# gitlab_workhorse['listen_network'] = "unix"
# gitlab_workhorse['listen_umask'] = 000
# gitlab_workhorse['listen_addr'] = "/var/opt/gitlab/gitlab-workhorse/sockets/socket"
# gitlab_workhorse['auth_backend'] = "http://localhost:8080"

##! Enable Redis keywatcher, if this setting is not present it defaults to true
# gitlab_workhorse['workhorse_keywatcher'] = true

##! the empty string is the default in gitlab-workhorse option parser
# gitlab_workhorse['auth_socket'] = "''"

##! put an empty string on the command line
# gitlab_workhorse['pprof_listen_addr'] = "''"

# gitlab_workhorse['prometheus_listen_addr'] = "localhost:9229"

# gitlab_workhorse['dir'] = "/var/opt/gitlab/gitlab-workhorse"
# gitlab_workhorse['log_directory'] = "/var/log/gitlab/gitlab-workhorse"
# gitlab_workhorse['proxy_headers_timeout'] = "1m0s"

##! limit number of concurrent API requests, defaults to 0 which is unlimited
# gitlab_workhorse['api_limit'] = 0

##! limit number of API requests allowed to be queued, defaults to 0 which
##! disables queuing
# gitlab_workhorse['api_queue_limit'] = 0

##! duration after which we timeout requests if they sit too long in the queue
# gitlab_workhorse['api_queue_duration'] = "30s"

##! Long polling duration for job requesting for runners
# gitlab_workhorse['api_ci_long_polling_duration'] = "60s"

##! Propagate X-Request-Id if available. Workhorse will generate a random value otherwise.
# gitlab_workhorse['propagate_correlation_id'] = false

##! A list of CIDR blocks to allow for propagation of correlation ID.
##! propagate_correlation_id should also be set to true.
##! For example: %w(127.0.0.1/32 ***********/32)
# gitlab_workhorse['trusted_cidrs_for_propagation'] = nil

##! A list of CIDR blocks that must match remote IP addresses to use
##! X-Forwarded-For HTTP header for the actual client IP. Used in
##! conjuction with propagate_correlation_id and
##! trusted_cidrs_for_propagation.
##! For example: %w(127.0.0.1/32 ***********/32)
# gitlab_workhorse['trusted_cidrs_for_x_forwarded_for'] = nil

##! Log format: default is json, can also be text or none.
# gitlab_workhorse['log_format'] = "json"

# gitlab_workhorse['env_directory'] = "/opt/gitlab/etc/gitlab-workhorse/env"
# gitlab_workhorse['env'] = {
#   'PATH' => "/opt/gitlab/bin:/opt/gitlab/embedded/bin:/bin:/usr/bin",
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }

##! Resource limitations for the dynamic image scaler.
##! Exceeding these thresholds will cause Workhorse to serve images in their original size.
##!
##! Maximum number of scaler processes that are allowed to execute concurrently.
##! It is recommended for this not to exceed the number of CPUs available.
# gitlab_workhorse['image_scaler_max_procs'] = 4
##!
##! Maximum file size in bytes for an image to be considered eligible for rescaling
# gitlab_workhorse['image_scaler_max_filesize'] = 250000

##! Service name used to register GitLab Workhorse as a Consul service
# gitlab_workhorse['consul_service_name'] = 'workhorse'
##! Semantic metadata used when registering GitLab Workhorse as a Consul service
# gitlab_workhorse['consul_service_meta'] = {}

################################################################################
## GitLab User Settings
##! Modify default git user.
##! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#changing-the-name-of-the-git-user-group
################################################################################

# user['username'] = "git"
# user['group'] = "git"
# user['uid'] = nil
# user['gid'] = nil

##! The shell for the git user
# user['shell'] = "/bin/sh"

##! The home directory for the git user
# user['home'] = "/var/opt/gitlab"

# user['git_user_name'] = "GitLab"
# user['git_user_email'] = "gitlab@#{node['fqdn']}"

################################################################################
## GitLab Puma
##! Tweak puma settings.
##! Docs: https://docs.gitlab.com/ee/administration/operations/puma.html
################################################################################

# puma['enable'] = true
# puma['ha'] = false
# puma['worker_timeout'] = 60
# puma['worker_processes'] = 2
# puma['min_threads'] = 4
# puma['max_threads'] = 4

### Advanced settings
# puma['listen'] = '127.0.0.1'
# puma['port'] = 8080
# puma['socket'] = '/var/opt/gitlab/gitlab-rails/sockets/gitlab.socket'
# puma['somaxconn'] = 1024

### SSL settings
# puma['ssl_listen'] = nil
# puma['ssl_port'] = nil
# puma['ssl_certificate'] = nil
# puma['ssl_certificate_key'] = nil
# puma['ssl_client_certificate'] = nil
# puma['ssl_cipher_filter'] = nil
# puma['ssl_verify_mode'] = 'none'

# puma['pidfile'] = '/opt/gitlab/var/puma/puma.pid'
# puma['state_path'] = '/opt/gitlab/var/puma/puma.state'

###! **We do not recommend changing this setting**
# puma['log_directory'] = "/var/log/gitlab/puma"

### **Only change these settings if you understand well what they mean**
###! Docs: https://github.com/schneems/puma_worker_killer
# puma['per_worker_max_memory_mb'] = 1024

# puma['exporter_enabled'] = false
# puma['exporter_address'] = "127.0.0.1"
# puma['exporter_port'] = 8083
# puma['exporter_tls_enabled'] = false
# puma['exporter_tls_cert_path'] = ""
# puma['exporter_tls_key_path'] = ""

# puma['prometheus_scrape_scheme'] = 'http'
# puma['prometheus_scrape_tls_server_name'] = 'localhost'
# puma['prometheus_scrape_tls_skip_verification'] = false

##! Service name used to register Puma as a Consul service
# puma['consul_service_name'] = 'rails'
##! Semantic metadata used when registering Puma as a Consul service
# puma['consul_service_meta'] = {}

################################################################################
## GitLab Sidekiq
################################################################################

##! GitLab allows one to start multiple sidekiq processes. These
##! processes can be used to consume a dedicated set of queues. This
##! can be used to ensure certain queues are able to handle additional workload.
##! https://docs.gitlab.com/ee/administration/operations/extra_sidekiq_processes.html

# sidekiq['log_directory'] = "/var/log/gitlab/sidekiq"
# sidekiq['log_format'] = "json"
# sidekiq['shutdown_timeout'] = 4
# sidekiq['queue_selector'] = false
# sidekiq['interval'] = nil
# sidekiq['max_concurrency'] = 50
# sidekiq['min_concurrency'] = nil

##! GitLab allows route a job to a particular queue determined by an array of ##! routing rules.
##! Each routing rule is a tuple of queue selector query and corresponding queue. By default,
##! the routing rules are not configured (empty array)

# sidekiq['routing_rules'] = []

##! Each entry in the queue_groups array denotes a group of queues that have to be processed by a
##! Sidekiq process. Multiple queues can be processed by the same process by
##! separating them with a comma within the group entry, a `*` will process all queues

# sidekiq['queue_groups'] = ['*']

##! If negate is enabled then Sidekiq will process all the queues that
##! don't match those in queue_groups.

# sidekiq['negate'] = false

##! Specifies where Prometheus metrics endpoints should be made available for Sidekiq processes.
# sidekiq['metrics_enabled'] = true
# sidekiq['exporter_log_enabled'] = false
# sidekiq['exporter_tls_enabled'] = false
# sidekiq['exporter_tls_cert_path'] = ""
# sidekiq['exporter_tls_key_path'] = ""
# sidekiq['listen_address'] = "localhost"
# sidekiq['listen_port'] = 8082

##! Specifies where health-check endpoints should be made available for Sidekiq processes.
##! Defaults to the same settings as for Prometheus metrics (see above).
# sidekiq['health_checks_enabled'] = true
# sidekiq['health_checks_listen_address'] = "localhost"
# sidekiq['health_checks_listen_port'] = 8092

##! Service name used to register Sidekiq as a Consul service
# sidekiq['consul_service_name'] = 'sidekiq'
##! Semantic metadata used when registering Sidekiq as a Consul service
# sidekiq['consul_service_meta'] = {}

################################################################################
## gitlab-shell
################################################################################

# gitlab_shell['audit_usernames'] = false
# gitlab_shell['log_level'] = 'INFO'
# gitlab_shell['log_format'] = 'json'
# gitlab_shell['http_settings'] = { user: 'username', password: 'password', ca_file: '/etc/ssl/cert.pem', ca_path: '/etc/pki/tls/certs'}
# gitlab_shell['log_directory'] = "/var/log/gitlab/gitlab-shell/"

# gitlab_shell['auth_file'] = "/var/opt/gitlab/.ssh/authorized_keys"

### Migration to Go feature flags
###! Docs: https://gitlab.com/gitlab-org/gitlab-shell#migration-to-go-feature-flags
# gitlab_shell['migration'] = { enabled: true, features: [] }

### Git trace log file.
###! If set, git commands receive GIT_TRACE* environment variables
###! Docs: https://git-scm.com/book/es/v2/Git-Internals-Environment-Variables#Debugging
###! An absolute path starting with / – the trace output will be appended to
###! that file. It needs to exist so we can check permissions and avoid
###! throwing warnings to the users.
# gitlab_shell['git_trace_log_file'] = "/var/log/gitlab/gitlab-shell/gitlab-shell-git-trace.log"

##! **We do not recommend changing this directory.**
# gitlab_shell['dir'] = "/var/opt/gitlab/gitlab-shell"

################################################################
## GitLab PostgreSQL
################################################################

###! Changing any of these settings requires a restart of postgresql.
###! By default, reconfigure reloads postgresql if it is running. If you
###! change any of these settings, be sure to run `gitlab-ctl restart postgresql`
###! after reconfigure in order for the changes to take effect.
# postgresql['enable'] = true
# postgresql['listen_address'] = nil
# postgresql['port'] = 5432

## Only used when Patroni is enabled. This is the port that PostgreSQL responds to other
## cluster members. This port is used by Patroni to advertize the PostgreSQL connection
## endpoint to the cluster. By default it is the same as postgresql['port'].
# postgresql['connect_port'] = 5432

##! **recommend value is 1/4 of total RAM, up to 14GB.**
# postgresql['shared_buffers'] = "256MB"

### Advanced settings
# postgresql['ha'] = false
# postgresql['dir'] = "/var/opt/gitlab/postgresql"
# postgresql['log_directory'] = "/var/log/gitlab/postgresql"
# postgresql['log_destination'] = nil
# postgresql['logging_collector'] = nil
# postgresql['log_truncate_on_rotation'] = nil
# postgresql['log_rotation_age'] = nil
# postgresql['log_rotation_size'] = nil
##! 'username' affects the system and PostgreSQL user accounts created during installation and cannot be changed
##! on an existing installation. See https://gitlab.com/gitlab-org/omnibus-gitlab/-/issues/3606 for more details.
# postgresql['username'] = "gitlab-psql"
# postgresql['group'] = "gitlab-psql"
##! `SQL_USER_PASSWORD_HASH` can be generated using the command `gitlab-ctl pg-password-md5 gitlab`
# postgresql['sql_user_password'] = 'SQL_USER_PASSWORD_HASH'
# postgresql['uid'] = nil
# postgresql['gid'] = nil
# postgresql['shell'] = "/bin/sh"
# postgresql['home'] = "/var/opt/gitlab/postgresql"
# postgresql['user_path'] = "/opt/gitlab/embedded/bin:/opt/gitlab/bin:$PATH"
# postgresql['sql_user'] = "gitlab"
# postgresql['max_connections'] = 200
# postgresql['md5_auth_cidr_addresses'] = []
# postgresql['trust_auth_cidr_addresses'] = []
# postgresql['wal_buffers'] = "-1"
# postgresql['autovacuum_max_workers'] = "3"
# postgresql['autovacuum_freeze_max_age'] = "*********"
# postgresql['log_statement'] = nil
# postgresql['track_activity_query_size'] = "1024"
# postgresql['shared_preload_libraries'] = nil
# postgresql['dynamic_shared_memory_type'] = nil
# postgresql['hot_standby'] = "off"

### SSL settings
# See https://www.postgresql.org/docs/12/static/runtime-config-connection.html#GUC-SSL-CERT-FILE for more details
# postgresql['ssl'] = 'on'
# postgresql['hostssl'] = false
# postgresql['ssl_ciphers'] = 'HIGH:MEDIUM:+3DES:!aNULL:!SSLv3:!TLSv1'
# postgresql['ssl_cert_file'] = 'server.crt'
# postgresql['ssl_key_file'] = 'server.key'
# postgresql['ssl_ca_file'] = '/opt/gitlab/embedded/ssl/certs/cacert.pem'
# postgresql['ssl_crl_file'] = nil
# postgresql['cert_auth_addresses'] = {
#   'ADDRESS' => {
#     database: 'gitlabhq_production',
#     user: 'gitlab'
#   }
# }

### Replication settings
###! Note, some replication settings do not require a full restart. They are documented below.
# postgresql['wal_level'] = "hot_standby"
# postgresql['wal_log_hints'] = 'off'
# postgresql['max_wal_senders'] = 5
# postgresql['max_replication_slots'] = 0
# postgresql['max_locks_per_transaction'] = 128

# Backup/Archive settings
# postgresql['archive_mode'] = "off"

###! Changing any of these settings only requires a reload of postgresql. You do not need to
###! restart postgresql if you change any of these and run reconfigure.
# postgresql['work_mem'] = "16MB"
# postgresql['maintenance_work_mem'] = "16MB"
# postgresql['checkpoint_timeout'] = "5min"
# postgresql['checkpoint_completion_target'] = 0.9
# postgresql['effective_io_concurrency'] = 1
# postgresql['checkpoint_warning'] = "30s"
# postgresql['effective_cache_size'] = "1MB"
# postgresql['shmmax'] =  17179869184 # or 4294967295
# postgresql['shmall'] =  4194304 # or 1048575
# postgresql['autovacuum'] = "on"
# postgresql['log_autovacuum_min_duration'] = "-1"
# postgresql['autovacuum_naptime'] = "1min"
# postgresql['autovacuum_vacuum_threshold'] = "50"
# postgresql['autovacuum_analyze_threshold'] = "50"
# postgresql['autovacuum_vacuum_scale_factor'] = "0.02"
# postgresql['autovacuum_analyze_scale_factor'] = "0.01"
# postgresql['autovacuum_vacuum_cost_delay'] = "20ms"
# postgresql['autovacuum_vacuum_cost_limit'] = "-1"
# postgresql['statement_timeout'] = "60000"
# postgresql['idle_in_transaction_session_timeout'] = "60000"
# postgresql['log_line_prefix'] = "%a"
# postgresql['max_worker_processes'] = 8
# postgresql['max_parallel_workers_per_gather'] = 0
# postgresql['log_lock_waits'] = 1
# postgresql['deadlock_timeout'] = '5s'
# postgresql['track_io_timing'] = 0
# postgresql['default_statistics_target'] = 1000

### Available in PostgreSQL 9.6 and later
# postgresql['min_wal_size'] = "80MB"
# postgresql['max_wal_size'] = "1GB"

# Backup/Archive settings
# postgresql['archive_command'] = nil
# postgresql['archive_timeout'] = "0"

### Replication settings
# postgresql['sql_replication_user'] = "gitlab_replicator"
# postgresql['sql_replication_password'] = "md5 hash of postgresql password" # You can generate with `gitlab-ctl pg-password-md5 <dbuser>`
# postgresql['wal_keep_segments'] = 10
# postgresql['max_standby_archive_delay'] = "30s"
# postgresql['max_standby_streaming_delay'] = "30s"
# postgresql['synchronous_commit'] = on
# postgresql['synchronous_standby_names'] = ''
# postgresql['hot_standby_feedback'] = 'off'
# postgresql['random_page_cost'] = 2.0
# postgresql['log_temp_files'] = -1
# postgresql['log_checkpoints'] = 'off'
# To add custom entries to pg_hba.conf use the following
# postgresql['custom_pg_hba_entries'] = {
#   APPLICATION: [ # APPLICATION should identify what the settings are used for
#     {
#       type: example,
#       database: example,
#       user: example,
#       cidr: example,
#       method: example,
#       option: example
#     }
#   ]
# }
# See https://www.postgresql.org/docs/12/static/auth-pg-hba-conf.html for an explanation
# of the values

### Version settings
# Set this if you have disabled the bundled PostgreSQL but still want to use the backup rake tasks
# postgresql['version'] = 10


##! Automatically restart PostgreSQL service when version changes.
# postgresql['auto_restart_on_version_change'] = true

################################################################################
## GitLab Redis
##! **Can be disabled if you are using your own Redis instance.**
##! Docs: https://docs.gitlab.com/omnibus/settings/redis.html
################################################################################

# redis['enable'] = true
# redis['ha'] = false
# redis['hz'] = 10
# redis['dir'] = "/var/opt/gitlab/redis"
# redis['log_directory'] = "/var/log/gitlab/redis"
# redis['username'] = "gitlab-redis"
# redis['group'] = "gitlab-redis"
# redis['maxclients'] = "10000"
# redis['maxmemory'] = "0"
# redis['maxmemory_policy'] = "noeviction"
# redis['maxmemory_samples'] = "5"
# redis['stop_writes_on_bgsave_error'] = true
# redis['tcp_backlog'] = 511
# redis['tcp_timeout'] = "60"
# redis['tcp_keepalive'] = "300"
# redis['uid'] = nil
# redis['gid'] = nil

### Redis TLS settings
###! To run Redis over TLS, specify values for the following settings
# redis['tls_port'] = nil
# redis['tls_cert_file'] = nil
# redis['tls_key_file'] = nil

###! Other TLS related optional settings
# redis['tls_dh_params_file'] = nil
# redis['tls_ca_cert_dir'] = '/opt/gitlab/embedded/ssl/certs/'
# redis['tls_ca_cert_file'] = '/opt/gitlab/embedded/ssl/certs/cacert.pem'
# redis['tls_auth_clients'] = 'optional'
# redis['tls_replication'] = nil
# redis['tls_cluster'] = nil
# redis['tls_protocols'] = nil
# redis['tls_ciphers'] = nil
# redis['tls_ciphersuites'] = nil
# redis['tls_prefer_server_ciphers'] = nil
# redis['tls_session_caching'] = nil
# redis['tls_session_cache_size'] = nil
# redis['tls_session_cache_timeout'] = nil

### Disable or obfuscate unnecessary redis command names
### Uncomment and edit this block to add or remove entries.
### See https://docs.gitlab.com/omnibus/settings/redis.html#renamed-commands
### for detailed usage
###
# redis['rename_commands'] = {
#   'KEYS': ''
#}
#

###! **To enable only Redis service in this machine, uncomment
###!   one of the lines below (choose master or replica instance types).**
###! Docs: https://docs.gitlab.com/omnibus/settings/redis.html
###!       https://docs.gitlab.com/ee/administration/high_availability/redis.html
# redis_master_role['enable'] = true
# redis_replica_role['enable'] = true

### Redis TCP support (will disable UNIX socket transport)
# redis['bind'] = '0.0.0.0' # or specify an IP to bind to a single one
# redis['port'] = 6379
# redis['password'] = 'redis-password-goes-here'

### Redis Sentinel support
###! **You need a master replica Redis replication to be able to do failover**
###! **Please read the documentation before enabling it to understand the
###!   caveats:**
###! Docs: https://docs.gitlab.com/ee/administration/high_availability/redis.html

### Replication support
#### Replica Redis instance
# redis['master'] = false # by default this is true

#### Replica and Sentinel shared configuration
####! **Both need to point to the master Redis instance to get replication and
####!   heartbeat monitoring**
# redis['master_name'] = 'gitlab-redis'
# redis['master_ip'] = nil
# redis['master_port'] = 6379

#### Support to run redis replicas in a Docker or NAT environment
####! Docs: https://redis.io/topics/replication#configuring-replication-in-docker-and-nat
# redis['announce_ip'] = nil
# redis['announce_port'] = nil
# redis['announce_ip_from_hostname'] = false

####! **Master password should have the same value defined in
####!   redis['password'] to enable the instance to transition to/from
####!   master/replica in a failover event.**
# redis['master_password'] = 'redis-password-goes-here'

####! Increase these values when your replicas can't catch up with master
# redis['client_output_buffer_limit_normal'] = '0 0 0'
# redis['client_output_buffer_limit_replica'] = '256mb 64mb 60'
# redis['client_output_buffer_limit_pubsub'] = '32mb 8mb 60'

#####! Redis snapshotting frequency
#####! Set to [] to disable
#####! Set to [''] to clear previously set values
# redis['save'] = [ '900 1', '300 10', '60 10000' ]

#####! Redis lazy freeing
#####! Defaults to false
# redis['lazyfree_lazy_eviction'] = true
# redis['lazyfree_lazy_expire'] = true
# redis['lazyfree_lazy_server_del'] = true
# redis['replica_lazy_flush'] = true

#####! Redis threaded I/O
#####! Defaults to disabled
# redis['io_threads'] = 4
# redis['io_threads_do_reads'] = true

################################################################################
## GitLab Web server
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html#using-a-non-bundled-web-server
################################################################################

##! When bundled nginx is disabled we need to add the external webserver user to
##! the GitLab webserver group.
# web_server['external_users'] = []
# web_server['username'] = 'gitlab-www'
# web_server['group'] = 'gitlab-www'
# web_server['uid'] = nil
# web_server['gid'] = nil
# web_server['shell'] = '/bin/false'
# web_server['home'] = '/var/opt/gitlab/nginx'

################################################################################
## GitLab NGINX
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html
################################################################################

# nginx['enable'] = true
# nginx['client_max_body_size'] = '250m'
# nginx['redirect_http_to_https'] = false
# nginx['redirect_http_to_https_port'] = 80

##! Most root CA's are included by default
# nginx['ssl_client_certificate'] = "/etc/gitlab/ssl/ca.crt"

##! enable/disable 2-way SSL client authentication
# nginx['ssl_verify_client'] = "off"

##! if ssl_verify_client on, verification depth in the client certificates chain
# nginx['ssl_verify_depth'] = "1"

# nginx['ssl_certificate'] = "/etc/gitlab/ssl/#{node['fqdn']}.crt"
# nginx['ssl_certificate_key'] = "/etc/gitlab/ssl/#{node['fqdn']}.key"
# nginx['ssl_ciphers'] = "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384"
# nginx['ssl_prefer_server_ciphers'] = "off"

##! **Recommended by: https://raymii.org/s/tutorials/Strong_SSL_Security_On_nginx.html
##!                   https://cipherli.st/**
# nginx['ssl_protocols'] = "TLSv1.2 TLSv1.3"

##! **Recommended in: https://nginx.org/en/docs/http/ngx_http_ssl_module.html**
# nginx['ssl_session_cache'] = "shared:SSL:10m"

##! **Recommended in: https://ssl-config.mozilla.org/#server=nginx&version=1.17.7&config=intermediate&openssl=1.1.1d&ocsp=false&guideline=5.6**
# nginx['ssl_session_tickets'] = "off"

##! **Default according to https://nginx.org/en/docs/http/ngx_http_ssl_module.html**
# nginx['ssl_session_timeout'] = "1d"

# nginx['ssl_dhparam'] = nil # Path to dhparams.pem, eg. /etc/gitlab/ssl/dhparams.pem
# nginx['listen_addresses'] = ['*', '[::]']

##! **Defaults to forcing web browsers to always communicate using only HTTPS**
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html#setting-http-strict-transport-security
# nginx['hsts_max_age'] = 63072000
# nginx['hsts_include_subdomains'] = false

##! Defaults to stripping path information when making cross-origin requests
# nginx['referrer_policy'] = 'strict-origin-when-cross-origin'

##! **Docs: http://nginx.org/en/docs/http/ngx_http_gzip_module.html**
# nginx['gzip_enabled'] = true

##! **Override only if you use a reverse proxy**
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html#setting-the-nginx-listen-port
# nginx['listen_port'] = nil

##! **Override only if your reverse proxy internally communicates over HTTP**
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html#supporting-proxied-ssl
# nginx['listen_https'] = nil

##! **Override only if you use a reverse proxy with proxy protocol enabled**
##! Docs: https://docs.gitlab.com/omnibus/settings/nginx.html#configuring-proxy-protocol
# nginx['proxy_protocol'] = false

# nginx['custom_gitlab_server_config'] = "location ^~ /foo-namespace/bar-project/raw/ {\n deny all;\n}\n"
# nginx['custom_nginx_config'] = "include /etc/nginx/conf.d/example.conf;"
# nginx['proxy_read_timeout'] = 3600
# nginx['proxy_connect_timeout'] = 300
# nginx['proxy_set_headers'] = {
#  "Host" => "$http_host_with_default",
#  "X-Real-IP" => "$remote_addr",
#  "X-Forwarded-For" => "$proxy_add_x_forwarded_for",
#  "X-Forwarded-Proto" => "https",
#  "X-Forwarded-Ssl" => "on",
#  "Upgrade" => "$http_upgrade",
#  "Connection" => "$connection_upgrade"
# }
# nginx['proxy_cache_path'] = 'proxy_cache keys_zone=gitlab:10m max_size=1g levels=1:2'
# nginx['proxy_cache'] = 'gitlab'
# nginx['proxy_custom_buffer_size'] = '4k'
# nginx['http2_enabled'] = true
# nginx['real_ip_trusted_addresses'] = []
# nginx['real_ip_header'] = nil
# nginx['real_ip_recursive'] = nil
# nginx['custom_error_pages'] = {
#   '404' => {
#     'title' => 'Example title',
#     'header' => 'Example header',
#     'message' => 'Example message'
#   }
# }

### Advanced settings
# nginx['dir'] = "/var/opt/gitlab/nginx"
# nginx['log_directory'] = "/var/log/gitlab/nginx"
# nginx['error_log_level'] = "error"
# nginx['worker_processes'] = 4
# nginx['worker_connections'] = 10240
# nginx['log_format'] = '$remote_addr - $remote_user [$time_local] "$request_method $filtered_request_uri $server_protocol" $status $body_bytes_sent "$filtered_http_referer" "$http_user_agent" $gzip_ratio'
# nginx['sendfile'] = 'on'
# nginx['tcp_nopush'] = 'on'
# nginx['tcp_nodelay'] = 'on'
# nginx['hide_server_tokens'] = 'off'
# nginx['gzip_http_version'] = "1.0"
# nginx['gzip_comp_level'] = "2"
# nginx['gzip_proxied'] = "any"
# nginx['gzip_types'] = [ "text/plain", "text/css", "application/x-javascript", "text/xml", "application/xml", "application/xml+rss", "text/javascript", "application/json" ]
# nginx['keepalive_timeout'] = 65
# nginx['keepalive_time'] = '1h'
# nginx['cache_max_size'] = '5000m'
# nginx['server_names_hash_bucket_size'] = 64
##! These paths have proxy_request_buffering disabled
# nginx['request_buffering_off_path_regex'] = "/api/v\\d/jobs/\\d+/artifacts$|/import/gitlab_project$|\\.git/git-receive-pack$|\\.git/gitlab-lfs/objects|\\.git/info/lfs/objects/batch$"

### Nginx status
# nginx['status'] = {
#  "enable" => true,
#  "listen_addresses" => ["127.0.0.1"],
#  "fqdn" => "dev.example.com",
#  "port" => 9999,
#  "vts_enable" => true,
#  "options" => {
#    "server_tokens" => "off", # Don't show the version of NGINX
#    "access_log" => "off", # Disable logs for stats
#    "allow" => "127.0.0.1", # Only allow access from localhost
#    "deny" => "all" # Deny access to anyone else
#  }
# }

##! Service name used to register Nginx as a Consul service
# nginx['consul_service_name'] = 'nginx'
##! Semantic metadata used when registering NGINX as a Consul service
# nginx['consul_service_meta'] = {}

################################################################################
## GitLab Logging
##! Docs: https://docs.gitlab.com/omnibus/settings/logs.html
################################################################################

# logging['svlogd_size'] = 200 * 1024 * 1024 # rotate after 200 MB of log data
# logging['svlogd_num'] = 30 # keep 30 rotated log files
# logging['svlogd_timeout'] = 24 * 60 * 60 # rotate after 24 hours
# logging['svlogd_filter'] = "gzip" # compress logs with gzip
# logging['svlogd_udp'] = nil # transmit log messages via UDP
# logging['svlogd_prefix'] = nil # custom prefix for log messages
# logging['logrotate_frequency'] = "daily" # rotate logs daily
# logging['logrotate_maxsize'] = nil # rotate logs when they grow bigger than size bytes even before the specified time interval (daily, weekly, monthly, or yearly)
# logging['logrotate_size'] = nil # do not rotate by size by default
# logging['logrotate_rotate'] = 30 # keep 30 rotated logs
# logging['logrotate_compress'] = "compress" # see 'man logrotate'
# logging['logrotate_method'] = "copytruncate" # see 'man logrotate'
# logging['logrotate_postrotate'] = nil # no postrotate command by default
# logging['logrotate_dateformat'] = nil # use date extensions for rotated files rather than numbers e.g. a value of "-%Y-%m-%d" would give rotated files like production.log-2016-03-09.gz

### UDP log forwarding
##! Docs: http://docs.gitlab.com/omnibus/settings/logs.html#udp-log-forwarding

##! remote host to ship log messages to via UDP
# logging['udp_log_shipping_host'] = nil

##! override the hostname used when logs are shipped via UDP,
##  by default the system hostname will be used.
# logging['udp_log_shipping_hostname'] = nil

##! remote port to ship log messages to via UDP
# logging['udp_log_shipping_port'] = 514

################################################################################
## Logrotate
##! Docs: https://docs.gitlab.com/omnibus/settings/logs.html#logrotate
##! You can disable built in logrotate feature.
################################################################################
# logrotate['enable'] = true
# logrotate['log_directory'] = "/var/log/gitlab/logrotate"

################################################################################
## Users and groups accounts
##! Disable management of users and groups accounts.
##! **Set only if creating accounts manually**
##! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#disable-user-and-group-account-management
################################################################################

# manage_accounts['enable'] = true

################################################################################
## Storage directories
##! Disable managing storage directories
##! Docs: https://docs.gitlab.com/omnibus/settings/configuration.html#disable-storage-directories-management
################################################################################

##! **Set only if the select directories are created manually**
# manage_storage_directories['enable'] = false
# manage_storage_directories['manage_etc'] = false

################################################################################
## Runtime directory
##! Docs: https://docs.gitlab.com//omnibus/settings/configuration.html#configuring-runtime-directory
################################################################################

# runtime_dir '/run'

################################################################################
## Git
##! Advanced setting for configuring git system settings for omnibus-gitlab
##! internal git
################################################################################

##! For multiple options under one header use array of comma separated values,
##! eg.:
##! { "receive" => ["fsckObjects = true"], "alias" => ["st = status", "co = checkout"] }

# omnibus_gitconfig['system'] = {
#  "pack" => ["threads = 1"],
#  "receive" => ["fsckObjects = true", "advertisePushOptions = true"],
#  "repack" => ["writeBitmaps = true"],
#  "transfer" => ["hideRefs=^refs/tmp/", "hideRefs=^refs/keep-around/", "hideRefs=^refs/remotes/"],
#  "core" => [
#    'alternateRefsCommand="exit 0 #"',
#    "fsyncObjectFiles = true"
#  ],
#  "fetch" => ["writeCommitGraph = true"]
# }

################################################################################
## GitLab Pages
##! Docs: https://docs.gitlab.com/ee/administration/pages/
################################################################################

##! Define to enable GitLab Pages
# pages_external_url "http://pages.example.com/"
# gitlab_pages['enable'] = false

##! Configure to expose GitLab Pages on external IP address, serving the HTTP
# gitlab_pages['external_http'] = []

##! Configure to expose GitLab Pages on external IP address, serving the HTTPS
# gitlab_pages['external_https'] = []

##! Configure to expose GitLab Pages on external IP address, serving the HTTPS over PROXYv2
# gitlab_pages['external_https_proxyv2'] = []

##! Configure cert when using external IP address
# gitlab_pages['cert'] = "/etc/gitlab/ssl/#{Gitlab['gitlab_pages']['domain']}.crt"
# gitlab_pages['cert_key'] = "/etc/gitlab/ssl/#{Gitlab['gitlab_pages']['domain']}.key"

##! Configure to use the default list of cipher suites
# gitlab_pages['insecure_ciphers'] = false

##! Configure to enable health check endpoint on GitLab Pages
# gitlab_pages['status_uri'] = "/@status"

##! Tune the maximum number of concurrent connections GitLab Pages will handle.
##! Default to 0 for unlimited connections.
# gitlab_pages['max_connections'] = 0

##! Configure the maximum length of URIs accepted by GitLab Pages
##! By default is limited for security reasons. Set 0 for unlimited
# gitlab_pages['max_uri_length'] = 1024

##! Setting the propagate_correlation_id to true allows installations behind a reverse proxy
##! generate and set a correlation ID to requests sent to GitLab Pages. If a reverse proxy
##! sets the header value X-Request-ID, the value will be propagated in the request chain.
# gitlab_pages['propagate_correlation_id'] = false

##! Configure to use JSON structured logging in GitLab Pages
# gitlab_pages['log_format'] = "json"

##! Configure verbose logging for GitLab Pages
# gitlab_pages['log_verbose'] = false

##! Error Reporting and Logging with Sentry
# gitlab_pages['sentry_enabled'] = false
# gitlab_pages['sentry_dsn'] = 'https://<key>@sentry.io/<project>'
# gitlab_pages['sentry_environment'] = 'production'

##! Listen for requests forwarded by reverse proxy
# gitlab_pages['listen_proxy'] = "localhost:8090"

# gitlab_pages['redirect_http'] = true
# gitlab_pages['use_http2'] = true
# gitlab_pages['dir'] = "/var/opt/gitlab/gitlab-pages"
# gitlab_pages['log_directory'] = "/var/log/gitlab/gitlab-pages"

# gitlab_pages['artifacts_server'] = true
# gitlab_pages['artifacts_server_url'] = nil # Defaults to external_url + '/api/v4'
# gitlab_pages['artifacts_server_timeout'] = 10

##! Prometheus metrics for Pages docs: https://gitlab.com/gitlab-org/gitlab-pages/#enable-prometheus-metrics
# gitlab_pages['metrics_address'] = ":9235"

##! Specifies the minimum TLS version ("tls1.2" or "tls1.3")
# gitlab_pages['tls_min_version'] = "tls1.2"

##! Specifies the maximum TLS version ("tls1.2" or "tls1.3")
# gitlab_pages['tls_max_version'] = "tls1.3"

##! Pages access control
# gitlab_pages['access_control'] = false
# gitlab_pages['gitlab_id'] = nil # Automatically generated if not present
# gitlab_pages['gitlab_secret'] = nil # Generated if not present
# gitlab_pages['auth_redirect_uri'] = nil # Defaults to projects subdomain of pages_external_url and + '/auth'
# gitlab_pages['gitlab_server'] = nil # Defaults to external_url
# gitlab_pages['internal_gitlab_server'] = nil # Defaults to gitlab_server, can be changed to internal load balancer
# gitlab_pages['auth_secret'] = nil # Generated if not present
# gitlab_pages['auth_scope'] = nil # Defaults to api, can be changed to read_api to increase security

##! GitLab Pages Server Shutdown Timeout
##! Duration ("30s" for 30 seconds)
# gitlab_pages['server_shutdown_timeout'] = "30s"

##! GitLab API HTTP client connection timeout
# gitlab_pages['gitlab_client_http_timeout'] = "10s"

##! GitLab API JWT Token expiry time
# gitlab_pages['gitlab_client_jwt_expiry'] = "30s"

##! Advanced settings for API-based configuration for GitLab Pages.
##! The recommended default values are set inside GitLab Pages.
##! Should be changed only if absolutely needed.

##! The maximum time a domain's configuration is stored in the cache.
# gitlab_pages['gitlab_cache_expiry'] = "600s"
##! The interval at which a domain's configuration is set to be due to refresh (default: 60s).
# gitlab_pages['gitlab_cache_refresh'] = "60s"
##! The interval at which expired items are removed from the cache (default: 60s).
# gitlab_pages['gitlab_cache_cleanup'] = "60s"
##! The maximum time to wait for a response from the GitLab API per request.
# gitlab_pages['gitlab_retrieval_timeout'] = "30s"
##! The interval to wait before retrying to resolve a domain's configuration via the GitLab API.
# gitlab_pages['gitlab_retrieval_interval'] = "1s"
##! The maximum number of times to retry to resolve a domain's configuration via the API
# gitlab_pages['gitlab_retrieval_retries'] = 3

##! Define custom gitlab-pages HTTP headers for the whole instance
# gitlab_pages['headers'] = []

##! Shared secret used for authentication between Pages and GitLab
# gitlab_pages['api_secret_key'] = nil # Will be generated if not set. Base64 encoded and exactly 32 bytes long.

##! Advanced settings for serving GitLab Pages from zip archives.
##! The recommended default values are set inside GitLab Pages.
##! Should be changed only if absolutely needed.

##! The maximum time an archive will be cached in memory.
# gitlab_pages['zip_cache_expiration'] = "60s"
##! Zip archive cache cleaning interval.
# gitlab_pages['zip_cache_cleanup'] = "30s"
##! The interval to refresh a cache archive if accessed before expiring.
# gitlab_pages['zip_cache_refresh'] = "30s"
##! The maximum amount of time it takes to open a zip archive from the file system or object storage.
# gitlab_pages['zip_open_timeout'] = "30s"
##! Zip HTTP Client timeout
# gitlab_pages['zip_http_client_timeout'] = "30m"

##! ReadTimeout is the maximum duration for reading the entire request, including the body. A zero or negative value means there will be no timeout.
# gitlab_pages['server_read_timeout'] = "5s"
##! ReadHeaderTimeout is the amount of time allowed to read request headers. A zero or negative value means there will be no timeout.
# gitlab_pages['server_read_header_timeout'] = "1s"
##! WriteTimeout is the maximum duration before timing out writes of the response. A zero or negative value means there will be no timeout.
# gitlab_pages['server_write_timeout'] = "5m"
##! KeepAlive specifies the keep-alive period for network connections accepted by this listener. If zero, keep-alives are enabled if supported by the protocol and operating system. If negative, keep-alives are disabled.
# gitlab_pages['server_keep_alive'] = "15s"

##! Enable serving content from disk instead of Object Storage
# gitlab_pages['enable_disk'] = nil

##! Rate-limiting options below work in report-only mode:
##! they only count rejected requests, but don't reject them
##! enable `FF_ENABLE_RATE_LIMITER=true` environment variable to
##! reject requests.

##! Rate limits as described in https://docs.gitlab.com/ee/administration/pages/#rate-limits

##! Rate limit HTTP requests per second from a single IP, 0 means is disabled
# gitlab_pages['rate_limit_source_ip'] = 50.0
##! Rate limit HTTP requests from a single IP, maximum burst allowed per second
# gitlab_pages['rate_limit_source_ip_burst'] = 600
##! Rate limit HTTP requests per second to a single domain, 0 means is disabled
# gitlab_pages['rate_limit_domain'] = 0
##! Rate limit HTTP requests to a single domain, maximum burst allowed per second
# gitlab_pages['rate_limit_domain_burst'] = 10000

##! Rate limit new TLS connections per second from a single IP, 0 means is disabled
# gitlab_pages['rate_limit_tls_source_ip'] = 50.0
##! Rate limit new TLS connections from a single IP, maximum burst allowed per second
# gitlab_pages['rate_limit_tls_source_ip_burst'] = 600
##!Rate limit new TLS connections per second from to a single domain, 0 means is disabled
# gitlab_pages['rate_limit_tls_domain'] = 0
##! Rate limit new TLS connections to a single domain, maximum burst allowed per second
# gitlab_pages['rate_limit_tls_domain_burst'] = 10000

##! The maximum size of the _redirects file, in bytes
# gitlab_pages['redirects_max_config_size'] = 65536
##! The maximum number of path segments allowed in _redirects rules URLs
# gitlab_pages['redirects_max_path_segments'] = 25
##! The maximum number of rules allowed in _redirects
# gitlab_pages['redirects_max_rule_count'] = 1000

# gitlab_pages['env_directory'] = "/opt/gitlab/etc/gitlab-pages/env"
# gitlab_pages['env'] = {
#   'SSL_CERT_DIR' => "#{node['package']['install-dir']}/embedded/ssl/certs/"
# }

################################################################################
## GitLab Pages NGINX
################################################################################

# All the settings defined in the "GitLab Nginx" section are also available in
# this "GitLab Pages NGINX" section, using the key `pages_nginx`.  However,
# those settings should be explicitly set. That is, settings given as
# `nginx['some_setting']` WILL NOT be automatically replicated as
# `pages_nginx['some_setting']` and should be set separately.

# Below you can find settings that are exclusive to "GitLab Pages NGINX"
# pages_nginx['enable'] = true

# gitlab_rails['pages_path'] = "/var/opt/gitlab/gitlab-rails/shared/pages"

################################################################################
## GitLab CI
##! Docs: https://docs.gitlab.com/ee/ci/quick_start/
################################################################################

# gitlab_ci['gitlab_ci_all_broken_builds'] = true
# gitlab_ci['********************'] = true
# gitlab_ci['builds_directory'] = '/var/opt/gitlab/gitlab-ci/builds'

################################################################################
## GitLab Kubernetes Agent Server
##! Docs: https://gitlab.com/gitlab-org/cluster-integration/gitlab-agent/blob/master/README.md
################################################################################

##! Settings used by the GitLab application
# gitlab_rails['gitlab_kas_enabled'] = true
# gitlab_rails['gitlab_kas_external_url'] = 'ws://gitlab.example.com/-/kubernetes-agent/'
# gitlab_rails['gitlab_kas_internal_url'] = 'grpc://localhost:8153'
# gitlab_rails['gitlab_kas_external_k8s_proxy_url'] = 'https://gitlab.example.com/-/kubernetes-agent/'

##! Enable GitLab KAS
# gitlab_kas['enable'] = true

##! Agent configuration for GitLab KAS
# gitlab_kas['agent_configuration_poll_period'] = 20
# gitlab_kas['agent_gitops_poll_period'] = 20
# gitlab_kas['agent_gitops_project_info_cache_ttl'] = 300
# gitlab_kas['agent_gitops_project_info_cache_error_ttl'] = 60
# gitlab_kas['agent_info_cache_ttl'] = 300
# gitlab_kas['agent_info_cache_error_ttl'] = 60

##! Shared secret used for authentication between KAS and GitLab
# gitlab_kas['api_secret_key'] = nil # Will be generated if not set. Base64 encoded and exactly 32 bytes long.

##! Shared secret used for authentication between different KAS instances in a multi-node setup
# gitlab_kas['private_api_secret_key'] = nil # Will be generated if not set. Base64 encoded and exactly 32 bytes long.

##! Listen configuration for GitLab KAS
# gitlab_kas['listen_address'] = 'localhost:8150'
# gitlab_kas['listen_network'] = 'tcp'
# gitlab_kas['listen_websocket'] = true
# gitlab_kas['certificate_file'] = "/path/to/certificate.pem"
# gitlab_kas['key_file'] = "/path/to/key.pem"
# gitlab_kas['observability_listen_network'] = 'tcp'
# gitlab_kas['observability_listen_address'] = 'localhost:8151'
# gitlab_kas['internal_api_listen_network'] = 'tcp'
# gitlab_kas['internal_api_listen_address'] = 'localhost:8153'
# gitlab_kas['internal_api_certificate_file'] = "/path/to/certificate.pem"
# gitlab_kas['internal_api_key_file'] = "/path/to/key.pem"
# gitlab_kas['kubernetes_api_listen_address'] = 'localhost:8154'
# gitlab_kas['kubernetes_api_certificate_file'] = "/path/to/certificate.pem"
# gitlab_kas['kubernetes_api_key_file'] = "/path/to/key.pem"
# gitlab_kas['private_api_listen_network'] = 'tcp'
# gitlab_kas['private_api_listen_address'] = 'localhost:8155'
# gitlab_kas['private_api_certificate_file'] = "/path/to/certificate.pem"
# gitlab_kas['private_api_key_file'] = "/path/to/key.pem"

##! Metrics configuration for GitLab KAS
# gitlab_kas['metrics_usage_reporting_period'] = 60

##! Log configuration for GitLab KAS
# gitlab_kas['log_level'] = 'info'

##! Environment variables for GitLab KAS
# gitlab_kas['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/",
#   # In a multi-node setup, this address MUST be reachable from other KAS instances. In a single-node setup, it can be on localhost for simplicity
#   'OWN_PRIVATE_API_URL' => 'grpc://localhost:8155'
# }

##! Error Reporting and Logging with Sentry
# gitlab_kas['sentry_dsn'] = 'https://<key>@sentry.io/<project>'
# gitlab_kas['sentry_environment'] = 'production'

##! Directories for GitLab KAS
# gitlab_kas['dir'] = '/var/opt/gitlab/gitlab-kas'
# gitlab_kas['log_directory'] = '/var/log/gitlab/gitlab-kas'
# gitlab_kas['env_directory'] = '/opt/gitlab/etc/gitlab-kas/env'

################################################################################
## GitLab Mattermost
##! Docs: https://docs.gitlab.com/omnibus/gitlab-mattermost
################################################################################

# mattermost_external_url 'http://mattermost.example.com'

# mattermost['enable'] = false
# mattermost['username'] = 'mattermost'
# mattermost['group'] = 'mattermost'
# mattermost['uid'] = nil
# mattermost['gid'] = nil
# mattermost['home'] = '/var/opt/gitlab/mattermost'
# mattermost['database_name'] = 'mattermost_production'
# mattermost['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
# mattermost['service_address'] = "127.0.0.1"
# mattermost['service_port'] = "8065"
# mattermost['service_site_url'] = nil
# mattermost['service_allowed_untrusted_internal_connections'] = ""
# mattermost['service_enable_api_team_deletion'] = true
# mattermost['team_site_name'] = "GitLab Mattermost"
# mattermost['sql_driver_name'] = 'mysql'
# mattermost['sql_data_source'] = "mmuser:mostest@tcp(dockerhost:3306)/mattermost_test?charset=utf8mb4,utf8"
# mattermost['log_file_directory'] = '/var/log/gitlab/mattermost/'
# mattermost['gitlab_enable'] = false
# mattermost['gitlab_id'] = "12345656"
# mattermost['gitlab_secret'] = "123456789"
# mattermost['gitlab_scope'] = ""
# mattermost['gitlab_auth_endpoint'] = "http://gitlab.example.com/oauth/authorize"
# mattermost['gitlab_token_endpoint'] = "http://gitlab.example.com/oauth/token"
# mattermost['gitlab_user_api_endpoint'] = "http://gitlab.example.com/api/v4/user"
# mattermost['file_directory'] = "/var/opt/gitlab/mattermost/data"
# mattermost['plugin_directory'] = "/var/opt/gitlab/mattermost/plugins"
# mattermost['plugin_client_directory'] = "/var/opt/gitlab/mattermost/client-plugins"

################################################################################
## Mattermost NGINX
################################################################################

# All the settings defined in the "GitLab Nginx" section are also available in
# this "Mattermost NGINX" section, using the key `mattermost_nginx`.  However,
# those settings should be explicitly set. That is, settings given as
# `nginx['some_setting']` WILL NOT be automatically replicated as
# `mattermost_nginx['some_setting']` and should be set separately.

# Below you can find settings that are exclusive to "Mattermost NGINX"
# mattermost_nginx['enable'] = false

# mattermost_nginx['custom_gitlab_mattermost_server_config'] = "location ^~ /foo-namespace/bar-project/raw/ {\n deny all;\n}\n"
# mattermost_nginx['proxy_set_headers'] = {
#   "Host" => "$http_host",
#   "X-Real-IP" => "$remote_addr",
#   "X-Forwarded-For" => "$proxy_add_x_forwarded_for",
#   "X-Frame-Options" => "SAMEORIGIN",
#   "X-Forwarded-Proto" => "https",
#   "X-Forwarded-Ssl" => "on",
#   "Upgrade" => "$http_upgrade",
#   "Connection" => "$connection_upgrade"
# }


################################################################################
## Registry NGINX
################################################################################

# All the settings defined in the "GitLab Nginx" section are also available in
# this "Registry NGINX" section, using the key `registry_nginx`.  However, those
# settings should be explicitly set. That is, settings given as
# `nginx['some_setting']` WILL NOT be automatically replicated as
# `registry_nginx['some_setting']` and should be set separately.

# Below you can find settings that are exclusive to "Registry NGINX"
# registry_nginx['enable'] = false

# registry_nginx['proxy_set_headers'] = {
#  "Host" => "$http_host",
#  "X-Real-IP" => "$remote_addr",
#  "X-Forwarded-For" => "$proxy_add_x_forwarded_for",
#  "X-Forwarded-Proto" => "https",
#  "X-Forwarded-Ssl" => "on"
# }

# When the registry is automatically enabled using the same domain as `external_url`,
# it listens on this port
# registry_nginx['listen_port'] = 5050

################################################################################
## Prometheus
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/
################################################################################

###! **To enable only Monitoring service in this machine, uncomment
###!   the line below.**
###! Docs: https://docs.gitlab.com/ee/administration/high_availability
# monitoring_role['enable'] = true

# prometheus['enable'] = true
# prometheus['monitor_kubernetes'] = true
# prometheus['username'] = 'gitlab-prometheus'
# prometheus['group'] = 'gitlab-prometheus'
# prometheus['uid'] = nil
# prometheus['gid'] = nil
# prometheus['shell'] = '/bin/sh'
# prometheus['home'] = '/var/opt/gitlab/prometheus'
# prometheus['log_directory'] = '/var/log/gitlab/prometheus'
# prometheus['rules_files'] = ['/var/opt/gitlab/prometheus/rules/*.rules']
# prometheus['scrape_interval'] = 15
# prometheus['scrape_timeout'] = 15
# prometheus['external_labels'] = { }
# prometheus['env_directory'] = '/opt/gitlab/etc/prometheus/env'
# prometheus['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
#
### Custom scrape configs
#
# Prometheus can scrape additional jobs via scrape_configs.  The default automatically
# includes all of the exporters supported by the omnibus config.
#
# See: https://prometheus.io/docs/operating/configuration/#<scrape_config>
#
# Example:
#
# prometheus['scrape_configs'] = [
#   {
#     'job_name': 'example',
#     'static_configs' => [
#       'targets' => ['hostname:port'],
#     ],
#   },
# ]
#
### Custom alertmanager config
#
# To configure external alertmanagers, create an alertmanager config.
#
# See: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#alertmanager_config
#
# prometheus['alertmanagers'] = [
#   {
#     'static_configs' => [
#       {
#         'targets' => [
#           'hostname:port'
#         ]
#       }
#     ]
#   }
# ]
#
### Custom Prometheus flags
#
# prometheus['flags'] = {
#   'storage.tsdb.path' => "/var/opt/gitlab/prometheus/data",
#   'storage.tsdb.retention.time' => "15d",
#   'config.file' => "/var/opt/gitlab/prometheus/prometheus.yml"
# }

##! Advanced settings. Should be changed only if absolutely needed.
# prometheus['listen_address'] = 'localhost:9090'
#

##! Service name used to register Prometheus as a Consul service
# prometheus['consul_service_name'] = 'prometheus'
##! Semantic metadata used when registering Prometheus as a Consul service
# prometheus['consul_service_meta'] = {}

################################################################################
###! **Only needed if Prometheus and Rails are not on the same server.**
### For example, in a multi-node architecture, Prometheus will be installed on the monitoring node, while Rails will be on the Rails node.
### https://docs.gitlab.com/ee/administration/monitoring/prometheus/index.html#using-an-external-prometheus-server
### This value should be the address at which Prometheus is available to a GitLab Rails(Puma, Sidekiq) node.
################################################################################
# gitlab_rails['prometheus_address'] = 'your.prom:9090'

################################################################################
## Prometheus Alertmanager
################################################################################

# alertmanager['enable'] = true
# alertmanager['home'] = '/var/opt/gitlab/alertmanager'
# alertmanager['log_directory'] = '/var/log/gitlab/alertmanager'
# alertmanager['admin_email'] = '<EMAIL>'
# alertmanager['flags'] = {
#   'web.listen-address' => "localhost:9093",
#   'storage.path' => "/var/opt/gitlab/alertmanager/data",
#   'config.file' => "/var/opt/gitlab/alertmanager/alertmanager.yml"
# }
# alertmanager['env_directory'] = '/opt/gitlab/etc/alertmanager/env'
# alertmanager['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }

##! Advanced settings. Should be changed only if absolutely needed.
# alertmanager['listen_address'] = 'localhost:9093'
# alertmanager['global'] = {}

################################################################################
## Prometheus Node Exporter
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/node_exporter.html
################################################################################

# node_exporter['enable'] = true
# node_exporter['home'] = '/var/opt/gitlab/node-exporter'
# node_exporter['log_directory'] = '/var/log/gitlab/node-exporter'
# node_exporter['flags'] = {
#   'collector.textfile.directory' => "/var/opt/gitlab/node-exporter/textfile_collector"
# }
# node_exporter['env_directory'] = '/opt/gitlab/etc/node-exporter/env'
# node_exporter['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }

##! Advanced settings. Should be changed only if absolutely needed.
# node_exporter['listen_address'] = 'localhost:9100'

##! Service name used to register Node Exporter as a Consul service
# node_exporter['consul_service_name'] = 'node-exporter'
##! Semantic metadata used when registering Node Exporter as a Consul service
# node_exporter['consul_service_meta'] = {}

################################################################################
## Prometheus Redis exporter
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/redis_exporter.html
################################################################################

# redis_exporter['enable'] = true
# redis_exporter['log_directory'] = '/var/log/gitlab/redis-exporter'
# redis_exporter['flags'] = {
#   'redis.addr' => "unix:///var/opt/gitlab/redis/redis.socket",
# }
# redis_exporter['env_directory'] = '/opt/gitlab/etc/redis-exporter/env'
# redis_exporter['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }

##! Advanced settings. Should be changed only if absolutely needed.
# redis_exporter['listen_address'] = 'localhost:9121'

##! Service name used to register Redis Exporter as a Consul service
# redis_exporter['consul_service_name'] = 'redis-exporter'
##! Semantic metadata used when registering Redis Exporter as a Consul service
# redis_exporter['consul_service_meta'] = {}

################################################################################
## Prometheus Postgres exporter
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/postgres_exporter.html
################################################################################

# postgres_exporter['enable'] = true
# postgres_exporter['home'] = '/var/opt/gitlab/postgres-exporter'
# postgres_exporter['log_directory'] = '/var/log/gitlab/postgres-exporter'
# postgres_exporter['flags'] = {}
# postgres_exporter['listen_address'] = 'localhost:9187'
# postgres_exporter['env_directory'] = '/opt/gitlab/etc/postgres-exporter/env'
# postgres_exporter['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
# postgres_exporter['sslmode'] = nil
# postgres_exporter['per_table_stats'] = false

##! Service name used to register Postgres Exporter as a Consul service
# postgres_exporter['consul_service_name'] = 'postgres-exporter'
##! Semantic metadata used when registering Postgres Exporter as a Consul service
# postgres_exporter['consul_service_meta'] = {}

################################################################################
## Prometheus PgBouncer exporter (EE only)
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/pgbouncer_exporter.html
################################################################################

# pgbouncer_exporter['enable'] = false
# pgbouncer_exporter['log_directory'] = "/var/log/gitlab/pgbouncer-exporter"
# pgbouncer_exporter['listen_address'] = 'localhost:9188'
# pgbouncer_exporter['env_directory'] = '/opt/gitlab/etc/pgbouncer-exporter/env'
# pgbouncer_exporter['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }

################################################################################
## Prometheus Gitlab exporter
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/gitlab_exporter.html
################################################################################


# gitlab_exporter['enable'] = true
# gitlab_exporter['log_directory'] = "/var/log/gitlab/gitlab-exporter"
# gitlab_exporter['home'] = "/var/opt/gitlab/gitlab-exporter"

##! Advanced settings. Should be changed only if absolutely needed.
# gitlab_exporter['server_name'] = 'webrick'
# gitlab_exporter['listen_address'] = 'localhost'
# gitlab_exporter['listen_port'] = '9168'

##! TLS settings.
# gitlab_exporter['tls_enabled'] = false
# gitlab_exporter['tls_cert_path'] = '/etc/gitlab/ssl/gitlab-exporter.crt'
# gitlab_exporter['tls_key_path'] = '/etc/gitlab/ssl/gitlab-exporter.key'

##! Prometheus scrape related configs
# gitlab_exporter['prometheus_scrape_scheme'] = 'http'
# gitlab_exporter['prometheus_scrape_tls_server_name'] = 'localhost'
# gitlab_exporter['prometheus_scrape_tls_skip_verification'] = false

##! Manage gitlab-exporter sidekiq probes. false by default when Sentinels are
##! found.
# gitlab_exporter['probe_sidekiq'] = true

##! Manage gitlab-exporter elasticsearch probes. Add authorization header if security
##! is enabled.
# gitlab_exporter['probe_elasticsearch'] = false
# gitlab_exporter['elasticsearch_url'] = 'http://localhost:9200'
# gitlab_exporter['elasticsearch_authorization'] = 'Basic <yourbase64encodedcredentials>'

##! Service name used to register GitLab Exporter as a Consul service
# gitlab_exporter['consul_service_name'] = 'gitlab-exporter'
##! Semantic metadata used when registering GitLab Exporter as a Consul service
# gitlab_exporter['consul_service_meta'] = {}

# To completely disable prometheus, and all of it's exporters, set to false
# prometheus_monitoring['enable'] = true

################################################################################
## Grafana Dashboards
##! Docs: https://docs.gitlab.com/ee/administration/monitoring/prometheus/#prometheus-as-a-grafana-data-source
################################################################################

# grafana['enable'] = false
# grafana['log_directory'] = '/var/log/gitlab/grafana'
# grafana['home'] = '/var/opt/gitlab/grafana'
# grafana['admin_password'] = 'admin'
# grafana['allow_user_sign_up'] = false
# grafana['basic_auth_enabled'] = false
# grafana['disable_login_form'] = true
# grafana['gitlab_application_id'] = 'GITLAB_APPLICATION_ID'
# grafana['gitlab_secret'] = 'GITLAB_SECRET'
# grafana['env_directory'] = '/opt/gitlab/etc/grafana/env'
# grafana['allowed_groups'] = []
# grafana['gitlab_auth_sign_up'] = true
# grafana['env'] = {
#   'SSL_CERT_DIR' => "#{node['package']['install-dir']}/embedded/ssl/certs/"
# }
# grafana['metrics_enabled'] = false
# grafana['metrics_basic_auth_username'] = 'grafana_metrics' # default: nil
# grafana['metrics_basic_auth_password'] = 'please_set_a_unique_password' # default: nil
# grafana['alerting_enabled'] = false

### SMTP Configuration
#
# See: http://docs.grafana.org/administration/configuration/#smtp
#
# grafana['smtp'] = {
#   'enabled' => true,
#   'host' => 'localhost:25',
#   'user' => nil,
#   'password' => nil,
#   'cert_file' => nil,
#   'key_file' => nil,
#   'skip_verify' => false,
#   'from_address' => '<EMAIL>',
#   'from_name' => 'Grafana',
#   'ehlo_identity' => 'dashboard.example.com',
#   'startTLS_policy' => nil
# }

# Grafana usage reporting defaults to gitlab_rails['usage_ping_enabled']
# grafana['reporting_enabled'] = true

### Dashboards
#
# See: http://docs.grafana.org/administration/provisioning/#dashboards
#
# NOTE: Setting this will override the default.
#
# grafana['dashboards'] = [
#   {
#     'name' => 'GitLab Omnibus',
#     'orgId' => 1,
#     'folder' => 'GitLab Omnibus',
#     'type' => 'file',
#     'disableDeletion' => true,
#     'updateIntervalSeconds' => 600,
#     'options' => {
#       'path' => '/opt/gitlab/embedded/service/grafana-dashboards',
#     }
#   }
# ]

### Datasources
#
# See: http://docs.grafana.org/administration/provisioning/#example-datasource-config-file
#
# NOTE: Setting this will override the default.
#
# grafana['datasources'] = [
#   {
#     'name' => 'GitLab Omnibus',
#     'type' => 'prometheus',
#     'access' => 'proxy',
#     'url' => 'http://localhost:9090'
#   }
# ]

##! Advanced settings. Should be changed only if absolutely needed.
# grafana['http_addr'] = 'localhost'
# grafana['http_port'] = 3000

################################################################################
## Gitaly
##! Docs:
################################################################################

# The gitaly['enable'] option exists for the purpose of cluster
# deployments, see https://docs.gitlab.com/ee/administration/gitaly/index.html .
# gitaly['enable'] = true
# gitaly['dir'] = "/var/opt/gitlab/gitaly"
# gitaly['log_directory'] = "/var/log/gitlab/gitaly"
# gitaly['bin_path'] = "/opt/gitlab/embedded/bin/gitaly"
# gitaly['env_directory'] = "/opt/gitlab/etc/gitaly/env"
# gitaly['env'] = {
#  'PATH' => "/opt/gitlab/bin:/opt/gitlab/embedded/bin:/bin:/usr/bin",
#  'HOME' => '/var/opt/gitlab',
#  'TZ' => ':/etc/localtime',
#  'PYTHONPATH' => "/opt/gitlab/embedded/lib/python3.9/site-packages",
#  'ICU_DATA' => "/opt/gitlab/embedded/share/icu/current",
#  'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/",
#  'WRAPPER_JSON_LOGGING' => true
# }

# gitaly['runtime_dir'] = "/var/opt/gitlab/gitaly/run"
# gitaly['socket_path'] = "/var/opt/gitlab/gitaly/gitaly.socket"
# gitaly['listen_addr'] = "localhost:8075"
# gitaly['tls_listen_addr'] = "localhost:9075"
# gitaly['certificate_path'] = "/var/opt/gitlab/gitaly/certificate.pem"
# gitaly['key_path'] = "/var/opt/gitlab/gitaly/key.pem"
# gitaly['gpg_signing_key_path'] = "/var/opt/gitlab/gitaly/signing_key.gpg"
# gitaly['prometheus_listen_addr'] = "localhost:9236"
# gitaly['logging_level'] = "warn"
# gitaly['logging_format'] = "json"
# gitaly['logging_sentry_dsn'] = "https://<key>:<secret>@sentry.io/<project>"
# gitaly['logging_ruby_sentry_dsn'] = "https://<key>:<secret>@sentry.io/<project>"
# gitaly['logging_sentry_environment'] = "production"
# gitaly['prometheus_grpc_latency_buckets'] = "[0.001, 0.005, 0.025, 0.1, 0.5, 1.0, 10.0, 30.0, 60.0, 300.0, 1500.0]"
# gitaly['auth_token'] = '<secret>'
# gitaly['auth_transitioning'] = false # When true, auth is logged to Prometheus but NOT enforced
# gitaly['graceful_restart_timeout'] = '1m' # Grace time for a gitaly process to finish ongoing requests
# gitaly['git_catfile_cache_size'] = 100 # Number of 'git cat-file' processes kept around for re-use
# gitaly['git_bin_path'] = "/opt/gitlab/embedded/bin/git" # A custom path for the 'git' executable
# gitaly['use_bundled_git'] = true # Whether to use bundled Git.
# gitaly['open_files_ulimit'] = 15000 # Maximum number of open files allowed for the gitaly process
# gitaly['ruby_max_rss'] = 300000000 # RSS threshold in bytes for triggering a gitaly-ruby restart
# gitaly['ruby_graceful_restart_timeout'] = '10m' # Grace time for a gitaly-ruby process to finish ongoing requests
# gitaly['ruby_restart_delay'] = '5m' # Period of sustained high RSS that needs to be observed before restarting gitaly-ruby
# gitaly['ruby_num_workers'] = 3 # Number of gitaly-ruby worker processes. Minimum 2, default 2.
# gitaly['concurrency'] = [
#   {
#     'rpc' => "/gitaly.SmartHTTPService/PostReceivePack",
#     'max_per_repo' => 20
#   }, {
#     'rpc' => "/gitaly.SSHService/SSHUploadPack",
#     'max_per_repo' => 5
#   }
# ]
# gitaly['rate_limiting'] = [
#   {
#     'rpc' => "/gitaly.SmartHTTPService/PostReceivePack",
#     'interval' => '1m',
#     'burst' => 10
#   }, {
#     'rpc' => "/gitaly.SSHService/SSHUploadPack",
#     'interval' => '1m',
#     'burst' => 5
#   }
# ]
#
# gitaly['daily_maintenance_start_hour'] = 22
# gitaly['daily_maintenance_start_minute'] = 30
# gitaly['daily_maintenance_duration'] = '30m'
# gitaly['daily_maintenance_storages'] = ["default"]
# gitaly['daily_maintenance_disabled'] = false
# gitaly['cgroups_mountpoint'] = '/sys/fs/cgroup'
# gitaly['cgroups_hierarchy_root'] = 'gitaly'
# gitaly['cgroups_memory_bytes'] = 1048576
# gitaly['cgroups_cpu_shares'] = 512
# gitaly['cgroups_repositories_count'] = 1000
# gitaly['cgroups_repositories_memory_bytes'] = 12884901888
# gitaly['cgroups_repositories_cpu_shares'] = 128
# gitaly['pack_objects_cache_enabled'] = true
# gitaly['pack_objects_cache_dir'] = '/var/opt/gitlab/git-data/repositories/+gitaly/PackObjectsCache'
# gitaly['pack_objects_cache_max_age'] = '5m'
# gitaly['custom_hooks_dir'] = "/var/opt/gitlab/gitaly/custom_hooks"

##! Service name used to register Gitaly as a Consul service
# gitaly['consul_service_name'] = 'gitaly'
##! Semantic metadata used when registering Gitaly as a Consul service
# gitaly['consul_service_meta'] = {}

################################################################################
## Praefect
##! Docs: https://gitlab.com/gitlab-org/gitaly/blob/master/doc/design_ha.md
################################################################################

# praefect['enable'] = false
# praefect['dir'] = "/var/opt/gitlab/praefect"
# praefect['log_directory'] = "/var/log/gitlab/praefect"
# praefect['env_directory'] = "/opt/gitlab/etc/praefect/env"
# praefect['env'] = {
#  'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/",
#  'GITALY_PID_FILE' => "/var/opt/gitlab/praefect/praefect.pid",
#  'WRAPPER_JSON_LOGGING' => true
# }
# praefect['wrapper_path'] = "/opt/gitlab/embedded/bin/gitaly-wrapper"
# praefect['failover_enabled'] = true
# praefect['failover_timeout'] = "10s"
# praefect['auth_token'] = ""
# praefect['auth_transitioning'] = false
# praefect['listen_addr'] = "localhost:2305"
# praefect['tls_listen_addr'] = "localhost:3305"
# praefect['certificate_path'] = "/var/opt/gitlab/prafect/certificate.pem"
# praefect['key_path'] = "/var/opt/gitlab/prafect/key.pem"
# praefect['prometheus_listen_addr'] = "localhost:9652"
# praefect['prometheus_grpc_latency_buckets'] = "[0.001, 0.005, 0.025, 0.1, 0.5, 1.0, 10.0, 30.0, 60.0, 300.0, 1500.0]"
# praefect['separate_database_metrics'] = true
# praefect['logging_level'] = "warn"
# praefect['logging_format'] = "json"
# praefect['virtual_storages'] = {
#   'default' => {
#     'default_replication_factor' => 3,
#     'nodes' => {
#       'praefect-internal-0' => {
#         'address' => 'tcp://***********:8075',
#         'token' => 'abc123'
#       },
#       'praefect-internal-1' => {
#         'address' => 'tcp://***********:8075',
#         'token' => 'xyz456'
#       }
#     }
#   },
#   'alternative' => {
#     'nodes' => {
#       'praefect-internal-2' => {
#         'address' => 'tcp://**********:8075',
#         'token' => 'abc321'
#       },
#       'praefect-internal-3' => {
#         'address' => 'tcp://**********:8075',
#         'token' => 'xyz890'
#       }
#     }
#   }
# }
# praefect['background_verification_verification_interval'] = "72h"
# praefect['background_verification_delete_invalid_records'] = false
# praefect['sentry_dsn'] = "https://<key>:<secret>@sentry.io/<project>"
# praefect['sentry_environment'] = "production"
# praefect['auto_migrate'] = true
# praefect['database_host'] = 'postgres.external'
# praefect['database_port'] = 6432
# praefect['database_user'] = 'praefect'
# praefect['database_password'] = 'secret'
# praefect['database_dbname'] = 'praefect_production'
# praefect['database_sslmode'] = 'disable'
# praefect['database_sslcert'] = '/path/to/client-cert'
# praefect['database_sslkey'] = '/path/to/client-key'
# praefect['database_sslrootcert'] = '/path/to/rootcert'
# praefect['reconciliation_scheduling_interval'] = '5m'
# praefect['reconciliation_histogram_buckets'] = '[0.001, 0.005, 0.025, 0.1, 0.5, 1.0, 10.0]'
# praefect['database_direct_host'] = 'postgres.internal'
# praefect['database_direct_port'] = 5432
# praefect['database_direct_user'] = 'praefect'
# praefect['database_direct_password'] = 'secret'
# praefect['database_direct_dbname'] = 'praefect_production_direct'
# praefect['database_direct_sslmode'] = 'disable'
# praefect['database_direct_sslcert'] = '/path/to/client-cert'
# praefect['database_direct_sslkey'] = '/path/to/client-key'
# praefect['database_direct_sslrootcert'] = '/path/to/rootcert'

##! Service name used to register Praefect as a Consul service
# praefect['consul_service_name'] = 'praefect'
##! Semantic metadata used when registering Praefect as a Consul service
# praefect['consul_service_meta'] = {}

################################################################################
# Storage check
################################################################################
# storage_check['enable'] = false
# storage_check['target'] = 'unix:///var/opt/gitlab/gitlab-rails/sockets/gitlab.socket'
# storage_check['log_directory'] = '/var/log/gitlab/storage-check'

################################################################################
# Let's Encrypt integration
################################################################################
# letsencrypt['enable'] = nil
# letsencrypt['contact_emails'] = [] # This should be an array of email addresses to add as contacts
# letsencrypt['group'] = 'root'
# letsencrypt['key_size'] = 2048
# letsencrypt['owner'] = 'root'
# letsencrypt['wwwroot'] = '/var/opt/gitlab/nginx/www'
# See http://docs.gitlab.com/omnibus/settings/ssl.html#automatic-renewal for more on these sesttings
# letsencrypt['auto_renew'] = true
# letsencrypt['auto_renew_hour'] = 0
# letsencrypt['auto_renew_minute'] = nil # Should be a number or cron expression, if specified.
# letsencrypt['auto_renew_day_of_month'] = "*/4"
# letsencrypt['auto_renew_log_directory'] = '/var/log/gitlab/lets-encrypt'

##! Turn off automatic init system detection. To skip init detection in
##! non-docker containers. Recommended not to change.
# package['detect_init'] = true

##! Attempt to modify kernel paramaters. To skip this in containers where the
##! relevant file system is read-only, set the value to false.
# package['modify_kernel_parameters'] = true

##! Specify maximum number of tasks that can be created by the systemd unit
##! Will be populated as TasksMax value to the unit file if user is on a systemd
##! version that supports it (>= 227). Will be a no-op if user is not on systemd.
# package['systemd_tasks_max'] = 4915

##! Settings to configure order of GitLab's systemd unit.
##! Note: We do not recommend changing these values unless absolutely necessary
# package['systemd_after'] = 'multi-user.target'
# package['systemd_wanted_by'] = 'multi-user.target'
################################################################################
################################################################################
##                  Configuration Settings for GitLab EE only                 ##
################################################################################
################################################################################


################################################################################
## Auxiliary cron jobs applicable to GitLab EE only
################################################################################
#
# gitlab_rails['geo_repository_sync_worker_cron'] = "*/5 * * * *"
# gitlab_rails['geo_secondary_registry_consistency_worker'] = "* * * * *"
# gitlab_rails['geo_secondary_usage_data_cron_worker'] = "0 0 * * 0"
# gitlab_rails['geo_prune_event_log_worker_cron'] = "*/5 * * * *"
# gitlab_rails['geo_repository_verification_primary_batch_worker_cron'] = "*/5 * * * *"
# gitlab_rails['geo_repository_verification_secondary_scheduler_worker_cron'] = "*/5 * * * *"
# gitlab_rails['ldap_sync_worker_cron'] = "30 1 * * *"
# gitlab_rails['ldap_group_sync_worker_cron'] = "0 * * * *"
# gitlab_rails['historical_data_worker_cron'] = "0 12 * * *"
# gitlab_rails['elastic_index_bulk_cron'] = "*/1 * * * *"
# gitlab_rails['analytics_devops_adoption_create_all_snapshots_worker_cron'] = "0 4 * * 0"
# gitlab_rails['ci_runners_stale_group_runners_prune_worker_cron'] = "30 * * * *"

################################################################################
## Kerberos (EE Only)
##! Docs: https://docs.gitlab.com/ee/integration/kerberos.html#http-git-access
################################################################################

# gitlab_rails['kerberos_enabled'] = true
# gitlab_rails['kerberos_keytab'] = /etc/http.keytab
# gitlab_rails['kerberos_service_principal_name'] = HTTP/<EMAIL>
# gitlab_rails['kerberos_simple_ldap_linking_allowed_realms'] = ['example.com','kerberos.example.com']
# gitlab_rails['kerberos_use_dedicated_port'] = true
# gitlab_rails['kerberos_port'] = 8443
# gitlab_rails['kerberos_https'] = true

################################################################################
## Package repository
##! Docs: https://docs.gitlab.com/ee/administration/packages/
################################################################################

# gitlab_rails['packages_enabled'] = true
# gitlab_rails['packages_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/packages"
# gitlab_rails['packages_object_store_enabled'] = false
# gitlab_rails['packages_object_store_proxy_download'] = false
# gitlab_rails['packages_object_store_remote_directory'] = "packages"
# gitlab_rails['packages_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

################################################################################
## Dependency proxy
##! Docs: https://docs.gitlab.com/ee/administration/packages/dependency_proxy.html
################################################################################

# gitlab_rails['dependency_proxy_enabled'] = true
# gitlab_rails['dependency_proxy_storage_path'] = "/var/opt/gitlab/gitlab-rails/shared/dependency_proxy"
# gitlab_rails['dependency_proxy_object_store_enabled'] = false
# gitlab_rails['dependency_proxy_object_store_proxy_download'] = false
# gitlab_rails['dependency_proxy_object_store_remote_directory'] = "dependency_proxy"
# gitlab_rails['dependency_proxy_object_store_connection'] = {
#   'provider' => 'AWS',
#   'region' => 'eu-west-1',
#   'aws_access_key_id' => 'AWS_ACCESS_KEY_ID',
#   'aws_secret_access_key' => 'AWS_SECRET_ACCESS_KEY',
#   # # The below options configure an S3 compatible host instead of AWS
#   # 'host' => 's3.amazonaws.com',
#   # 'aws_signature_version' => 4, # For creation of signed URLs. Set to 2 if provider does not support v4.
#   # 'endpoint' => 'https://s3.amazonaws.com', # default: nil - Useful for S3 compliant services such as DigitalOcean Spaces
#   # 'path_style' => false # Use 'host/bucket_name/object' instead of 'bucket_name.host/object'
# }

################################################################################
## GitLab Sentinel (EE Only)
##! Docs: http://docs.gitlab.com/ce/administration/high_availability/redis.html#high-availability-with-sentinel
################################################################################

##! **Make sure you configured all redis['master_*'] keys above before
##!   continuing.**

##! To enable Sentinel and disable all other services in this machine,
##! uncomment the line below (if you've enabled Redis role, it will keep it).
##! Docs: https://docs.gitlab.com/ee/administration/high_availability/redis.html
# redis_sentinel_role['enable'] = true

# sentinel['enable'] = true

##! Bind to all interfaces, uncomment to specify an IP and bind to a single one
# sentinel['bind'] = '0.0.0.0'

##! Uncomment to change default port
# sentinel['port'] = 26379

#### Support to run sentinels in a Docker or NAT environment
#####! Docs: https://redis.io/topics/sentinel#sentinel-docker-nat-and-possible-issues
# In an standard case, Sentinel will run in the same network service as Redis, so the same IP will be announce for Redis and Sentinel
# Only define these values if it is needed to announce for Sentinel a differen IP service than Redis
# sentinel['announce_ip'] = nil # If not defined, its value will be taken from redis['announce_ip'] or nil if not present
# sentinel['announce_port'] = nil # If not defined, its value will be taken from sentinel['port'] or nil if redis['announce_ip'] not present

##! Quorum must reflect the amount of voting sentinels it take to start a
##! failover.
##! **Value must NOT be greater then the amount of sentinels.**
##! The quorum can be used to tune Sentinel in two ways:
##! 1. If a the quorum is set to a value smaller than the majority of Sentinels
##!    we deploy, we are basically making Sentinel more sensible to master
##!    failures, triggering a failover as soon as even just a minority of
##!    Sentinels is no longer able to talk with the master.
##! 2. If a quorum is set to a value greater than the majority of Sentinels, we
##!    are making Sentinel able to failover only when there are a very large
##!    number (larger than majority) of well connected Sentinels which agree
##!    about the master being down.
# sentinel['quorum'] = 1

### Consider unresponsive server down after x amount of ms.
# sentinel['down_after_milliseconds'] = 10000

### Specifies the failover timeout in milliseconds.
##! It is used in many ways:
##!
##! - The time needed to re-start a failover after a previous failover was
##!   already tried against the same master by a given Sentinel, is two
##!   times the failover timeout.
##!
##! - The time needed for a replica replicating to a wrong master according
##!   to a Sentinel current configuration, to be forced to replicate
##!   with the right master, is exactly the failover timeout (counting since
##!   the moment a Sentinel detected the misconfiguration).
##!
##! - The time needed to cancel a failover that is already in progress but
##!   did not produced any configuration change (REPLICAOF NO ONE yet not
##!   acknowledged by the promoted replica).
##!
##! - The maximum time a failover in progress waits for all the replicas to be
##!   reconfigured as replicas of the new master. However even after this time
##!   the replicas will be reconfigured by the Sentinels anyway, but not with
##!   the exact parallel-syncs progression as specified.
# sentinel['failover_timeout'] = 60000

### Sentinel TLS settings
###! To run Sentinel over TLS, specify values for the following settings
# sentinel['tls_port'] = nil
# sentinel['tls_cert_file'] = nil
# sentinel['tls_key_file'] = nil

###! Other TLS related optional settings
# sentinel['tls_dh_params_file'] = nil
# sentinel['tls_ca_cert_dir'] = '/opt/gitlab/embedded/ssl/certs/'
# sentinel['tls_ca_cert_file'] = '/opt/gitlab/embedded/ssl/certs/cacert.pem'
# sentinel['tls_auth_clients'] = 'optional'
# sentinel['tls_replication'] = nil
# sentinel['tls_cluster'] = nil
# sentinel['tls_protocols'] = nil
# sentinel['tls_ciphers'] = nil
# sentinel['tls_ciphersuites'] = nil
# sentinel['tls_prefer_server_ciphers'] = nil
# sentinel['tls_session_caching'] = nil
# sentinel['tls_session_cache_size'] = nil
# sentinel['tls_session_cache_timeout'] = nil

### Sentinel hostname support
###! When enabled, Redis will leverage hostname support
###! Generally this does not need to be changed as we determine this based on
###! the provided input from `redis['announce_ip']`
###! * This is configured to `true` when a fully qualified hostname is provided
###! * This is configured to `false` when an IP address is provided
# sentinel['use_hostnames'] = <calculated>

################################################################################
## Additional Database Settings (EE only)
##! Docs: https://docs.gitlab.com/ee/administration/database_load_balancing.html
################################################################################
# gitlab_rails['db_load_balancing'] = { 'hosts' => ['secondary1.example.com'] }

################################################################################
## GitLab Geo
##! Docs: https://docs.gitlab.com/ee/gitlab-geo
################################################################################
##! Geo roles 'geo_primary_role' and 'geo_secondary_role' are set above with
##! other roles. For more information, see: https://docs.gitlab.com/omnibus/roles/index.html#roles.

# This is an optional identifier which Geo nodes can use to identify themselves.
# For example, if external_url is the same for two secondaries, you must specify
# a unique Geo node name for those secondaries.
#
# If it is blank, it defaults to external_url.
# gitlab_rails['geo_node_name'] = nil

# gitlab_rails['geo_registry_replication_enabled'] = true
# gitlab_rails['geo_registry_replication_primary_api_url'] = 'https://example.com:5050'


################################################################################
## GitLab Geo Secondary (EE only)
################################################################################
# geo_secondary['auto_migrate'] = true
# geo_secondary['db_adapter'] = "postgresql"
# geo_secondary['db_encoding'] = "unicode"
# geo_secondary['db_collation'] = nil
# geo_secondary['db_database'] = "gitlabhq_geo_production"
# geo_secondary['db_username'] = "gitlab_geo"
# geo_secondary['db_password'] = nil
# geo_secondary['db_host'] = "/var/opt/gitlab/geo-postgresql"
# geo_secondary['db_port'] = 5431
# geo_secondary['db_socket'] = nil
# geo_secondary['db_sslmode'] = nil
# geo_secondary['db_sslcompression'] = 0
# geo_secondary['db_sslrootcert'] = nil
# geo_secondary['db_sslca'] = nil
# geo_secondary['db_prepared_statements'] = false
# geo_secondary['db_database_tasks'] = true

################################################################################
## GitLab Geo Secondary Tracking Database (EE only)
################################################################################

# geo_postgresql['enable'] = false
# geo_postgresql['ha'] = false
# geo_postgresql['dir'] = '/var/opt/gitlab/geo-postgresql'
# geo_postgresql['pgbouncer_user'] = nil
# geo_postgresql['pgbouncer_user_password'] = nil
##! `SQL_USER_PASSWORD_HASH` can be generated using the command `gitlab-ctl pg-password-md5 gitlab`
# geo_postgresql['sql_user_password'] = 'SQL_USER_PASSWORD_HASH'
# geo_postgresql['log_directory'] = '/var/log/gitlab/geo-postgresql'

##! Automatically restart PostgreSQL service when version changes.
# geo_postgresql['auto_restart_on_version_change'] = true

################################################################################
## GitLab Geo Log Cursor Daemon (EE only)
################################################################################

# geo_logcursor['log_directory'] = '/var/log/gitlab/geo-logcursor'

################################################################################
## Unleash
##! These settings are for GitLab internal use.
##! They are used to control feature flags during GitLab development.
##! Docs: https://docs.gitlab.com/ee/development/feature_flags
################################################################################
# gitlab_rails['feature_flags_unleash_enabled'] = false
# gitlab_rails['feature_flags_unleash_url'] = nil
# gitlab_rails['feature_flags_unleash_app_name'] = nil
# gitlab_rails['feature_flags_unleash_instance_id'] = nil

################################################################################
# Pgbouncer (EE only)
# See [GitLab PgBouncer documentation](http://docs.gitlab.com/omnibus/settings/database.html#enabling-pgbouncer-ee-only)
# See the [PgBouncer page](https://pgbouncer.github.io/config.html) for details
################################################################################
# pgbouncer['enable'] = false
# pgbouncer['log_directory'] = '/var/log/gitlab/pgbouncer'
# pgbouncer['data_directory'] = '/var/opt/gitlab/pgbouncer'
# pgbouncer['env_directory'] = '/opt/gitlab/etc/pgbouncer/env'
# pgbouncer['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
# pgbouncer['listen_addr'] = '0.0.0.0'
# pgbouncer['listen_port'] = '6432'
# pgbouncer['pool_mode'] = 'transaction'
# pgbouncer['server_reset_query'] = 'DISCARD ALL'
# pgbouncer['application_name_add_host'] = '1'
# pgbouncer['max_client_conn'] = '2048'
# pgbouncer['default_pool_size'] = '100'
# pgbouncer['min_pool_size'] = '0'
# pgbouncer['reserve_pool_size'] = '5'
# pgbouncer['reserve_pool_timeout'] = '5.0'
# pgbouncer['server_round_robin'] = '0'
# pgbouncer['log_connections'] = '0'
# pgbouncer['server_idle_timeout'] = '30'
# pgbouncer['dns_max_ttl'] = '15.0'
# pgbouncer['dns_zone_check_period'] = '0'
# pgbouncer['dns_nxdomain_ttl'] = '15.0'
# pgbouncer['admin_users'] = %w(gitlab-psql postgres pgbouncer)
# pgbouncer['stats_users'] = %w(gitlab-psql postgres pgbouncer)
# pgbouncer['ignore_startup_parameters'] = 'extra_float_digits'
# pgbouncer['databases'] = {
#   DATABASE_NAME: {
#     host: HOSTNAME,
#     port: PORT
#     user: USERNAME,
#     password: PASSWORD
###! generate this with `echo -n '$password + $username' | md5sum`
#   }
#   ...
# }
# pgbouncer['logfile'] = nil
# pgbouncer['unix_socket_dir'] = nil
# pgbouncer['unix_socket_mode'] = '0777'
# pgbouncer['unix_socket_group'] = nil
# pgbouncer['auth_type'] = 'md5'
# pgbouncer['auth_hba_file'] = nil
# pgbouncer['auth_query'] = 'SELECT username, password FROM public.pg_shadow_lookup($1)'
# pgbouncer['users'] = {
#   USERNAME: {
#     'password': MD5_PASSWORD_HASH,
#   }
# }
# postgresql['pgbouncer_user'] = nil
# postgresql['pgbouncer_user_password'] = nil
# pgbouncer['server_reset_query_always'] = 0
# pgbouncer['server_check_query'] = 'select 1'
# pgbouncer['server_check_delay'] = 30
# pgbouncer['max_db_connections'] = nil
# pgbouncer['max_user_connections'] = nil
# pgbouncer['syslog'] = 0
# pgbouncer['syslog_facility'] = 'daemon'
# pgbouncer['syslog_ident'] = 'pgbouncer'
# pgbouncer['log_disconnections'] = 1
# pgbouncer['log_pooler_errors'] = 1
# pgbouncer['stats_period'] = 60
# pgbouncer['verbose'] = 0
# pgbouncer['server_lifetime'] = 3600
# pgbouncer['server_connect_timeout'] = 15
# pgbouncer['server_login_retry'] = 15
# pgbouncer['query_timeout'] = 0
# pgbouncer['query_wait_timeout'] = 120
# pgbouncer['client_idle_timeout'] = 0
# pgbouncer['client_login_timeout'] = 60
# pgbouncer['autodb_idle_timeout'] = 3600
# pgbouncer['suspend_timeout'] = 10
# pgbouncer['idle_transaction_timeout'] = 0
# pgbouncer['pkt_buf'] = 4096
# pgbouncer['listen_backlog'] = 128
# pgbouncer['sbuf_loopcnt'] = 5
# pgbouncer['max_packet_size'] = 2147483647
# pgbouncer['tcp_defer_accept'] = 0
# pgbouncer['tcp_socket_buffer'] = 0
# pgbouncer['tcp_keepalive'] = 1
# pgbouncer['tcp_keepcnt'] = 0
# pgbouncer['tcp_keepidle'] = 0
# pgbouncer['tcp_keepintvl'] = 0
# pgbouncer['disable_pqexec'] = 0

## Pgbouncer client TLS options
# pgbouncer['client_tls_sslmode'] = 'disable'
# pgbouncer['client_tls_ca_file'] = nil
# pgbouncer['client_tls_key_file'] = nil
# pgbouncer['client_tls_cert_file'] = nil
# pgbouncer['client_tls_protocols'] = 'all'
# pgbouncer['client_tls_dheparams'] = 'auto'
# pgbouncer['client_tls_ecdhcurve'] = 'auto'
#
## Pgbouncer server  TLS options
# pgbouncer['server_tls_sslmode'] = 'disable'
# pgbouncer['server_tls_ca_file'] = nil
# pgbouncer['server_tls_key_file'] = nil
# pgbouncer['server_tls_cert_file'] = nil
# pgbouncer['server_tls_protocols'] = 'all'
# pgbouncer['server_tls_ciphers'] = 'fast'

################################################################################
# Patroni (EE only)
################################################################################
# patroni['enable'] = false

# patroni['dir'] = '/var/opt/gitlab/patroni'
# patroni['ctl_command'] = '/opt/gitlab/embedded/bin/patronictl'

## Patroni dynamic configuration settings
# patroni['loop_wait'] = 10
# patroni['ttl'] = 30
# patroni['retry_timeout'] = 10
# patroni['maximum_lag_on_failover'] = 1_048_576
# patroni['max_timelines_history'] = 0
# patroni['master_start_timeout'] = 300
# patroni['use_pg_rewind'] = true
# patroni['remove_data_directory_on_rewind_failure'] = false
# patroni['remove_data_directory_on_diverged_timelines'] = false
# patroni['use_slots'] = true
# patroni['replication_password'] = nil
# patroni['replication_slots'] = {}
# patroni['callbacks'] = {}
# patroni['recovery_conf'] = {}
# patroni['tags'] = {}

## Standby cluster replication settings
# patroni['standby_cluster']['enable'] = false
# patroni['standby_cluster']['host'] = nil
# patroni['standby_cluster']['port'] = 5432
# patroni['standby_cluster']['primary_slot_name'] = nil

## Global/Universal settings
# patroni['scope'] = 'gitlab-postgresql-ha'
# patroni['name'] = nil

## Log settings
# patroni['log_directory'] = '/var/log/gitlab/patroni'
# patroni['log_level'] = 'INFO'

## Consul specific settings
# patroni['consul']['url'] = 'http://127.0.0.1:8500'
# patroni['consul']['service_check_interval'] = '10s'
# patroni['consul']['register_service'] = true
# patroni['consul']['checks'] = []

## PostgreSQL configuration override
# patroni['postgresql']['hot_standby'] = 'on'

## The following must hold the same values on all nodes.
## Leave unassined to use PostgreSQL's default values.
# patroni['postgresql']['wal_level'] = 'replica'
# patroni['postgresql']['wal_log_hints'] = 'on'
# patroni['postgresql']['max_worker_processes'] = 8
# patroni['postgresql']['max_locks_per_transaction'] = 64
# patroni['postgresql']['max_connections'] = 200
# patroni['postgresql']['checkpoint_timeout'] = 30

## The following can hold different values on all nodes.
## Leave unassined to use PostgreSQL's default values.
# patroni['postgresql']['wal_keep_segments'] = 8
# patroni['postgresql']['max_wal_senders'] = 5
# patroni['postgresql']['max_replication_slots'] = 5

## Permanent replication slots for Streaming Replication
# patroni['replication_slots'] = {
#   'geo_secondary' => { 'type' => 'physical' }
# }

## The address and port that Patroni API binds to and listens on.
# patroni['listen_address'] = nil
# patroni['port'] = '8008'

## The address of the Patroni node that is advertized to other cluster
## members to communicate with its API and PostgreSQL. If it is not specified,
## it tries to use the first available private IP and falls back to the default
## network interface.
# patroni['connect_address'] = nil

## The port that Patroni API responds to other cluster members. This port is
## advertized and by default is the same as patroni['port'].
# patroni['connect_port'] = '8008'

## Specifies the set of hosts that are allowed to call unsafe REST API endpoints.
## Each item can be an hostname, IP address, or CIDR address.
## All hosts are allowed if this is unset.
# patroni['allowlist'] = []
# patroni['allowlist_include_members'] = false

## The username and password to use for basic auth on write commands to the
## Patroni API. If not specified then the API does not use basic auth.
# patroni['username'] = nil
# patroni['password'] = nil

## TLS configuration for Patroni API. Both certificate and key files are
## required to enable TLS. If not specified then the API uses plain HTTP.
# patroni['tls_certificate_file'] = nil
# patroni['tls_key_file'] = nil
# patroni['tls_key_password'] = nil
# patroni['tls_ca_file'] = nil
# patroni['tls_ciphers'] = nil
# patroni['tls_client_mode'] = nil
# patroni['tls_client_certificate_file'] = nil
# patroni['tls_client_key_file'] = nil
# patroni['tls_verify'] = true

################################################################################
# Consul (EEP only)
################################################################################
# consul['enable'] = false
# consul['dir'] = '/var/opt/gitlab/consul'
# consul['username'] = 'gitlab-consul'
# consul['group'] = 'gitlab-consul'
# consul['config_file'] = '/var/opt/gitlab/consul/config.json'
# consul['config_dir'] = '/var/opt/gitlab/consul/config.d'
# consul['data_dir'] = '/var/opt/gitlab/consul/data'
# consul['log_directory'] = '/var/log/gitlab/consul'
# consul['env_directory'] = '/opt/gitlab/etc/consul/env'
# consul['env'] = {
#   'SSL_CERT_DIR' => "/opt/gitlab/embedded/ssl/certs/"
# }
# consul['monitoring_service_discovery'] = false
# consul['node_name'] = nil
# consul['script_directory'] = '/var/opt/gitlab/consul/scripts'
# consul['configuration'] = {
#   'client_addr' => nil,
#   'datacenter' => 'gitlab_consul',
#   'enable_script_checks' => true,
#   'server' => false
# }
# consul['services'] = []
# consul['service_config'] = {
#   'postgresql' => {
#     'service' => {
#       'name' => "postgresql",
#       'address' => '',
#       'port' => 5432,
#       'checks' => [
#         {
#           'script' => "/var/opt/gitlab/consul/scripts/check_postgresql",
#           'interval' => "10s"
#         }
#       ]
#     }
#   }
# }
# consul['watchers'] = []
#
# consul['custom_config_dir'] = '/path/to/service/configs/directory'
#

#### HTTP API ports
# consul['http_port'] = nil
# consul['https_port'] = nil

#### Gossip encryption
# consul['encryption_key'] = nil
# consul['encryption_verify_incoming'] = nil
# consul['encryption_verify_outgoing'] = nil

#### TLS settings
# consul['use_tls'] = false
# consul['tls_ca_file'] = nil
# consul['tls_certificate_file'] = nil
# consul['tls_key_file'] = nil
# consul['tls_verify_client'] = nil

################################################################################
# Service desk email settings
################################################################################
### Service desk email
###! Allow users to create new service desk issues by sending an email to
###! service desk address.
###! Docs: https://docs.gitlab.com/ee/user/project/service_desk.html
# gitlab_rails['service_desk_email_enabled'] = false

#### Service Desk Mailbox Settings (via `mail_room`)
#### Service Desk Email Address
####! The email address including the `%{key}` placeholder that will be replaced
####! to reference the item being replied to.
####! **The placeholder can be omitted but if present, it must appear in the
####!   "user" part of the address (before the `@`).**
# gitlab_rails['service_desk_email_address'] = "contact_project+%{key}@gmail.com"

#### Service Desk Email account username
####! **With third party providers, this is usually the full email address.**
####! **With self-hosted email servers, this is usually the user part of the
####!   email address.**
# gitlab_rails['service_desk_email_email'] = "<EMAIL>"

#### Service Desk Email account password
# gitlab_rails['service_desk_email_password'] = "[REDACTED]"

####! The mailbox where service desk mail will end up. Usually "inbox".
# gitlab_rails['service_desk_email_mailbox_name'] = "inbox"
####! The IDLE command timeout.
# gitlab_rails['service_desk_email_idle_timeout'] = 60
####! The file name for internal `mail_room` JSON logfile
# gitlab_rails['service_desk_email_log_file'] = "/var/log/gitlab/mailroom/mail_room_json.log"

#### Service Desk IMAP Settings
# gitlab_rails['service_desk_email_host'] = "imap.gmail.com"
# gitlab_rails['service_desk_email_port'] = 993
# gitlab_rails['service_desk_email_ssl'] = true
# gitlab_rails['service_desk_email_start_tls'] = false

#### Inbox options (for Microsoft Graph)
# gitlab_rails['service_desk_email_inbox_method'] = 'microsoft_graph'
# gitlab_rails['service_desk_email_inbox_options'] = {
#    'tenant_id': 'YOUR-TENANT-ID',
#    'client_id': 'YOUR-CLIENT-ID',
#    'client_secret': 'YOUR-CLIENT-SECRET',
#    'poll_interval': 60  # Optional
# }

#### How service desk emails are delivered to Rails process. Accept either
#### sidekiq or webhook. The default config is webhook.
# gitlab_rails['service_desk_email_delivery_method'] = "webhook"

#### Token to authenticate webhook requests. The token must be exactly 32 bytes,
#### encoded with base64
# gitlab_rails['service_desk_email_auth_token'] = nil

################################################################################
## Spamcheck (EE only)
#################################################################################

# spamcheck['enable'] = false
# spamcheck['dir'] = '/var/opt/gitlab/spamcheck'
# spamcheck['port'] = 8001
# spamcheck['external_port'] = nil
# spamcheck['monitoring_address'] = ':8003'
# spamcheck['log_level'] = 'info'
# spamcheck['log_format'] = 'json'
# spamcheck['log_output'] = 'stdout'
# spamcheck['monitor_mode'] = false
# spamcheck['allowlist'] = {}
# spamcheck['denylist'] = {}
# spamcheck['log_directory'] = "/var/log/gitlab/spamcheck"
# spamcheck['env_directory'] = "/opt/gitlab/etc/spamcheck/env"
# spamcheck['env'] = {
#   'SSL_CERT_DIR' => '/opt/gitlab/embedded/ssl/cers'
# }
# spamcheck['classifier']['log_directory'] = "/var/log/gitlab/spam-classifier"


external_url 'https://gitlab.ibootz.com'
gitlab_rails['time_zone'] = 'Asia/Shanghai'
nginx['listen_port'] = 80
nginx['listen_https'] = false
nginx['redirect_http_to_https'] = false
nginx['ssl_certificate'] = "/etc/gitlab/ssl/ibootz.pem"
nginx['ssl_certificate_key'] = "/etc/gitlab/ssl/ibootz.key"
nginx['proxy_set_headers'] = {
    "Host" => "$http_host_with_default",
    "X-Real-IP" => "$remote_addr",
    "X-Forwarded-For" => "$proxy_add_x_forwarded_for",
    "X-Forwarded-Proto" => "http",
    "Upgrade" => "$http_upgrade",
    "Connection" => "$connection_upgrade"
}

gitlab_rails['gitlab_shell_ssh_port'] = 22

#ssl 采用手动配置证书的方式
letsencrypt['enable'] = false

gitlab_workhorse['image_scaler_max_filesize'] = 2500000

# 电子邮件相关功能
gitlab_rails['smtp_enable'] = true
# 开启之后似乎会导致不断重启mail_room的问题
#gitlab_rails['incoming_email_enabled'] = true
gitlab_rails['gitlab_email_enabled'] = true
gitlab_rails['smtp_tls'] = true
gitlab_rails['smtp_address'] = "smtp.163.com"
gitlab_rails['smtp_port'] = 465
gitlab_rails['smtp_user_name'] = "<EMAIL>"
gitlab_rails['smtp_password'] = "BQKMUCJRGPVGQIAK"
gitlab_rails['smtp_domain'] = "163.com"
gitlab_rails['smtp_authentication'] = "login"
gitlab_rails['smtp_enable_starttls_auto'] = true
gitlab_rails['gitlab_email_from'] = "<EMAIL>"
gitlab_rails['gitlab_email_display_name'] = "Gitlab"
gitlab_rails['gitlab_email_reply_to'] = "<EMAIL>"

# CI
gitlab_ci['gitlab_ci_all_broken_builds'] = true
gitlab_ci['********************'] = true

# Terraform
gitlab_rails['terraform_state_enabled'] = true

# 打开容器仓库功能
gitlab_rails['gitlab_default_projects_features_container_registry'] = true
gitlab_rails['registry_enabled'] = true
registry['enable'] = true
registry_external_url "https://registry.ibootz.com"
registry_nginx['enable'] = true
registry_nginx['listen_port'] = 5050
registry_nginx['listen_https'] = false
registry_nginx['redirect_http_to_https'] = false
registry_nginx['ssl_certificate'] = "/etc/gitlab/ssl/ibooz.pem"
registry_nginx['ssl_certificate_key'] = "/etc/gitlab/ssl/ibootz.key"
nginx['proxy_set_headers'] = {
    "Host" => "$http_host_with_default",
    "X-Real-IP" => "$remote_addr",
    "X-Forwarded-For" => "$proxy_add_x_forwarded_for",
    "X-Forwarded-Proto" => "http",
    "Upgrade" => "$http_upgrade",
    "Connection" => "$connection_upgrade"
}

# 包仓库
gitlab_rails['packages_enabled'] = true
gitlab_rails['dependency_proxy_enabled'] = true

# GitLab KAS
gitlab_kas['enable'] = true
gitlab_rails['gitlab_kas_enabled'] = true
gitlab_rails['gitlab_kas_external_url'] = "ws://gitlab.ibootz.com/-/kubernetes-agent"

# Mattermost
mattermost['enable'] = false
mattermost_nginx['enable'] = false

# Kerberos
gitlab_rails['kerberos_enabled'] = false
sentinel['enable'] = false

# GitLab Pages
gitlab_pages['enable'] = false
pages_nginx['enable'] = false

# 禁用 PUMA 集群模式
puma['worker_processes'] = 2
puma['min_threads'] = 4
puma['max_threads'] = 4

# 降低后台守护进程并发数
sidekiq['max_concurrency'] = 4

# Usage Statistics
gitlab_rails['usage_ping_enabled'] = false
gitlab_rails['sentry_enabled'] = false
grafana['reporting_enabled'] = false

# 关闭监控
prometheus['enable'] = false
prometheus_monitoring['enable'] = false
alertmanager['enable'] = false
node_exporter['enable'] = false
redis_exporter['enable'] = false
postgres_exporter['enable'] = false
pgbouncer_exporter['enable'] = false
gitlab_exporter['enable'] = false
grafana['enable'] = false
sidekiq['metrics_enabled'] = false
