import asyncio
import os
import httpx
from datetime import datetime

from src.config import (
    CONTESTANT_MODELS,
    JUDGE_MODELS,
    EVALUATION_DIMENSIONS,
    RESULTS_DIR,
    get_config_summary,
)
from src.llm_client import get_model_response, get_judge_rating
from src.report_generator import generate_report

async def main():
    """
    运行LLM评测流程的主函数。
    """
    print("启动LLM评测流程...")
    print(get_config_summary())

    # 1. 从用户处获取问题
    question = input("请输入您想让模型回答的问题:\n> ")
    if not question.strip():
        print("问题不能为空。正在退出。")
        return

    # 2. 为本次运行创建一个唯一的目录
    timestamp = datetime.now().strftime("%Y-%m-%d_%H%M%S")
    run_dir = os.path.join(RESULTS_DIR, timestamp)
    answers_dir = os.path.join(run_dir, "answers")
    os.makedirs(answers_dir, exist_ok=True)
    print(f"已为本次运行创建结果目录: {run_dir}")

    async with httpx.AsyncClient() as client:
        # 3. 并发获取所有参赛模型的回答
        print("\n--- 阶段 1: 从参赛模型获取回答 ---")
        contestant_tasks = [get_model_response(client, model, question) for model in CONTESTANT_MODELS]
        answers = await asyncio.gather(*contestant_tasks)

        # 筛选出失败的请求，并为评分做准备
        valid_answers = []
        for model_name, answer_text in zip(CONTESTANT_MODELS, answers):
            if answer_text:
                # 注意：这里的 "answer" 键是硬编码在 `prompts.py` 中的，所以保持英文
                valid_answers.append({"model_name": model_name, "answer": answer_text})
                # 将每个回答（可能包含思考过程）保存到文件
                try:
                    answer_file_path = os.path.join(answers_dir, f"{model_name.replace('/', '_')}.txt")
                    with open(answer_file_path, "w", encoding="utf-8") as f:
                        f.write(answer_text)
                    print(f"已将 {model_name} 的回答保存至 {answer_file_path}")
                except IOError as e:
                    print(f"保存 {model_name} 的回答时出错: {e}")

        if not valid_answers:
            print("未能从任何参赛模型处收到有效回答。正在退出。")
            return

        # 4. 并发获取所有裁判模型的评分
        print("\n--- 阶段 2: 从裁判模型获取评分 ---")
        all_ratings = {}
        judge_tasks = [get_judge_rating(client, judge_model, question, valid_answers, EVALUATION_DIMENSIONS) for judge_model in JUDGE_MODELS]
        rating_results = await asyncio.gather(*judge_tasks)

        for judge_model, rating in zip(JUDGE_MODELS, rating_results):
            if rating:
                all_ratings[judge_model] = rating
            else:
                print(f"裁判模型 {judge_model} 未能返回有效评分，将跳过。")

        if not all_ratings:
            print("未能从任何裁判模型处收到有效评分。评测无法继续，正在退出。")
            return

        # 5. 生成最终报告
        print("\n--- 阶段 3: 生成最终报告 ---")
        generate_report(question, all_ratings, run_dir)

    print("\n评测流程已完成。")

if __name__ == "__main__":
    # 确保根结果目录存在
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户中断了流程。正在退出。")
