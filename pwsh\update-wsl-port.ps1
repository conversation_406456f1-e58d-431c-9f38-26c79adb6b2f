# --- PowerShell 脚本开始 ---
# 该脚本只有使用NAT网络模式下才需要


# 1. 设置你想转发的端口
# Windows 监听的端口
$windowsPort = 2222 
# WSL 内部服务的端口 (例如 SSH 的 22)
$wslPort = 22     

# 2. 自动获取 WSL 的 IP 地址
# 这行命令会运行 wsl, 执行 ip addr, 然后通过复杂的文本处理精确找到 IP
$wslIP = (wsl -e ip addr | Select-String -Pattern '(?<=inet\s)[\d\.]+(?=\/.*scope global eth0)' -AllMatches).Matches.Value

# 3. 检查是否成功获取到 IP
if ($null -eq $wslIP) {
    Write-Error "错误：无法获取 WSL 的 IP 地址。请确保 WSL 正在运行。"
    # 等待用户按键后退出，以便用户能看到错误信息
    Read-Host "按 Enter 键退出"
    exit
}

Write-Host "成功获取到 WSL IP: $wslIP"

# 4. 设置 Windows 防火墙规则 (如果不存在的话)
$ruleName = "WSL Port Forwarding for port $windowsPort"
if (-not (Get-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue)) {
    Write-Host "防火墙规则 '$ruleName' 不存在，正在创建..."
    New-NetFirewallRule -DisplayName $ruleName -Direction Inbound -Action Allow -Protocol TCP -LocalPort $windowsPort
} else {
    Write-Host "防火墙规则 '$ruleName' 已存在。"
}


# 5. 更新端口转发规则
Write-Host "正在更新端口转发规则..."
# 先尝试删除可能存在的旧规则，忽略可能发生的错误（比如规则不存在）
netsh interface portproxy delete v4tov4 listenport=$windowsPort listenaddress=0.0.0.0 | Out-Null
# 添加新的规则
netsh interface portproxy add v4tov4 listenport=$windowsPort listenaddress=0.0.0.0 connectport=$wslPort connectaddress=$wslIP

Write-Host "操作完成！现在应该可以通过 你的WindowsIP:$windowsPort 访问 WSL。"
# 等待用户按键后退出
Read-Host "按 Enter 键退出"
# --- PowerShell 脚本结束 ---