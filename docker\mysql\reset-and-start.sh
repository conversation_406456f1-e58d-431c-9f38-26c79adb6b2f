#!/bin/bash
#
# 该脚本用于完全重置并重新启动 MySQL 服务。
# 它会删除所有现有数据，请谨慎使用！
#

# 如果任何命令失败，则立即退出脚本
set -e

# 切换到脚本所在的目录，以确保 docker-compose.yml 文件可以被找到
cd "$(dirname "$0")"

# 1. 停止并删除现有容器
echo "INFO: Stopping and removing existing containers..."
docker compose down

# 2. 删除旧的数据目录
echo "INFO: Removing old data directory..."
if [ -d "./data" ]; then
    sudo rm -rf ./data
fi

# 3. 创建新的数据目录
echo "INFO: Creating new data directory..."
mkdir ./data

# 4. 设置正确的目录所有者
echo "INFO: Setting permissions for data directory..."
sudo chown -R 999:999 ./data

# 5. 启动服务
echo "INFO: Starting MySQL container in detached mode..."
docker compose up -d

# 6. 等待并显示日志
echo "INFO: Waiting for container to initialize..."
sleep 15 # 等待15秒，给数据库足够的时间来完成初始化

echo "INFO: Displaying container logs:"
docker compose logs --tail=50 # 显示最后50行日志

echo "
SUCCESS: MySQL reset and startup process completed."
