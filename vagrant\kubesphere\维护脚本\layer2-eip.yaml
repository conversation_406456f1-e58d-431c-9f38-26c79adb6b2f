### openelb负载均衡器 eip配置文件
apiVersion: network.kubesphere.io/v1alpha2
kind: Eip
metadata:
  #名称
  name: layer2-eip
  #配置默认的eip，下次使用安装应用使用loadbalancer时则不需要重复添加该注解
  annotations:
    eip.openelb.kubesphere.io/is-default-eip: layer2-eip
spec:
  # address 必须与Kubernetes 集群节点在同一网段。一个或多个 IP   地址，将被 OpenELB 使用，可以用**********/24,也可以是单个的，也可以是************-************这种，一下用单个的，暴露服务的时候可以用这个，由于二层网络只能使用同一个网段的地址，切换成bgp模式后则可以使用自定义的网段
  address: ***************-***************
  # 网卡
  interface: eth1
  #协议
  protocol: layer2
