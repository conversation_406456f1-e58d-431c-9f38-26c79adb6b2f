#!/bin/bash

# Git项目还原脚本
# 此脚本由collect_git_projects.sh自动生成
# 功能：根据收集的信息克隆或更新Git项目
# 特性：支持幂等操作，智能冲突处理，安全的仓库更新

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    cat << 'HELP_EOF'
Git项目还原脚本

用法: $0 [基础目录] [预演模式] [冲突策略]

参数:
  基础目录     项目还原的根目录 (默认: 当前目录)
  预演模式     true=仅预览不执行, false=实际执行 (默认: false)
  冲突策略     处理目录冲突的策略 (默认: ask)

冲突处理策略:
  ask      交互式询问如何处理每个冲突 (推荐)
  skip     跳过所有冲突项目
  backup   自动备份冲突目录后继续
  force    强制覆盖冲突目录 (危险)

功能特性:
  ✓ 幂等操作: 可重复执行而不会破坏现有项目
  ✓ URL验证: 检查现有仓库URL是否匹配
  ✓ 安全更新: 检测未提交更改，避免数据丢失
  ✓ 智能合并: 自动执行快进合并
  ✓ 分支管理: 自动切换到目标分支

使用示例:
  $0                                    # 交互模式还原到当前目录
  $0 /home/<USER>/projects                # 交互模式还原到指定目录
  $0 /target true                       # 预览模式，查看将要执行的操作
  $0 /target false skip                 # 自动跳过冲突项目
  $0 /target false backup               # 自动备份冲突后继续

注意事项:
  - 首次使用建议先执行预览模式
  - backup策略会创建带时间戳的备份目录
  - force策略会删除冲突目录，请谨慎使用
  - 脚本会保护有未提交更改的仓库

HELP_EOF
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
esac

# 配置变量
BASE_DIR="${1:-$(pwd)}"
DRY_RUN="${2:-false}"

log_info "Git项目还原脚本启动"
log_info "基础目录: $BASE_DIR"
log_info "预演模式: $DRY_RUN"

# 创建基础目录
if [[ "$DRY_RUN" != "true" ]]; then
    mkdir -p "$BASE_DIR"
fi


# 项目信息列表
declare -a PROJECTS=(
    # 目录: ai/AI_Agent_and_Frameworks
    "ai/AI_Agent_and_Frameworks/AgentGPT|https://github.com/reworkd/AgentGPT.git|main"
    "ai/AI_Agent_and_Frameworks/agenticSeek|https://github.com/Fosowl/agenticSeek.git|main"
    "ai/AI_Agent_and_Frameworks/agent-zero|https://github.com/agent0ai/agent-zero.git|main"
    "ai/AI_Agent_and_Frameworks/agno|https://github.com/agno-agi/agno.git|main"
    "ai/AI_Agent_and_Frameworks/autogen|https://github.com/microsoft/autogen.git|main"
    "ai/AI_Agent_and_Frameworks/ChatDev|https://github.com/OpenBMB/ChatDev.git|main"
    "ai/AI_Agent_and_Frameworks/crewAI|https://github.com/crewAIInc/crewAI.git|main"
    "ai/AI_Agent_and_Frameworks/Flowise|https://github.com/FlowiseAI/Flowise.git|main"
    "ai/AI_Agent_and_Frameworks/langchain|https://github.com/langchain-ai/langchain.git|master"
    "ai/AI_Agent_and_Frameworks/langgraph|https://github.com/langchain-ai/langgraph.git|main"
    "ai/AI_Agent_and_Frameworks/langgraph-supervisor-py|https://github.com/langchain-ai/langgraph-supervisor-py.git|main"
    "ai/AI_Agent_and_Frameworks/MetaGPT|https://github.com/FoundationAgents/MetaGPT.git|main"
    "ai/AI_Agent_and_Frameworks/spring-ai-alibaba-examples|https://github.com/springaialibaba/spring-ai-alibaba-examples.git|main"
    "ai/AI_Agent_and_Frameworks/spring-ai-alibaba|https://github.com/alibaba/spring-ai-alibaba.git|main"
    "ai/AI_Agent_and_Frameworks/UI-TARS-desktop|https://github.com/bytedance/UI-TARS-desktop.git|main"

    # 目录: ai/AI_for_Software_Engineering
    "ai/AI_for_Software_Engineering/aider|https://github.com/Aider-AI/aider.git|main"
    "ai/AI_for_Software_Engineering/browser-use|https://github.com/browser-use/browser-use.git|main"
    "ai/AI_for_Software_Engineering/gemini-cli|https://github.com/google-gemini/gemini-cli.git|main"
    "ai/AI_for_Software_Engineering/kilocode|https://github.com/Kilo-Org/kilocode.git|main"
    "ai/AI_for_Software_Engineering/OpenHands|https://github.com/All-Hands-AI/OpenHands.git|main"

    # 目录: ai/Data_Intelligence_and_Research
    "ai/Data_Intelligence_and_Research/deep-research|https://github.com/dzhng/deep-research.git|main"
    "ai/Data_Intelligence_and_Research/graphiti|https://github.com/getzep/graphiti.git|main"
    "ai/Data_Intelligence_and_Research/local-deep-researcher|https://github.com/langchain-ai/local-deep-researcher.git|main"
    "ai/Data_Intelligence_and_Research/maigret|https://github.com/soxoj/maigret.git|main"
    "ai/Data_Intelligence_and_Research/open_deep_research|https://github.com/langchain-ai/open_deep_research.git|main"
    "ai/Data_Intelligence_and_Research/qlib|https://github.com/microsoft/qlib.git|main"
    "ai/Data_Intelligence_and_Research/vanna|https://github.com/vanna-ai/vanna.git|main"

    # 目录: ai/Domain_Specific_AI_Agents
    "ai/Domain_Specific_AI_Agents/agenticSeek|https://github.com/Fosowl/agenticSeek.git|main"
    "ai/Domain_Specific_AI_Agents/gpt-researcher|https://github.com/assafelovic/gpt-researcher.git|master"
    "ai/Domain_Specific_AI_Agents/Jobs_Applier_AI_Agent_AIHawk|https://github.com/feder-cr/Jobs_Applier_AI_Agent_AIHawk.git|main"
    "ai/Domain_Specific_AI_Agents/MediaCrawler|https://github.com/NanmiCoder/MediaCrawler.git|main"
    "ai/Domain_Specific_AI_Agents/social-media-agent|https://github.com/langchain-ai/social-media-agent.git|main"
    "ai/Domain_Specific_AI_Agents/suna|https://github.com/kortix-ai/suna.git|main"
    "ai/Domain_Specific_AI_Agents/TradingAgents|https://github.com/TauricResearch/TradingAgents.git|main"

    # 目录: ai/Platforms_and_Infrastructure
    "ai/Platforms_and_Infrastructure/ai-pdf-chatbot-langchain|https://github.com/mayooear/ai-pdf-chatbot-langchain.git|main"
    "ai/Platforms_and_Infrastructure/anything-llm|https://github.com/Mintplex-Labs/anything-llm.git|master"
    "ai/Platforms_and_Infrastructure/awesome-llm-apps|https://github.com/Shubhamsaboo/awesome-llm-apps.git|main"
    "ai/Platforms_and_Infrastructure/dify|https://github.com/langgenius/dify.git|main"
    "ai/Platforms_and_Infrastructure/strapi|https://github.com/strapi/strapi.git|develop"

    # 目录: java
    "java/automon|https://github.com/stevensouza/automon.git|master"
    "java/caffeine|https://github.com/ben-manes/caffeine.git|master"
    "java/cat|https://github.com/dianping/cat.git|master"
    "java/DataX|https://github.com/alibaba/DataX.git|master"
    "java/disruptor|https://github.com/LMAX-Exchange/disruptor.git|master"
    "java/glowroot|https://github.com/glowroot/glowroot.git|main"
    "java/HikariCP|https://github.com/brettwooldridge/HikariCP.git|dev"
    "java/hippo4j|https://github.com/opengoofy/hippo4j.git|develop"
    "java/Hystrix|https://github.com/Netflix/Hystrix.git|master"
    "java/jedis|https://github.com/redis/jedis.git|master"
    "java/langchain4j|https://github.com/langchain4j/langchain4j.git|main"
    "java/lettuce|https://github.com/redis/lettuce.git|main"
    "java/logging-log4j2|https://github.com/apache/logging-log4j2.git|2.x"
    "java/mcp-java-sdk|https://github.com/modelcontextprotocol/java-sdk.git|main"
    "java/micrometer|https://github.com/micrometer-metrics/micrometer.git|main"

    # 目录: java/mybatis/mybatis-coll
    "java/mybatis/mybatis-coll/mybatis|https://github.com/tuguangquan/mybatis.git|master"
    "java/mybatis/mybatis-coll/Mybatis-PageHelper|https://github.com/pagehelper/Mybatis-PageHelper.git|master"
    "java/mybatis/mybatis-coll/parent|https://github.com/mybatis/parent.git|master"
    "java/mybatis/mybatis-coll/spring-boot-starter|https://github.com/mybatis/spring-boot-starter.git|master"
    "java/mybatis/mybatis-coll/spring|https://github.com/mybatis/spring.git|master"

    # 目录: java/mybatis/mybatis-plus-coll
    "java/mybatis/mybatis-plus-coll/mybatis-plus|https://github.com/baomidou/mybatis-plus.git|3.0"
    "java/mybatis/mybatis-plus-coll/mybatis-plus-samples|https://github.com/baomidou/mybatis-plus-samples.git|master"

    # 目录: java
    "java/netty|https://github.com/netty/netty.git|4.2"
    "java/redisson|https://github.com/redisson/redisson.git|master"
    "java/resilience4j|https://github.com/resilience4j/resilience4j.git|master"
    "java/retrofit|https://github.com/square/retrofit.git|trunk"

    # 目录: java/ruoyi
    "java/ruoyi/RuoYi-Vue3|https://github.com/yangzongzhuan/RuoYi-Vue3.git|master"
    "java/ruoyi/RuoYi-Vue|https://gitee.com/y_project/RuoYi-Vue.git|master"

    # 目录: java
    "java/Sa-Token|https://github.com/dromara/Sa-Token.git|dev"
    "java/shardingsphere|https://github.com/apache/shardingsphere.git|master"
    "java/slf4j|https://github.com/qos-ch/slf4j.git|master"
    "java/snowy|https://gitee.com/xiaonuobase/snowy.git|master"

    # 目录: java/spring-ai
    "java/spring-ai/spring-ai-examples|https://github.com/spring-projects/spring-ai-examples.git|main"
    "java/spring-ai/spring-ai|https://github.com/spring-projects/spring-ai.git|main"

    # 目录: java/spring-boot
    "java/spring-boot/spring-boot|https://github.com/spring-projects/spring-boot.git|main"

    # 目录: java/spring-cloud-alibaba
    "java/spring-cloud-alibaba/incubator-seata|https://github.com/apache/incubator-seata.git|2.x"
    "java/spring-cloud-alibaba/nacos|https://github.com/alibaba/nacos.git|develop"
    "java/spring-cloud-alibaba/rocketmq|https://github.com/apache/rocketmq.git|develop"
    "java/spring-cloud-alibaba/Sentinel|https://github.com/alibaba/Sentinel.git|1.8"
    "java/spring-cloud-alibaba/spring-cloud-alibaba|https://github.com/alibaba/spring-cloud-alibaba.git|2023.x"

    # 目录: java/spring-cloud
    "java/spring-cloud/spring-cloud-commons|https://github.com/spring-cloud/spring-cloud-commons.git|main"
    "java/spring-cloud/spring-cloud-config|https://github.com/spring-cloud/spring-cloud-config.git|main"
    "java/spring-cloud/spring-cloud-function|https://github.com/spring-cloud/spring-cloud-function.git|main"
    "java/spring-cloud/spring-cloud-gateway|https://github.com/spring-cloud/spring-cloud-gateway.git|main"
    "java/spring-cloud/spring-cloud-openfeign|https://github.com/spring-cloud/spring-cloud-openfeign.git|main"
    "java/spring-cloud/spring-cloud-sleuth|https://github.com/spring-cloud/spring-cloud-sleuth.git|3.1.x"
    "java/spring-cloud/spring-cloud-stream|https://github.com/spring-cloud/spring-cloud-stream.git|main"
    "java/spring-cloud/spring-cloud-stream-samples|https://github.com/spring-cloud/spring-cloud-stream-samples.git|main"

    # 目录: java/spring-data
    "java/spring-data/spring-data-commons|https://github.com/spring-projects/spring-data-commons.git|main"
    "java/spring-data/spring-data-examples|https://github.com/spring-projects/spring-data-examples.git|main"
    "java/spring-data/spring-data-jpa|https://github.com/spring-projects/spring-data-jpa.git|main"
    "java/spring-data/spring-data-redis|https://github.com/spring-projects/spring-data-redis.git|main"

    # 目录: java/spring-security
    "java/spring-security/spring-security|https://github.com/spring-projects/spring-security.git|main"
    "java/spring-security/spring-security-samples|https://github.com/spring-projects/spring-security-samples.git|main"

    # 目录: java/spring
    "java/spring/spring-batch|https://github.com/spring-projects/spring-batch.git|main"
    "java/spring/spring-framework|https://github.com/spring-projects/spring-framework.git|main"
    "java/spring/spring-petclinic|https://github.com/spring-projects/spring-petclinic.git|main"
    "java/spring/spring-retry|https://github.com/spring-projects/spring-retry.git|main"
    "java/spring/spring-session|https://github.com/spring-projects/spring-session.git|main"

    # 目录: java
    "java/xxl-job|https://github.com/xuxueli/xxl-job.git|master"

    # 目录: java/yudao
    "java/yudao/ruoyi-vue-pro|https://gitee.com/zhijiantianya/ruoyi-vue-pro.git|master"
    "java/yudao/yudao-cloud|https://gitee.com/zhijiantianya/yudao-cloud.git|master"
    "java/yudao/yudao-mall-uniapp|https://gitee.com/yudaocode/yudao-mall-uniapp.git|master"
    "java/yudao/yudao-ui-admin-vben|https://gitee.com/yudaocode/yudao-ui-admin-vben.git|master"
    "java/yudao/yudao-ui-go-view|https://gitee.com/yudaocode/yudao-ui-go-view.git|master"

    # 目录: js/react
    "js/react/ant-design-pro|https://github.com/ant-design/ant-design-pro.git|master"
    "js/react/arco-design-mobile|https://github.com/arco-design/arco-design-mobile.git|main"
    "js/react/arco-design-pro|https://github.com/arco-design/arco-design-pro.git|main"
    "js/react/react-admin|https://github.com/marmelab/react-admin.git|master"

    # 目录: js/vue
    "js/vue/vue-vben-admin|https://github.com/vbenjs/vue-vben-admin.git|main"

    # 目录: py/fastapi
    "py/fastapi/fastapi-admin|https://github.com/fastapi-admin/fastapi-admin.git|dev"
    "py/fastapi/fastapi-amis-admin|https://github.com/amisadmin/fastapi-amis-admin.git|master"
    "py/fastapi/fastapi-best-practices|https://github.com/zhanymkanov/fastapi-best-practices.git|master"
    "py/fastapi/fastapi_mcp|https://github.com/tadata-org/fastapi_mcp.git|main"
    "py/fastapi/full-stack-fastapi-template|https://github.com/fastapi/full-stack-fastapi-template.git|master"
)

# 冲突处理策略配置
CONFLICT_STRATEGY="${3:-ask}"  # ask, skip, backup, force
BACKUP_SUFFIX=".backup.$(date +%Y%m%d_%H%M%S)"

# 检查Git仓库URL是否匹配
check_git_url_match() {
    local repo_dir="$1"
    local expected_url="$2"
    
    if [[ ! -d "$repo_dir/.git" ]]; then
        return 1  # 不是Git仓库
    fi
    
    cd "$repo_dir" || return 1
    
    # 获取所有远程URL
    local current_urls
    current_urls=$(git remote -v | grep fetch | awk '{print $2}' | sort | uniq)
    
    # 标准化URL进行比较（移除.git后缀，统一协议格式）
    local normalized_expected
    normalized_expected=$(echo "$expected_url" | sed 's/\.git$//' | sed 's|^git@\([^:]*\):|https://\1/|')
    
    for url in $current_urls; do
        local normalized_current
        normalized_current=$(echo "$url" | sed 's/\.git$//' | sed 's|^git@\([^:]*\):|https://\1/|')
        if [[ "$normalized_current" == "$normalized_expected" ]]; then
            return 0  # URL匹配
        fi
    done
    
    return 1  # URL不匹配
}

# 处理目录冲突
handle_directory_conflict() {
    local target_dir="$1"
    local clone_url="$2"
    local rel_path="$3"
    
    if [[ ! -e "$target_dir" ]]; then
        return 0  # 无冲突
    fi
    
    log_warning "发现目录冲突: $target_dir"
    
    # 检查是否为Git仓库
    if [[ -d "$target_dir/.git" ]]; then
        if check_git_url_match "$target_dir" "$clone_url"; then
            log_info "现有仓库URL匹配，将进行更新"
            return 0  # 可以安全更新
        else
            log_warning "现有仓库URL不匹配！"
            log_info "  期望URL: $clone_url"
            log_info "  当前URL: $(cd "$target_dir" && git remote get-url origin 2>/dev/null || echo '未知')"
        fi
    else
        log_warning "目录存在但不是Git仓库"
    fi
    
    # 根据策略处理冲突
    case "$CONFLICT_STRATEGY" in
        "skip")
            log_info "跳过冲突项目: $rel_path"
            return 1
            ;;
        "backup")
            local backup_dir="${target_dir}${BACKUP_SUFFIX}"
            log_info "创建备份: $backup_dir"
            mv "$target_dir" "$backup_dir"
            return 0
            ;;
        "force")
            log_warning "强制覆盖现有目录"
            rm -rf "$target_dir"
            return 0
            ;;
        "ask")
            echo ""
            log_warning "如何处理冲突目录: $target_dir"
            echo "  1) 跳过 (s/skip)"
            echo "  2) 备份后克隆 (b/backup)"
            echo "  3) 强制覆盖 (f/force)"
            echo "  4) 退出脚本 (q/quit)"
            read -p "请选择 [s/b/f/q]: " choice
            
            case "$choice" in
                s|skip)
                    log_info "跳过项目: $rel_path"
                    return 1
                    ;;
                b|backup)
                    local backup_dir="${target_dir}${BACKUP_SUFFIX}"
                    log_info "创建备份: $backup_dir"
                    mv "$target_dir" "$backup_dir"
                    return 0
                    ;;
                f|force)
                    log_warning "强制覆盖现有目录"
                    rm -rf "$target_dir"
                    return 0
                    ;;
                q|quit)
                    log_info "用户选择退出"
                    exit 0
                    ;;
                *)
                    log_warning "无效选择，跳过此项目"
                    return 1
                    ;;
            esac
            ;;
    esac
    
    return 1
}

# 安全更新Git仓库
safe_update_repo() {
    local repo_dir="$1"
    local branch="$2"
    
    cd "$repo_dir" || return 1
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        log_warning "仓库有未提交的更改，跳过更新: $(basename "$repo_dir")"
        return 1
    fi
    
    # 获取当前分支
    local current_branch
    current_branch=$(git branch --show-current 2>/dev/null || echo "")
    
    log_info "更新仓库: $(basename "$repo_dir")"
    
    # 获取远程更新
    if ! git fetch --all --prune; then
        log_warning "获取远程更新失败"
        return 1
    fi
    
    # 处理分支切换
    if [[ "$branch" != "unknown" && "$branch" != "$current_branch" ]]; then
        if git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
            log_info "切换到分支: $branch"
            git checkout "$branch" || log_warning "分支切换失败: $branch"
        else
            log_warning "远程分支不存在: $branch"
        fi
    fi
    
    # 如果当前分支有对应的远程分支，尝试合并
    local current_branch_after
    current_branch_after=$(git branch --show-current 2>/dev/null || echo "")
    if [[ -n "$current_branch_after" ]] && git show-ref --verify --quiet "refs/remotes/origin/$current_branch_after"; then
        local local_commit remote_commit base_commit
        local_commit=$(git rev-parse HEAD)
        remote_commit=$(git rev-parse "origin/$current_branch_after")
        base_commit=$(git merge-base HEAD "origin/$current_branch_after" 2>/dev/null || echo "")
        
        if [[ "$local_commit" == "$remote_commit" ]]; then
            log_info "仓库已是最新状态"
        elif [[ "$local_commit" == "$base_commit" ]]; then
            log_info "执行快进合并"
            git merge --ff-only "origin/$current_branch_after"
        else
            log_warning "本地有独特提交，跳过自动合并"
        fi
    fi
    
    return 0
}

# 执行项目克隆/更新
for project_info in "${PROJECTS[@]}"; do
    IFS='|' read -r rel_path clone_url branch <<< "$project_info"
    
    target_dir="$BASE_DIR/$rel_path"
    
    log_info "处理项目: $rel_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "  [预演] 目标目录: $target_dir"
        log_info "  [预演] 仓库URL: $clone_url"
        log_info "  [预演] 目标分支: $branch"
        
        if [[ -e "$target_dir" ]]; then
            if [[ -d "$target_dir/.git" ]]; then
                if check_git_url_match "$target_dir" "$clone_url"; then
                    log_info "  [预演] 操作: 更新现有仓库"
                else
                    log_warning "  [预演] 冲突: URL不匹配，需要处理"
                fi
            else
                log_warning "  [预演] 冲突: 目录存在但不是Git仓库"
            fi
        else
            log_info "  [预演] 操作: 克隆新仓库"
        fi
        continue
    fi
    
    # 处理目录冲突
    if ! handle_directory_conflict "$target_dir" "$clone_url" "$rel_path"; then
        continue  # 跳过此项目
    fi
    
    # 执行克隆或更新
    if [[ -d "$target_dir/.git" ]]; then
        # 更新现有仓库
        if safe_update_repo "$target_dir" "$branch"; then
            log_success "仓库更新完成: $rel_path"
        else
            log_warning "仓库更新失败: $rel_path"
        fi
    else
        # 克隆新仓库
        log_info "克隆新仓库: $clone_url"
        mkdir -p "$(dirname "$target_dir")"
        
        if git clone --depth=1 "$clone_url" "$target_dir"; then
            cd "$target_dir"
            
            # 切换到指定分支
            if [[ "$branch" != "unknown" ]] && git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
                git checkout "$branch" || log_warning "切换分支失败: $branch"
            fi
            
            log_success "克隆完成: $rel_path"
        else
            log_error "克隆失败: $clone_url"
        fi
    fi
done

log_success "项目还原完成！"
