#!/bin/bash

ips=$1
ports=$2
action=$3

if [ $action ]

#要将$a分割开，先存储旧的分隔符
OLD_IFS="$IFS"

#设置分隔符
IFS="," 

#如下会自动分隔
ipArr=($ips)
portArr=($ports)

#恢复原来的分隔符
IFS="$OLD_IFS"

#遍历数组
for ip in ${ipArr[@]}; do
  for port in ${portArr[@]}; do
    if [ "$action" == "remove" ]; then
      firewall-cmd --permanent --remove-rich-rule="rule family="ipv4" source address="$ip" port protocol="tcp" port="$port" accept"
    else
      firewall-cmd --permanent --add-rich-rule="rule family="ipv4" source address="$ip" port protocol="tcp" port="$port" accept"
    fi
  done
done

firewall-cmd --reload
firewall-cmd --zone=public --list-rich-rules
