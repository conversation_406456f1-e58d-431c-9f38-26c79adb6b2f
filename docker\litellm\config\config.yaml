# LiteLLM 配置文件
# 参考文档: https://docs.litellm.ai/docs/proxy_server

# 服务器配置
server:
  host: "0.0.0.0"
  port: 4000
  debug: false
  environment: "production"

# 模型配置
model_list:
  - model_name: "gpt-3.5-turbo"
    litellm_params:
      model: "openai/gpt-3.5-turbo"
      api_key: "${OPENAI_API_KEY}"
  - model_name: "gpt-4"
    litellm_params:
      model: "openai/gpt-4"
      api_key: "${OPENAI_API_KEY}"
  - model_name: "claude-2"
    litellm_params:
      model: "anthropic/claude-2"
      api_key: "${ANTHROPIC_API_KEY}"

# 限流配置
rate_limiting:
  enabled: true
  rate_limit_by_ip: true
  global_rate_limit: "60/minute"
  rate_limit_by_key: true
  rate_limit_key_header: "Authorization"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "/app/data/litellm.log"
  max_size: 100  # MB
  backup_count: 5

# 缓存配置
caching:
  type: "in_memory"  # 生产环境建议使用 redis
  ttl: 300  # 缓存过期时间(秒)

# 如果使用Redis缓存，取消以下注释并配置
#  caching:
#    type: "redis"
#    host: "redis"
#    port: 6379
#    password: "${REDIS_PASSWORD}"
#    db: 0
#    ttl: 300

# 监控和追踪
monitoring:
  sentry_dsn: "${SENTRY_DSN}"  # 可选: Sentry DSN 用于错误追踪
  langfuse_public_key: "${LANGFUSE_PUBLIC_KEY}"  # 可选: Langfuse 公钥
  langfuse_secret_key: "${LANGFUSE_SECRET_KEY}"  # 可选: Langfuse 密钥

# 安全配置
security:
  master_key: "${LITELLM_MASTER_KEY}"  # 用于管理API的密钥
  allowed_ips:  # 允许访问的IP地址，留空表示允许所有
  # - "***********/24"
  # - "10.0.0.0/8"
  enable_auth: true
  auth_type: "bearer"  # 或 "none" 如果不需要认证

# 数据库配置 (用于持久化数据)
database:
#   type: "sqlite"  # 生产环境建议使用 postgres
#   path: "/app/data/litellm.db"
#   如果使用PostgreSQL，取消以下注释并配置
  type: "postgres"
  url: "${DB_URL}"
  pool_size: 5
  max_overflow: 10

# 代理配置 (如果需要)
# proxy:
#   http: "http://your-proxy-address:port"
#   https: "http://your-proxy-address:port"
#   no_proxy: "localhost,127.0.0.1,*.example.com"
