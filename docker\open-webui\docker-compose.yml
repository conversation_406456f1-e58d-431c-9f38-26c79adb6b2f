services:
  # Open WebUI 服务
  open-webui:
    # 1. 使用官方的 CUDA 镜像
    # 这个镜像不包含 Ollama，但为 WebUI 本身提供了 NVIDIA GPU 支持，适合连接到本机的 Ollama。
    image: swr.cn-east-3.myhuaweicloud.com/kubesre/ghcr.io/open-webui/open-webui:cuda
    container_name: open-webui
    ports:
      # 将主机的 3000 端口映射到容器的 8080 端口，以便通过浏览器访问。
      - "3000:8080"
    environment:
      # 2. 关键：明确设置 Ollama 服务的地址
      # 'host.docker.internal' 允许容器访问其宿主机（在这里就是你的 WSL2 环境）。
      - 'OLLAMA_BASE_URL=http://host.docker.internal:11434'
      - 'USER_AGENT=Docker-Open-WebUI'
    volumes:
      # 仅需持久化 Open WebUI 自身的配置、聊天记录等数据。
      - open-webui-data:/app/backend/data
    extra_hosts:
      # 确保 'host.docker.internal' 能被正确解析到宿主机。
      - "host.docker.internal:host-gateway"
    # 3. 关键：为容器分配 GPU 资源
    # 这等同于 `docker run` 命令中的 `--gpus all` 参数。
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    restart: always
    # 可选：每次启动时都尝试拉取最新的镜像，以保持更新。
    pull_policy: always

# 定义数据卷
volumes:
  # 仅需为 Open WebUI 定义数据卷，以持久化其数据。
  open-webui-data:
