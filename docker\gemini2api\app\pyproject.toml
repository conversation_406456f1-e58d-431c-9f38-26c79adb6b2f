[project]
name = "gemi2api-server"
version = "0.1.2"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "browser-cookie3>=0.20.1",
    "fastapi>=0.115.12",
    "gemini-webapi>=1.12.1",
    "uvicorn[standard]>=0.34.1",
]
[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

[dependency-groups]
dev = [
    "ruff>=0.11.7",
]

[tool.ruff]
line-length = 150  # 设置最大行长度
select = ["E", "F", "W", "I"]  # 启用的规则（E: pycodestyle, F: pyflakes, W: pycodestyle warnings, I: isort）
ignore = ["E501"]  # 忽略特定规则（如行长度警告）

[tool.ruff.format]
quote-style = "double"  # 使用双引号
indent-style = "tab"  # 使用空格缩进
