# -*- mode: ruby -*-
# vi: set ft=ruby :

# Common
$name="workstation"

Vagrant.configure("2") do |config|
  config.vm.box = "zhangq/rockylinux9"
  config.vm.box_check_update = false
  config.vm.hostname = $name

  # 读取环境变量来确定使用哪个配置文件
  env = ENV['VAGRANT_ENV'] || 'home'
  config_file = "env-#{env}.rb"

  # 加载配置文件
  if File.exist?(config_file)
    puts "======>#{config_file}"
    require_relative config_file
  else
    raise "Config file #{config_file} not found"
  end

  config.vm.network "public_network", ip: $static_ip, netmask: $netmask, gateway: $gateway, hostname: true

  if Vagrant.has_plugin?("vagrant-vbguest") then
    config.vbguest.auto_update = false
    config.vbguest.no_remote = true
    config.vbguest.no_install = true
  end

  config.vm.provider "virtualbox" do |vb|
    vb.gui = false
    vb.memory = $memory
    vb.cpus = $cpus
    vb.name = $name
  end

  # 机构初始化
  config.vm.provision "shell", run: "always", privileged: true, inline: <<-SHELL, args: [$dns1]
    if ! grep -qxF "nameserver $1" /etc/resolv.conf; then
      echo "nameserver $1" > /etc/resolv.conf
    fi
    if ! grep -qxF "nameserver $2" /etc/resolv.conf; then
      echo "nameserver $2" >> /etc/resolv.conf
    fi
  SHELL
  config.vm.provision "shell", path: "./init.sh", privileged: true

end
