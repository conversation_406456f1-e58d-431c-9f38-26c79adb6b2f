import json

def create_judge_prompt(question: str, answer_list: list[dict], dimension_list: list[str]) -> str:
    """
    为裁判模型生成结构化的提示词。

    参数:
        question: 原始问题。
        answer_list: 一个字典列表，每个字典包含 {"model_name": str, "answer": str}。
        dimension_list: 评测维度的列表。

    返回:
        一个为裁判模型格式化好的提示词字符串。
    """

    formatted_answers = "\n\n".join(
        [f"--- 模型: {ans['model_name']} ---\n{ans['answer']}" for ans in answer_list]
    )

    formatted_dimensions = ", ".join(f'"{dim}"' for dim in dimension_list)

    # 定义期望的JSON输出结构，这让解析裁判的响应变得可靠。
    json_structure_example = [
        {
            "model_name": "字符串",
            "scores": {
                "维度_1": "整数 (1-10)",
                "维度_2": "整数 (1-10)",
            },
            "comment": "字符串 (一句精简的评语)"
        }
    ]
    # 动态创建得分字典的示例
    json_structure_example[0]['scores'] = {dim: "整数 (1-10)" for dim in dimension_list}


    prompt = f"""
    作为一名专业的大语言模型评测员，您的任务是针对一个给定的问题，评估多个AI模型的回答质量。
    请根据指定的评测维度，提供一个公平、客观且精确的评估。

    # 原始问题
    {question}

    # 模型回答
    {formatted_answers}

    # 评测任务
    您必须根据以下维度评估每个模型的回答: {formatted_dimensions}。
    请为每个维度提供一个1（最差）到10（最好）的分数。
    同时，请为每个模型的回答提供一句精简的总体评语。

    # 输出格式
    您的输出必须是一个有效的JSON数组，其中每个模型对应一个JSON对象。请不要在JSON结构之外包含任何文字或解释。
    每个对象的JSON结构必须如下所示:

    ```json
    {json.dumps(json_structure_example, indent=2, ensure_ascii=False)}
    ```

    请直接以半角方括号 `[` 开始您的回答。
    """
    return prompt.strip()

if __name__ == '__main__':
    # 示例用法
    example_question = "请用简单的语言解释相对论。"
    example_answers = [
        {"model_name": "模型A", "answer": "E=mc²，讲的是能量和质量的关系。"},
        {"model_name": "模型B", "answer": "相对论分为狭义和广义两个部分。狭义相对论讨论光速，而广义相对论则将引力描述为时空的弯曲。"},
    ]
    example_dimensions = ["清晰度", "深度", "准确性"]

    judge_prompt = create_judge_prompt(example_question, example_answers, example_dimensions)
    print(judge_prompt)
