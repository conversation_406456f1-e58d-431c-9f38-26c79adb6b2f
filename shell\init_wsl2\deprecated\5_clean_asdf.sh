#!/usr/bin/env bash

# ==============================================================================
# 5. 清理 asdf 及其所有遗留配置
# ==============================================================================

# 确保日志输出即时刷新，避免格式错乱
log_with_flush() {
    local func=$1
    local msg=$2
    $func "$msg"
    sleep 0.01  # 等待一小段时间确保输出已刷新
}

clean_asdf_env() {
    log_with_flush info "--- 开始彻底清理 asdf 及其所有遗留配置 ---"

    # 1. 定义 asdf 相关的所有路径和配置文件
    local ASDF_DIR="$HOME/.asdf"
    local ASDF_DATA_DIR="${ASDF_DATA_DIR:-$HOME/.asdf}"
    local SHELL_CONFIG_FILES=(
        "$HOME/.bashrc"
        "$HOME/.bash_profile"
        "$HOME/.profile"
        "$HOME/.zshrc"
        "$HOME/.zprofile"
        "$HOME/.zshenv"
        "$HOME/.config/fish/config.fish"
    )

    # 2. 停止所有可能正在运行的 asdf 相关进程
    log_with_flush info "停止所有可能的 asdf 相关进程..."
    # 使用 pgrep 先检查进程是否存在，避免不必要的错误
    if pgrep -f "asdf" >/dev/null 2>&1; then
        pkill -f "asdf" 2>/dev/null || true
    fi

    # 3. 移除 asdf 主目录
    if [ -d "$ASDF_DIR" ]; then
        log_with_flush info "移除 asdf 主目录: $ASDF_DIR"
        rm -rf "$ASDF_DIR"
        log_with_flush success "已移除 asdf 主目录"
    else
        log_with_flush info "asdf 主目录不存在，无需移除"
    fi

    # 4. 移除 asdf 可能创建的其他数据目录（如果与主目录不同）
    if [ "$ASDF_DATA_DIR" != "$ASDF_DIR" ] && [ -d "$ASDF_DATA_DIR" ]; then
        log_with_flush info "移除 asdf 数据目录: $ASDF_DATA_DIR"
        rm -rf "$ASDF_DATA_DIR"
        log_with_flush success "已移除 asdf 数据目录"
    fi

    # 5. 清理 shell 配置文件中的 asdf 相关配置
    log_with_flush info "清理 shell 配置文件中的 asdf 相关配置..."
    for config_file in "${SHELL_CONFIG_FILES[@]}"; do
        if [ -f "$config_file" ]; then
            log_with_flush info "处理配置文件: $config_file"

            # 创建临时文件
            local temp_file="${config_file}.temp"

            # 检查文件是否包含 asdf 相关配置
            if grep -q -E '(asdf|ASDF_DIR|ASDF_DATA_DIR|fpath=\$\{ASDF_DIR}|\.asdf/|asdf\.sh|asdf/completions)' "$config_file" 2>/dev/null; then
                # 过滤掉所有 asdf 相关的行
                grep -v -E '(asdf|ASDF_DIR|ASDF_DATA_DIR|fpath=\$\{ASDF_DIR}|\.asdf/|asdf\.sh|asdf/completions)' "$config_file" > "$temp_file" || true

                # 替换原文件
                mv "$temp_file" "$config_file"
                log_with_flush success "已清理 $config_file 中的 asdf 配置"
            else
                log_with_flush info "$config_file 中未发现 asdf 配置，无需清理"
            fi
        fi
    done

    # 6. 清理环境变量
    log_with_flush info "清理环境变量..."
    # 检查环境变量是否存在再清理，避免不必要的警告
    for var in ASDF_DIR ASDF_DATA_DIR ASDF_CONFIG_FILE ASDF_DEFAULT_TOOL_VERSIONS_FILENAME; do
        if [ -n "${!var+x}" ]; then
            unset "$var"
        fi
    done

    # 7. 清理 PATH 中的 asdf 相关路径
    if [[ ":$PATH:" == *":$HOME/.asdf/bin:"* ]] || [[ ":$PATH:" == *":$HOME/.asdf/shims:"* ]]; then
        log_with_flush info "从 PATH 中移除 asdf 相关路径"
        # 分步处理，避免复杂的管道操作导致日志错乱
        local ORIG_PATH="$PATH"
        local NEW_PATH="$ORIG_PATH"
        
        # 分别处理四种情况，避免复杂的单行 sed 命令
        NEW_PATH=$(echo "$NEW_PATH" | sed 's|:[^:]*/.asdf/bin||g')
        NEW_PATH=$(echo "$NEW_PATH" | sed 's|:[^:]*/.asdf/shims||g')
        NEW_PATH=$(echo "$NEW_PATH" | sed 's|^[^:]*/.asdf/bin:||g')
        NEW_PATH=$(echo "$NEW_PATH" | sed 's|^[^:]*/.asdf/shims:||g')
        
        # 只有当 PATH 确实发生变化时才更新
        if [ "$ORIG_PATH" != "$NEW_PATH" ]; then
            export PATH="$NEW_PATH"
            log_with_flush success "PATH 中的 asdf 相关路径已清理"
        fi
    fi

    # 8. 清理 asdf 插件可能留下的全局配置
    log_with_flush info "清理 asdf 插件可能留下的全局配置..."

    # 清理 .tool-versions 文件
    local tool_versions_files=(
        "$HOME/.tool-versions"
        "$HOME/.default-gems"
        "$HOME/.default-npm-packages"
        "$HOME/.default-python-packages"
    )

    local found_files=false
    for tvf in "${tool_versions_files[@]}"; do
        if [ -f "$tvf" ]; then
            found_files=true
            log_with_flush info "移除文件: $tvf"
            rm -f "$tvf" 2>/dev/null || true
        fi
    done
    
    if [ "$found_files" = false ]; then
        log_with_flush info "未发现 asdf 插件配置文件，无需清理"
    fi

    # 9. 清理可能的 asdf 缓存目录
    log_with_flush info "检查并清理 asdf 缓存目录..."
    local cache_dirs=(
        "$HOME/.cache/asdf"
        "$HOME/.cache/asdf-nodejs"
        "$HOME/.cache/asdf-python"
    )

    local found_cache=false
    for cache_dir in "${cache_dirs[@]}"; do
        if [ -d "$cache_dir" ]; then
            found_cache=true
            log_with_flush info "移除缓存目录: $cache_dir"
            rm -rf "$cache_dir"
        fi
    done
    
    if [ "$found_cache" = false ]; then
        log_with_flush info "未发现 asdf 缓存目录，无需清理"
    fi

    # 10. 清理可能的临时文件
    log_with_flush info "清理 asdf 相关临时文件..."
    # 使用更安全的方式清理临时文件，避免意外删除
    if [ -d "/tmp" ]; then
        find /tmp -maxdepth 2 -name "asdf*" -type d -exec rm -rf {} \; 2>/dev/null || true
    fi

    # 添加空行增强可读性
    echo ""
    log_with_flush success "--- asdf 及其所有遗留配置已彻底清理完成 ---"
    echo ""
}
