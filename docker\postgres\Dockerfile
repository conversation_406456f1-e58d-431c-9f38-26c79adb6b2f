# 使用官方 pgvector 镜像作为基础
FROM pgvector/pgvector:pg16

# 安装 PostGIS 插件及其脚本
# 使用 root 用户来安装系统包
USER root
# 更换 APT 软件源为清华大学镜像源
RUN find /etc/apt/ -type f -name "*.list" -exec sed -i 's|deb.debian.org|mirrors.tuna.tsinghua.edu.cn|g' {} \; && \
    find /etc/apt/ -type f -name "*.list" -exec sed -i 's|security.debian.org|mirrors.tuna.tsinghua.edu.cn|g' {} \;
RUN apt-get update && apt-get install -y --no-install-recommends \
    postgresql-16-postgis-3 \
    postgresql-16-postgis-3-scripts \
    && rm -rf /var/lib/apt/lists/*
# 切换回 postgres 用户
USER postgres
