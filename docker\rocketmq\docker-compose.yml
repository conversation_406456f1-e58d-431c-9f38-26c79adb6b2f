services:
  # RocketMQ NameServer
  namesrv:
    image: apache/rocketmq:4.9.4
    container_name: rmqnamesrv
    ports:
      - 9876:9876
    volumes:
      - ./data/namesrv/logs:/home/<USER>/logs
      - ./data/namesrv/store:/home/<USER>/store
    networks:
      - rocketmq
    environment:
      - JAVA_OPT_EXT=-server -Xms256m -Xmx256m -Xmn128m
    command: sh mqnamesrv
    healthcheck:
      test: [ "CMD", "sh", "-c", "nc -z localhost 9876" ]
      interval: 5s
      timeout: 5s
      retries: 3

  # RocketMQ Broker
  broker:
    image: apache/rocketmq:4.9.4
    container_name: rmqbroker
    ports:
      - 10909:10909 # 向Producer发送消息的端口
      - 10911:10911 # 向Consumer投递消息的端口
      - 10912:10912 # 用于主从同步的端口
    volumes:
      - ./data/broker/logs:/home/<USER>/logs
      - ./data/broker/store:/home/<USER>/store
      - ./conf/broker.conf:/home/<USER>/conf/broker.conf
      # - type: bind
      #   source: ./conf/broker.conf
      #   target: /home/<USER>/rocketmq-4.9.4/conf/broker.conf
      #   read_only: true
    networks:
      - rocketmq
    environment:
      - JAVA_OPT_EXT=-server -Xms256m -Xmx256m -Xmn128m
      - NAMESRV_ADDR=namesrv:9876
    command: sh mqbroker -c /home/<USER>/conf/broker.conf
    depends_on:
      namesrv:
        condition: service_healthy

  # RocketMQ Dashboard
  dashboard:
    image: apacherocketmq/rocketmq-dashboard:1.0.0
    container_name: rmqdashboard
    ports:
      - 48080:8080
    volumes:
      - ./dashboard-conf:/tmp/rocketmq-console/data # 不起作用，以后再调
    networks:
      - rocketmq
    environment:
      - JAVA_OPTS=-Drocketmq.namesrv.addr=namesrv:9876 -Xmx128m -Xms128m
      - ROCKETMQ_CONFIG_LOGIN_REQUIRED=true
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    depends_on:
      - namesrv
    restart: always

networks:
  rocketmq:
    name: rocketmq
    driver: bridge
