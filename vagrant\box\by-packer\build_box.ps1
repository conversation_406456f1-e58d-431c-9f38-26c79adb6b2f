# 设置目标目录
$destinationDir = "E:\BaiduSyncdisk\VagrantBox\"

# 定义box名称变量
$boxName = "ibootz/rockylinux-9-base"

# 获取当前目录下的所有 .box 文件
$boxFiles = Get-ChildItem -Path . -Filter *.box

# 删除当前同名box
vagrant box remove $boxName --all-providers

foreach ($boxFile in $boxFiles) {
  # 提取box文件名和供应商信息
  $boxFileName = [System.IO.Path]::GetFileNameWithoutExtension($boxFile.Name)
  $provider = if ($boxFileName -match "-vmware") { "vmware_desktop" } elseif ($boxFileName -match "-virtualbox") { "virtualbox" } else { "unknown" }

  if ($provider -ne "unknown") {

    # 添加box到本地
    vagrant box add $boxName $boxFile.FullName --provider $provider --force

    # 复制box文件到指定目录
    # Copy-Item $boxFile.FullName -Destination $destinationDir

    Write-Host "Box $boxName for provider $provider has been added and copied to $destinationDir."
  }
  else {
    Write-Host "Skipping unknown provider for box file: $boxFile.Name"
  }
}

# 删除当前目录下的box文件
Remove-Item -Path .\*.box

# 删除当前目录下的packer_cache文件夹, 如不存在则忽略
Remove-Item -Path ./packer_cache -Recurse -Force -ErrorAction SilentlyContinue

# 列出本地box
vagrant box list
