services:
  mongodb:
    image: mongo:latest
    container_name: mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME:-root}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD:-123456}
      MONGO_INITDB_DATABASE: ${MONGO_INITDB_DATABASE:-admin}
      TZ: Asia/Shanghai
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - mongo_network
    # 添加初始化参数
    command: [
      '--auth',
      '--bind_ip_all',
      '--wiredTigerCacheSizeGB=1.5'
    ]

volumes:
  mongodb_data:
    name: mongodb_data

networks:
  mongo_network:
    driver: bridge
