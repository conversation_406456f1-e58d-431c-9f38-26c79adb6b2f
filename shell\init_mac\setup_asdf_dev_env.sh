#!/usr/bin/env bash

# ==============================================================================
# macOS asdf多语言开发环境配置脚本
# ==============================================================================

# --- 辅助函数定义 ---
info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查是否为Apple Silicon Mac
is_apple_silicon() {
    [[ "$(uname -m)" == "arm64" ]]
}

# 获取Homebrew路径
get_brew_path() {
    if is_apple_silicon; then
        echo "/opt/homebrew"
    else
        echo "/usr/local"
    fi
}

setup_asdf_dev_env() {
    info "--- 开始配置asdf及多语言开发环境 (macOS) ---"

    # 定义 asdf 数据目录和安装目录
    ASDF_DATA_DIR="${ASDF_DATA_DIR:-$HOME/.asdf}"
    ASDF_VERSION="0.18.0"  # 指定最新稳定版本
    BREW_PATH=$(get_brew_path)

    # 检查Homebrew是否已安装
    if ! command_exists brew; then
        error "Homebrew未安装，请先安装Homebrew后再运行此脚本"
        info "安装命令: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        return 1
    fi

    # 检查依赖
    info "检查 asdf 所需的依赖..."
    for dep in git curl bash; do
        if ! command -v $dep &> /dev/null; then
            error "缺少必要依赖: $dep，请先安装后再运行此脚本"
            return 1
        fi
    done
    success "所有基础依赖已满足"

    # 检查是否已通过Homebrew安装 asdf
    if brew list asdf &>/dev/null; then
        current_version=$(asdf --version 2>/dev/null | awk '{print $1}' | cut -d 'v' -f2)
        if [ -n "$current_version" ]; then
            info "检测到已通过Homebrew安装 asdf 版本: $current_version"
            install_needed=false
        else
            warn "无法确定当前 asdf 版本，将重新安装。"
            install_needed=true
        fi
    elif command -v asdf &> /dev/null; then
        warn "检测到非Homebrew安装的asdf，建议使用Homebrew重新安装以获得更好的兼容性"
        install_needed=true
    else
        info "未检测到 asdf 安装，将执行全新安装..."
        install_needed=true
    fi

    # 如果需要安装或更新
    if [ "${install_needed:-true}" = true ]; then
        info "通过Homebrew安装asdf..."

        # 如果已经安装了非Homebrew版本，先清理
        if command -v asdf &> /dev/null && ! brew list asdf &>/dev/null; then
            warn "检测到非Homebrew版本的asdf，建议手动清理后重新运行脚本"
            info "清理命令: rm -rf ~/.asdf && 从shell配置文件中移除asdf相关配置"
        fi

        # 通过Homebrew安装asdf
        if brew install asdf; then
            success "asdf 已通过Homebrew成功安装"
        else
            error "通过Homebrew安装asdf失败"
            return 1
        fi
    fi

    # 验证安装
    if ! command -v asdf &> /dev/null; then
        error "asdf 安装后无法在 PATH 中找到，请检查Homebrew配置"
        return 1
    fi

    # --- 配置 shell 环境 ---
    info "检查并确保 shell 环境中的 asdf 配置..."

    # 配置 .zshrc
    if [ -f "$HOME/.zshrc" ]; then
        info "配置 .zshrc 中的 asdf 设置..."

        # 添加 asdf 初始化配置（Homebrew方式）
        if ! grep -q "asdf.sh" "$HOME/.zshrc"; then
            echo -e "\n# ASDF Version Manager - Homebrew 配置" >> "$HOME/.zshrc"
            echo ". $BREW_PATH/opt/asdf/libexec/asdf.sh" >> "$HOME/.zshrc"
            success "asdf 初始化配置已添加到 .zshrc"
        else
            info "asdf 初始化配置已存在于 .zshrc，跳过。"
        fi

        # 添加 completions 配置
        if ! grep -q "asdf.*completions" "$HOME/.zshrc"; then
            echo -e "\n# ASDF completions 配置" >> "$HOME/.zshrc"
            echo ". $BREW_PATH/opt/asdf/etc/bash_completion.d/asdf.bash" >> "$HOME/.zshrc"
            success "asdf completions 配置已添加到 .zshrc"
        else
            info "asdf completions 配置已存在于 .zshrc，跳过。"
        fi
    fi

    # 在当前会话中初始化asdf
    if [ -f "$BREW_PATH/opt/asdf/libexec/asdf.sh" ]; then
        source "$BREW_PATH/opt/asdf/libexec/asdf.sh"
    fi

    # 2. 辅助函数定义
    is_language_installed() {
        local lang=$1
        local version=$2

        info "检查 $lang $version 是否已安装..."

        # 首先确保 asdf 命令可用
        if ! command_exists asdf; then
            warn "asdf命令不可用，无法检查语言安装状态"
            return 1
        fi

        # 检查插件是否已安装
        if ! asdf plugin list 2>/dev/null | grep -q "^$lang$"; then
            info "$lang 插件未安装"
            return 1
        fi

        # 获取已安装的版本列表
        local installed_versions
        installed_versions=$(asdf list "$lang" 2>/dev/null)

        # 如果没有安装任何版本
        if [ -z "$installed_versions" ]; then
            info "$lang 没有安装任何版本"
            return 1
        fi

        # 特殊处理Java版本
        if [[ "$lang" == "java" ]]; then
            # 如果要求temurin版本
            if [[ "$version" == *"temurin"* ]]; then
                # 提取请求的具体版本号，例如从"latest:temurin-21"提取"21"
                local requested_version=""
                if [[ "$version" =~ temurin-([0-9]+) ]]; then
                    requested_version="${BASH_REMATCH[1]}"
                    info "请求的Java版本: temurin-$requested_version"
                fi

                # 检查是否有任何temurin版本已安装
                if echo "$installed_versions" | grep -q "temurin"; then
                    local temurin_version
                    temurin_version=$(echo "$installed_versions" | grep "temurin" | sort -V | tail -1 | xargs)
                    info "检测到 $lang 已安装temurin版本: $temurin_version"

                    # 提取已安装版本的主版本号
                    local clean_temurin_version
                    clean_temurin_version=$(echo "$temurin_version" | sed 's/^[ *]*//' | xargs) # 移除星号和多余空格

                    local installed_major_version=""
                    if [[ "$clean_temurin_version" =~ temurin-(jre-)?([0-9]+) ]]; then
                        installed_major_version="${BASH_REMATCH[2]}"
                    fi

                    if [ "$installed_major_version" == "$requested_version" ]; then
                        success "已安装的temurin版本($temurin_version)与请求的版本(temurin-$requested_version)匹配"
                        return 0
                    else
                        info "已安装的temurin版本($temurin_version)与请求的版本(temurin-$requested_version)不匹配"
                        return 1
                    fi
                fi
            fi
        fi

        # 处理latest或lts版本
        if [[ "$version" == "latest"* || "$version" == "lts"* ]]; then
            # 获取最新安装的版本
            local latest_installed
            latest_installed=$(echo "$installed_versions" | sed -E 's/^[ *]*//; s/ *\(.*\)//' | sort -V | tail -1 | xargs)

            if [ -n "$latest_installed" ]; then
                info "检测到 $lang 已安装最新版本: $latest_installed"
                # 设置为全局版本
                (cd "$HOME" && asdf global "$lang" "$latest_installed")
                return 0
            fi
        fi

        # 对于其他语言，检查请求的版本是否在列表中
        if echo "$installed_versions" | sed -E 's/^[ *]*//; s/ *\(.*\)//' | grep -q -x "$version"; then
            success "$lang $version 已安装"

            # 如果尚未设置，则设置为全局版本
            local current_global_name
            current_global_name=$(asdf current "$lang" 2>/dev/null | awk '{print $1}')

            if [ "$current_global_name" != "$version" ]; then
                (cd "$HOME" && asdf global "$lang" "$version")
            fi
            return 0
        else
            info "$lang $version 未安装"
            return 1
        fi
    }

    install_language() {
        local lang=$1
        local version=$2
        info "正在处理语言: $lang $version"

        # 首先检查是否已安装
        if is_language_installed "$lang" "$version"; then
            return 0
        fi

        # 确保插件已安装
        if ! asdf plugin list | grep -q "^$lang$"; then
            info "正在安装 asdf-$lang 插件..."
            asdf plugin add "$lang" || { error "安装 asdf-$lang 插件失败"; return 1; }
        else
            info "正在更新 asdf-$lang 插件..."
            asdf plugin update "$lang" 2>/dev/null || warn "更新 $lang 插件失败，将使用本地缓存版本"
        fi

        # macOS特定配置
        case "$lang" in
            "nodejs")
                info "检测到 Node.js 安装，设置macOS优化配置..."
                # 在macOS上，通常不需要特殊镜像配置，但可以设置
                export NODE_BUILD_MIRROR_URL="https://npmmirror.com/mirrors/node"
                ;;
            "python")
                info "检测到 Python 安装，设置macOS编译环境..."
                # 确保有必要的编译工具
                if ! command_exists gcc; then
                    warn "未检测到编译工具，建议安装Xcode Command Line Tools: xcode-select --install"
                fi
                ;;
            "java")
                info "检测到 Java 安装，macOS将自动处理..."
                ;;
        esac

        # 安装指定版本
        info "正在安装 $lang $version..."
        if ! asdf install "$lang" "$version"; then
            error "$lang $version 安装失败。"
            # 清理环境变量
            if [ "$lang" = "nodejs" ]; then
                unset NODE_BUILD_MIRROR_URL
            fi
            return 1
        fi

        # 清理环境变量
        if [ "$lang" = "nodejs" ]; then
            info "Node.js 安装完成，正在取消镜像设置..."
            unset NODE_BUILD_MIRROR_URL
        fi

        # 检查安装后是否有可用版本
        local available_versions
        available_versions=$(asdf list "$lang" 2>/dev/null)

        if [ -n "$available_versions" ]; then
            # 获取并设置实际安装的版本为全局版本
            local installed_version

            # 对于Java temurin，精确查找刚安装的版本
            if [[ "$lang" == "java" && "$version" == *"temurin"* ]]; then
                local requested_major_version_pattern
                if [[ "$version" =~ temurin-([0-9]+) ]]; then
                    requested_major_version_pattern="temurin-${BASH_REMATCH[1]}"
                    # 从所有可用版本中，筛选出符合主版本要求的最新版
                    installed_version=$(echo "$available_versions" | grep "$requested_major_version_pattern" | sort -V | tail -1 | xargs)
                fi
            fi

            # 如果上述逻辑未找到版本，则使用通用逻辑
            if [ -z "$installed_version" ]; then
                # 直接使用传入的版本别名（如 lts, latest），asdf global 会自动解析
                installed_version=$version
            fi

            # 设置为全局版本
            (cd "$HOME" && asdf global "$lang" "$installed_version")

            # 获取最终设置的具体版本号用于日志输出
            local final_version
            final_version=$(asdf current "$lang" | awk '{print $2}')
            success "$lang $final_version 安装并设为全局版本。"
            return 0
        else
            error "$lang $version 安装失败，没有找到可用版本"
            return 1
        fi
    }

    # 3. 安装核心语言
    info "--- 开始安装核心开发语言 ---"

    # 创建 .tool-versions 文件（如果不存在）
    if [ ! -f "$HOME/.tool-versions" ]; then
        info "创建全局 .tool-versions 文件..."
        touch "$HOME/.tool-versions"
    fi

    # 安装核心语言
    install_language java latest:temurin-21
    install_language nodejs lts
    install_language python latest
    install_language golang latest

    # 安装和配置其他工具
    info "--- 安装和配置其他开发工具 ---"

    # Java - Maven
    info "安装 Maven..."
    if ! asdf plugin list | grep -q "^maven$"; then
        asdf plugin add maven 2>/dev/null || true
    fi
    asdf install maven latest &>/dev/null || true
    (cd "$HOME" && asdf global maven latest)
    success "Maven 安装完成。"

    # Python - uv (现代化的pip替代品)
    info "安装 uv (pip 替代品)..."
    if ! command -v uv &> /dev/null; then
        if command_exists brew; then
            brew install uv
        else
            curl -LsSf https://astral.sh/uv/install.sh | sh
        fi
    fi
    if command -v uv &> /dev/null; then
        if ! grep -q "alias pip=" "$HOME/.zshrc"; then
            info "配置 uv 作为 pip 的别名..."
            echo -e '\n# 使用 uv 作为 pip 的替代品\nalias pip="uv pip"\nalias pip3="uv pip"' >> "$HOME/.zshrc"
        fi
        success "uv 已安装并配置为 pip 的别名。"
    fi

    # Node.js - pnpm & yarn (通过corepack)
    info "安装 pnpm 和 yarn..."
    if command -v corepack &> /dev/null; then
        corepack enable
        info "使用 corepack 安装 pnpm..."
        corepack prepare pnpm@latest --activate
        info "使用 corepack 安装 yarn..."
        corepack prepare yarn@latest --activate
        info "更新 asdf shims for nodejs..."
        asdf reshim nodejs
        success "pnpm 和 yarn 安装完成。"
    else
        warn "corepack 命令不存在，跳过 pnpm 和 yarn 的安装。请确保 Node.js 版本支持 corepack。"
    fi

    # Go - 开启 go mod
    info "配置 Go 模块..."
    if command -v go &> /dev/null; then
        go env -w GO111MODULE=on
        success "Go 模块配置完成。"
    else
        warn "Go 未正确安装，跳过模块配置。"
    fi

    # 配置 .asdfrc 文件（如果不存在）
    if [ ! -f "$HOME/.asdfrc" ]; then
        info "创建 .asdfrc 配置文件..."
        echo "# 启用传统版本文件支持（如 .nvmrc, .node-version 等）" > "$HOME/.asdfrc"
        echo "legacy_version_file = yes" >> "$HOME/.asdfrc"
        success ".asdfrc 配置文件已创建"
    fi

    # macOS特定的额外配置
    info "--- macOS特定配置 ---"

    # 配置Homebrew环境变量（如果尚未配置）
    if [ -f "$HOME/.zshrc" ] && ! grep -q "HOMEBREW_PREFIX" "$HOME/.zshrc"; then
        info "添加Homebrew环境变量到 .zshrc..."
        echo -e "\n# Homebrew 环境变量" >> "$HOME/.zshrc"
        echo "export HOMEBREW_PREFIX=\"$BREW_PATH\"" >> "$HOME/.zshrc"
        echo "export HOMEBREW_CELLAR=\"$BREW_PATH/Cellar\"" >> "$HOME/.zshrc"
        echo "export HOMEBREW_REPOSITORY=\"$BREW_PATH\"" >> "$HOME/.zshrc"
        echo "export PATH=\"$BREW_PATH/bin:$BREW_PATH/sbin:\$PATH\"" >> "$HOME/.zshrc"
        echo "export MANPATH=\"$BREW_PATH/share/man\${MANPATH+:\$MANPATH}:\"" >> "$HOME/.zshrc"
        echo "export INFOPATH=\"$BREW_PATH/share/info:\${INFOPATH:-}\"" >> "$HOME/.zshrc"
        success "Homebrew环境变量已添加到 .zshrc"
    fi

    # 安装一些macOS开发常用的工具
    info "安装macOS开发常用工具..."
    local mac_tools=("git" "wget" "tree" "htop")
    for tool in "${mac_tools[@]}"; do
        if ! command_exists "$tool"; then
            info "安装 $tool..."
            brew install "$tool" 2>/dev/null || warn "安装 $tool 失败"
        else
            info "$tool 已安装，跳过。"
        fi
    done

    info "--- asdf及多语言开发环境配置完成 (macOS) ---"
    info "请重新启动终端或运行 'source ~/.zshrc' 以应用新配置。"
    info "验证安装: asdf list"
}

# 主执行逻辑
main() {
    echo "========================================"
    echo "🚀 macOS asdf多语言开发环境配置脚本"
    echo "========================================"
    echo ""

    # 检查是否在macOS上运行
    if [[ "$(uname)" != "Darwin" ]]; then
        error "此脚本仅适用于macOS系统"
        exit 1
    fi

    # 执行主要配置
    setup_asdf_dev_env

    echo ""
    echo "========================================"
    echo "✅ 配置完成！"
    echo "========================================"
    echo ""
    echo "下一步操作："
    echo "1. 重新启动终端或运行: source ~/.zshrc"
    echo "2. 验证安装: asdf list"
    echo "3. 查看已安装的语言版本: asdf current"
    echo ""
}

# 如果脚本被直接执行（而不是被source），则运行main函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
