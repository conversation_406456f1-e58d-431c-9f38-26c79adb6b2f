#!/bin/bash

# 引入基础函数
source $(dirname "$0")/00_init_base.sh

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   log "此脚本必须以root用户运行"
   exit 1
fi

DOMAIN="lobe.ibootz.com"
NGINX_INSTALL_DIR="/usr/local/nginx"

log "启动lobe-chat-db服务..."
cd ~/workspace/scripts/docker/lobe-chat-db || {
    log "切换到lobe-chat-db目录失败"
    exit 1
}

if ! docker compose ps | grep -q "lobe-chat-db"; then
    docker compose up -d || {
        log "启动lobe-chat-db服务失败"
        exit 1
    }
else
    log "lobe-chat-db服务已经在运行"
fi

# 创建初始nginx配置(不包含SSL)
log "创建初始nginx配置..."
cat > ${NGINX_INSTALL_DIR}/conf.d/lobe-chat-database.conf << EOF
server {
    listen       80;
    server_name  ${DOMAIN};

    location / {
        proxy_pass http://localhost:3210;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
        proxy_read_timeout 300s;
    }
}
EOF

log "验证初始nginx配置..."
${NGINX_INSTALL_DIR}/sbin/nginx -t || {
    log "nginx配置验证失败"
    exit 1
}

log "重启nginx服务..."
systemctl restart nginx || {
    log "nginx重启失败"
    exit 1
}

log "检查并申请SSL证书..."
if [ -d "/etc/letsencrypt/live/${DOMAIN}" ]; then
    log "SSL证书已存在，尝试更新..."
    certbot renew --nginx \
        --cert-name ${DOMAIN} \
        --nginx-server-root ${NGINX_INSTALL_DIR}/conf \
        --non-interactive || {
        log "SSL证书更新失败"
        exit 1
    }
else
    log "开始申请新的SSL证书..."
    certbot --nginx \
        -d ${DOMAIN} \
        --agree-tos \
        --email <EMAIL> \
        --redirect \
        --nginx-server-root ${NGINX_INSTALL_DIR}/conf \
        --non-interactive || {
        log "SSL证书申请失败"
        exit 1
    }
fi

# 创建最终的nginx配置(包含SSL)
log "创建最终nginx配置..."
cat > ${NGINX_INSTALL_DIR}/conf.d/lobe-chat-database.conf << EOF
server {
    listen       80;
    server_name  ${DOMAIN};
    return 301 https://\$server_name\$request_uri;
}

server {
    listen       443 ssl http2;
    server_name  ${DOMAIN};

    ssl_certificate      /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;
    ssl_certificate_key  /etc/letsencrypt/live/${DOMAIN}/privkey.pem;

    add_header Strict-Transport-Security "max-age=31536000" always;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://localhost:3210;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
        proxy_read_timeout 300s;
    }
}
EOF

log "验证最终nginx配置..."
${NGINX_INSTALL_DIR}/sbin/nginx -t || {
    log "nginx配置验证失败"
    exit 1
}

log "重启nginx服务..."
systemctl restart nginx || {
    log "nginx重启失败"
    exit 1
}

log "检查nginx状态..."
if ! systemctl is-active nginx &>/dev/null; then
    log "nginx状态异常"
    exit 1
fi

log "lobe-chat-db服务部署完成" 