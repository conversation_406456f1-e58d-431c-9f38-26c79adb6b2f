#!/bin/bash

echo -e "\n\n\033[1;36m=========== 添加 vagrant’s public key ===============\033[0m"
mkdir -p /home/<USER>/.ssh
echo "ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEA6NF8iallvQVp22WDkTkyrtvp9eWW6A8YVr+kz4TjGYe7gHzIw+niNltGEFHzD8+v1I2YJ6oXevct1YeS0o9HZyN1Q9qgCgzUFtdOKLv6IedplqoPkcmF0aYet2PkEDo3MlTBckFXPITAMzF8dJSIFo9D8HfdOV0IAdx4O7PtixWKn5y2hMNG0zQPyUecp4pzC6kivAIhyfHilFR61RGL+GPXQ2MWZWFYbAGjyiYJnAmCP3NOTd0jMZEnDkbUvxhMmBYSdETk1rRgm+R4LOzFUGaHqHDLKLX+FIPKcF96hrucXzcWyLbIbEgE98OHlnVYCzRdK8jlqm8tehUc9c9WhQ== vagrant insecure public key" > /home/<USER>/.ssh/authorized_keys
chmod 0600 /home/<USER>/.ssh/authorized_keys
chown -R vagrant:vagrant /home/<USER>/.ssh

# 确保SSH服务启动并启用
systemctl enable sshd
systemctl start sshd
