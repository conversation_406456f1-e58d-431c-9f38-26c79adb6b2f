[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
pid-file	= /var/run/mysqld/mysqld.pid
socket		= /var/run/mysqld/mysqld.sock
datadir		= /var/lib/mysql
#log-error	= /var/log/mysql/error.log
# 默认只接受本地连接，这里修改为接受所有连接
bind-address	= 0.0.0.0
# 禁用符号链接以防止安全风险
symbolic-links = 0

# 将MySQL日志文件时间戳修改为跟随系统时区，解决日志时间与东八区差8小时的问题
log_timestamps = SYSTEM

# 取消所有校验模式，开发环境下更灵活
sql_mode=

# 设置MySQL支持utf8mb4编码和字符集
character-set-client-handshake = FALSE
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# 允许导入的SQL文件大小，开发环境设置较大以便导入测试数据
max_allowed_packet = 1G

# 禁止MySQL对外部连接进行DNS解析，提高连接速度
# 注意：开启此选项后，所有远程主机连接授权都要使用IP地址方式
skip-name-resolve

# 连接请求队列大小，开发环境适当降低
back_log = 500

# 设置MySQL的最大连接数，开发环境适当降低
max_connections = 500

# 防止暴力破解，超过100次后禁止连接，成功连接一次后会清零
max_connect_errors = 100

# 设置MySQL打开文件数
open_files_limit = 65535

# 明确默认时间戳行为
explicit_defaults_for_timestamp = 1

# InnoDB缓冲池大小，开发环境适当降低
innodb_buffer_pool_size = 256M

# 事务日志刷新策略
# 1: 每次提交事务时刷新日志到磁盘，安全但性能较低
# 2: 每次提交事务写日志但不刷新，每秒定时刷新，性能好但可能丢失1秒数据
# 0: 每秒刷新，性能最好但崩溃时可能丢失数据
# 开发环境推荐设置为2，平衡性能和安全性
innodb_flush_log_at_trx_commit = 2

# 日志缓冲区大小，开发环境适当降低
innodb_log_buffer_size = 8M

# 数据日志文件大小，开发环境适当降低
innodb_log_file_size = 256M

# 日志文件组中的文件数量
innodb_log_files_in_group = 2

# 交互式连接超时时间（秒）
interactive_timeout = 7200
   
# 非交互式连接超时时间（秒）
wait_timeout = 7200

# 查询缓存大小，MySQL 5.7 仍支持查询缓存，对开发环境有帮助
# MySQL 8.0 已移除此功能
query_cache_size = 64M
query_cache_type = 1

# 临时表大小限制
tmp_table_size = 64M
max_heap_table_size = 64M

# 开发环境下启用慢查询日志，帮助优化SQL
slow_query_log = 1
slow_query_log_file = /var/lib/mysql/mysql-slow.log
long_query_time = 2

# 开发环境下可以启用通用日志，方便调试
# general_log = 1
# general_log_file = /var/lib/mysql/mysql-general.log

# 表名不区分大小写，开发环境更友好
lower_case_table_names = 1

# 开发环境下可以设置较大的排序缓冲区
sort_buffer_size = 4M
join_buffer_size = 4M

# 开启性能模式，帮助开发者诊断性能问题
performance_schema = ON
