# 代理配置
$defaultProxyHost = "127.0.0.1"
$defaultProxyPort = "7890"
$defaultProxyUrl = "http://${defaultProxyHost}:${defaultProxyPort}"

# 检查软件是否安装的函数
function Test-Software {
    param($name)
    if (-not (Get-Command $name -ErrorAction SilentlyContinue)) {
        Write-Host "❌ $name 未安装" -ForegroundColor Red
        return $false
    }
    return $true
}

# 获取代理 URL 的函数
function Get-ProxyUrl {
    $proxyHost = Read-Host "请输入代理主机地址 (默认: $defaultProxyHost)"
    $proxyPort = Read-Host "请输入代理端口 (默认: $defaultProxyPort)"

    if ([string]::IsNullOrWhiteSpace($proxyHost)) { $proxyHost = $defaultProxyHost }
    if ([string]::IsNullOrWhiteSpace($proxyPort)) { $proxyPort = $defaultProxyPort }

    return "http://${proxyHost}:${proxyPort}"
}

# 在现有代码中添加 VSCode 代理设置函数
function Set-VSCodeProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        # 获取 VSCode 设置文件路径
        $settingsPath = "$env:APPDATA\Code\User\settings.json"

        # 如果文件不存在，创建一个空的设置文件
        if (-not (Test-Path $settingsPath)) {
            New-Item -Path $settingsPath -ItemType File -Force | Out-Null
            Set-Content -Path $settingsPath -Value "{}" -Force
        }

        # 读取现有设置
        $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
        if ($null -eq $settings) {
            $settings = [PSCustomObject]@{}
        }

        if ($enable) {
            # 设置代理
            $settings | Add-Member -NotePropertyName "http.proxy" -NotePropertyValue $proxyUrl -Force
        } else {
            # 移除代理
            $settings.PSObject.Properties.Remove("http.proxy")
        }

        # 保存设置
        $settings | ConvertTo-Json -Depth 10 | Set-Content -Path $settingsPath -Force
        Write-Host "✅ VSCode 代理已$(if ($enable) { '设置' } else { '清除' })" -ForegroundColor Green
    } catch {
        Write-Host "❌ VSCode 代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 设置当前会话的代理环境变量
function Set-SessionProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        if ($enable) {
            # 设置当前会话的环境变量
            $env:http_proxy = $proxyUrl
            $env:https_proxy = $proxyUrl
            $env:all_proxy = $proxyUrl
            $env:no_proxy = 'localhost,127.0.0.1,localaddress,.localdomain.com,::1'

            Write-Host "✅ 当前会话代理已设置" -ForegroundColor Green
        } else {
            # 清除当前会话的环境变量
            Remove-Item Env:http_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:https_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:all_proxy -ErrorAction SilentlyContinue
            Remove-Item Env:no_proxy -ErrorAction SilentlyContinue

            Write-Host "✅ 当前会话代理已清除" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ 当前会话代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 更新 Gradle 代理设置函数，支持没有全局安装 Gradle 的情况
function Set-GradleProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    try {
        # 获取 Gradle 用户目录
        $gradleUserHome = if ($env:GRADLE_USER_HOME) { $env:GRADLE_USER_HOME } else { "$env:USERPROFILE\.gradle" }
        $gradlePropertiesPath = Join-Path $gradleUserHome "gradle.properties"

        # 确保目录存在
        if (-not (Test-Path $gradleUserHome)) {
            New-Item -Path $gradleUserHome -ItemType Directory -Force | Out-Null
        }

        # 解析代理 URL
        if ($enable -and $proxyUrl) {
            $uri = [System.Uri]$proxyUrl
            $proxyHost = $uri.Host
            $proxyPort = $uri.Port
        }

        # 读取现有的 gradle.properties 文件
        $properties = @()
        if (Test-Path $gradlePropertiesPath) {
            $properties = Get-Content $gradlePropertiesPath
        }

        # 移除现有的代理配置
        $properties = $properties | Where-Object {
            $_ -notmatch '^systemProp\.http\.proxyHost=' -and
            $_ -notmatch '^systemProp\.http\.proxyPort=' -and
            $_ -notmatch '^systemProp\.https\.proxyHost=' -and
            $_ -notmatch '^systemProp\.https\.proxyPort=' -and
            $_ -notmatch '^systemProp\.http\.nonProxyHosts=' -and
            $_ -notmatch '^systemProp\.https\.nonProxyHosts='
        }

        if ($enable) {
            # 添加新的代理配置
            $properties += "systemProp.http.proxyHost=$proxyHost"
            $properties += "systemProp.http.proxyPort=$proxyPort"
            $properties += "systemProp.https.proxyHost=$proxyHost"
            $properties += "systemProp.https.proxyPort=$proxyPort"
            # 修复nonProxyHosts设置，使用更完整的排除列表
            $nonProxyHosts = "localhost|127.*|[::1]|*.local|*.localdomain|10.*|192.168.*|172.16.*|172.17.*|172.18.*|172.19.*|172.20.*|172.21.*|172.22.*|172.23.*|172.24.*|172.25.*|172.26.*|172.27.*|172.28.*|172.29.*|172.30.*|172.31.*"
            $properties += "systemProp.http.nonProxyHosts=$nonProxyHosts"
            $properties += "systemProp.https.nonProxyHosts=$nonProxyHosts"

            # 不设置GRADLE_OPTS环境变量，避免与gradlew冲突
            # 移除可能存在的GRADLE_OPTS设置
            Remove-Item Env:GRADLE_OPTS -ErrorAction SilentlyContinue

            Write-Host "✅ Gradle 配置文件代理已设置，已清除可能冲突的环境变量" -ForegroundColor Green
        } else {
            # 清除环境变量
            Remove-Item Env:GRADLE_OPTS -ErrorAction SilentlyContinue
            Write-Host "✅ Gradle 环境变量代理已清除" -ForegroundColor Green
        }

        # 保存文件
        $properties | Set-Content -Path $gradlePropertiesPath -Force
        Write-Host "✅ Gradle 配置文件代理已$(if ($enable) { '设置' } else { '清除' }) ($gradlePropertiesPath)" -ForegroundColor Green

        # 如果是Flutter项目，给出额外提示
        if ((Test-Path "pubspec.yaml") -or (Test-Path "android/gradlew.bat") -or (Test-Path "android/gradlew")) {
            if ($enable) {
                Write-Host "ℹ️  检测到Flutter项目，代理配置已应用到Gradle。如果仍有问题，请尝试运行 'flutter clean' 后重新构建" -ForegroundColor Yellow
            } else {
                Write-Host "ℹ️  检测到Flutter项目，Gradle代理已清除。建议运行 'flutter clean' 清理缓存" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "❌ Gradle 代理$(if ($enable) { '设置' } else { '清除' })失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 添加Flutter项目特定的gradle代理修复函数
function Fix-FlutterGradleProxy {
    param (
        [string]$proxyUrl,
        [bool]$enable = $true
    )

    Write-Host "`n正在为Flutter项目配置Gradle代理..." -ForegroundColor Cyan

    # 首先设置全局gradle代理
    Set-GradleProxy -proxyUrl $proxyUrl -enable $enable

    # 检查是否是Flutter项目
    if (-not (Test-Path "pubspec.yaml")) {
        Write-Host "⚠️  当前目录不是Flutter项目根目录" -ForegroundColor Yellow
        return
    }

    # 检查android目录下的gradle配置
    $androidGradlePropsPath = "android/gradle.properties"
    if (Test-Path "android") {
        try {
            # 读取android项目的gradle.properties
            $androidProperties = @()
            if (Test-Path $androidGradlePropsPath) {
                $androidProperties = Get-Content $androidGradlePropsPath
            }

            # 移除现有的代理配置
            $androidProperties = $androidProperties | Where-Object {
                $_ -notmatch '^systemProp\.http\.proxyHost=' -and
                $_ -notmatch '^systemProp\.http\.proxyPort=' -and
                $_ -notmatch '^systemProp\.https\.proxyHost=' -and
                $_ -notmatch '^systemProp\.https\.proxyPort=' -and
                $_ -notmatch '^systemProp\.http\.nonProxyHosts=' -and
                $_ -notmatch '^systemProp\.https\.nonProxyHosts='
            }

            if ($enable -and $proxyUrl) {
                $uri = [System.Uri]$proxyUrl
                $proxyHost = $uri.Host
                $proxyPort = $uri.Port

                # 添加代理配置到android项目
                $androidProperties += "systemProp.http.proxyHost=$proxyHost"
                $androidProperties += "systemProp.http.proxyPort=$proxyPort"
                $androidProperties += "systemProp.https.proxyHost=$proxyHost"
                $androidProperties += "systemProp.https.proxyPort=$proxyPort"
                $nonProxyHosts = "localhost|127.*|[::1]|*.local|*.localdomain|10.*|192.168.*|172.16.*|172.17.*|172.18.*|172.19.*|172.20.*|172.21.*|172.22.*|172.23.*|172.24.*|172.25.*|172.26.*|172.27.*|172.28.*|172.29.*|172.30.*|172.31.*"
                $androidProperties += "systemProp.http.nonProxyHosts=$nonProxyHosts"
                $androidProperties += "systemProp.https.nonProxyHosts=$nonProxyHosts"
            }

            # 保存android项目的gradle.properties
            $androidProperties | Set-Content -Path $androidGradlePropsPath -Force
            Write-Host "✅ Flutter Android项目的Gradle代理已$(if ($enable) { '设置' } else { '清除' })" -ForegroundColor Green

        } catch {
            Write-Host "❌ 配置Flutter Android项目Gradle代理失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 建议清理操作
    if ($enable) {
        Write-Host "`n建议执行以下命令清理缓存：" -ForegroundColor Yellow
        Write-Host "flutter clean" -ForegroundColor White
        Write-Host "cd android && ./gradlew clean && cd .." -ForegroundColor White
    }
}

# 修改 proxyon 函数
function proxyon {
    param(
        [string]$proxyUrl = $null
    )

    if ([string]::IsNullOrWhiteSpace($proxyUrl)) {
        $proxyUrl = Get-ProxyUrl
    }

    Write-Host "`n正在配置代理服务... ($proxyUrl)" -ForegroundColor Cyan

    # 设置当前会话环境变量代理
    Set-SessionProxy -proxyUrl $proxyUrl -enable $true

    # 设置 Git 代理
    if (Test-Software "git") {
        try {
            git config --global http.proxy $proxyUrl | Out-Null
            git config --global https.proxy $proxyUrl | Out-Null
            Write-Host "✅ Git 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ Git 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 NPM 代理
    if (Test-Software "npm") {
        try {
            npm config set proxy $proxyUrl | Out-Null
            npm config set https-proxy $proxyUrl | Out-Null
            Write-Host "✅ NPM 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ NPM 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 Yarn 代理
    if (Test-Software "yarn") {
        try {
            yarn config set proxy $proxyUrl 2>&1 | Out-Null
            yarn config set https-proxy $proxyUrl 2>&1 | Out-Null
            Write-Host "✅ Yarn 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ Yarn 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 PNPM 代理
    if (Test-Software "pnpm") {
        try {
            pnpm config set proxy $proxyUrl | Out-Null
            pnpm config set https-proxy $proxyUrl | Out-Null
            Write-Host "✅ PNPM 代理已设置" -ForegroundColor Green
        } catch {
            Write-Host "❌ PNPM 代理设置失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 设置 Gradle 代理（无论是否全局安装都会设置）
    Set-GradleProxy -proxyUrl $proxyUrl -enable $true

    # 设置 VSCode 代理
    Set-VSCodeProxy -proxyUrl $proxyUrl -enable $true

    Write-Host "`n✨ 代理服务已启动" -ForegroundColor Green
}

function proxyoff {
    Write-Host "`n正在关闭代理服务..." -ForegroundColor Cyan

    # 清除当前会话环境变量代理
    Set-SessionProxy -proxyUrl "" -enable $false

    # 清除 Git 代理
    if (Test-Software "git") {
        try {
            git config --global --unset http.proxy
            git config --global --unset https.proxy
            Write-Host "✅ Git 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ Git 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 NPM 代理
    if (Test-Software "npm") {
        try {
            npm config delete proxy | Out-Null
            npm config delete https-proxy | Out-Null
            Write-Host "✅ NPM 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ NPM 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 Yarn 代理
    if (Test-Software "yarn") {
        try {
            yarn config delete proxy 2>&1 | Out-Null
            yarn config delete https-proxy 2>&1 | Out-Null
            Write-Host "✅ Yarn 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ Yarn 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 PNPM 代理
    if (Test-Software "pnpm") {
        try {
            pnpm config delete proxy | Out-Null
            pnpm config delete https-proxy | Out-Null
            Write-Host "✅ PNPM 代理已清除" -ForegroundColor Green
        } catch {
            Write-Host "❌ PNPM 代理清除失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

    # 清除 Gradle 代理
    Set-GradleProxy -proxyUrl "" -enable $false

    # 清除 VSCode 代理
    Set-VSCodeProxy -proxyUrl "" -enable $false

    Write-Host "`n✨ 代理服务已关闭" -ForegroundColor Green
}

# 显示当前代理状态的函数
function proxystate {
    Write-Host "`n当前代理状态：" -ForegroundColor Cyan

    Write-Host "`n当前会话环境变量代理："
    Write-Host "HTTP_PROXY: $env:http_proxy"
    Write-Host "HTTPS_PROXY: $env:https_proxy"
    Write-Host "ALL_PROXY: $env:all_proxy"
    Write-Host "NO_PROXY: $env:no_proxy"

    if (Test-Software "git") {
        Write-Host "`nGit 代理："
        git config --global --get http.proxy
        git config --global --get https.proxy
    }

    if (Test-Software "npm") {
        Write-Host "`nNPM 代理："
        npm config get proxy
        npm config get https-proxy
    }

    if (Test-Software "yarn") {
        Write-Host "`nYarn 代理："
        yarn config get proxy
        yarn config get https-proxy
    }

    if (Test-Software "pnpm") {
        Write-Host "`nPNPM 代理："
        pnpm config get proxy
        pnpm config get https-proxy
    }

    # 显示 Gradle 代理状态
    Write-Host "`nGradle 代理："
    try {
        $gradleUserHome = if ($env:GRADLE_USER_HOME) { $env:GRADLE_USER_HOME } else { "$env:USERPROFILE\.gradle" }
        $gradlePropertiesPath = Join-Path $gradleUserHome "gradle.properties"

        Write-Host "配置文件: $gradlePropertiesPath"
        if (Test-Path $gradlePropertiesPath) {
            $properties = Get-Content $gradlePropertiesPath
            $httpProxyHost = ($properties | Where-Object { $_ -match '^systemProp\.http\.proxyHost=' }) -replace '^systemProp\.http\.proxyHost=', ''
            $httpProxyPort = ($properties | Where-Object { $_ -match '^systemProp\.http\.proxyPort=' }) -replace '^systemProp\.http\.proxyPort=', ''
            $httpsProxyHost = ($properties | Where-Object { $_ -match '^systemProp\.https\.proxyHost=' }) -replace '^systemProp\.https\.proxyHost=', ''
            $httpsProxyPort = ($properties | Where-Object { $_ -match '^systemProp\.https\.proxyPort=' }) -replace '^systemProp\.https\.proxyPort=', ''
            $nonProxyHosts = ($properties | Where-Object { $_ -match '^systemProp\.http\.nonProxyHosts=' }) -replace '^systemProp\.http\.nonProxyHosts=', ''

            if ($httpProxyHost -and $httpProxyPort) {
                Write-Host "HTTP_PROXY: http://${httpProxyHost}:${httpProxyPort}"
            }
            if ($httpsProxyHost -and $httpsProxyPort) {
                Write-Host "HTTPS_PROXY: http://${httpsProxyHost}:${httpsProxyPort}"
            }
            if ($nonProxyHosts) {
                Write-Host "NON_PROXY_HOSTS: $nonProxyHosts"
            }
            if (-not $httpProxyHost -and -not $httpsProxyHost) {
                Write-Host "配置文件中未设置代理"
            }
        } else {
            Write-Host "gradle.properties 文件不存在"
        }

        # 显示环境变量状态（应该为空，避免冲突）
        if ($env:GRADLE_OPTS) {
            Write-Host "⚠️  环境变量 GRADLE_OPTS: $env:GRADLE_OPTS" -ForegroundColor Yellow
            Write-Host "   建议清除此环境变量以避免与配置文件冲突" -ForegroundColor Yellow
        } else {
            Write-Host "✅ 环境变量 GRADLE_OPTS: 未设置（推荐）" -ForegroundColor Green
        }

        # 检查是否有 gradlew 文件
        if ((Test-Path "gradlew") -or (Test-Path "gradlew.bat")) {
            Write-Host "✅ 检测到 Gradle Wrapper，代理配置将自动生效" -ForegroundColor Green
        } elseif ((Test-Path "android/gradlew") -or (Test-Path "android/gradlew.bat")) {
            Write-Host "✅ 检测到 Android/Flutter 项目的 Gradle Wrapper" -ForegroundColor Green
        } else {
            Write-Host "ℹ️  当前目录没有 Gradle Wrapper，但全局配置仍然有效" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 无法读取 Gradle 设置: $($_.Exception.Message)" -ForegroundColor Red
    }

    # 添加 VSCode 代理状态显示
    try {
        $settingsPath = "$env:APPDATA\Code\User\settings.json"
        if (Test-Path $settingsPath) {
            $settings = Get-Content -Path $settingsPath -Raw | ConvertFrom-Json
            Write-Host "`nVSCode 代理："
            Write-Host "HTTP_PROXY: $($settings.'http.proxy')"
        }
    } catch {
        Write-Host "❌ 无法读取 VSCode 设置: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 弹出提示信息
# Write-Host @"
# 代理工具使用说明：
# - proxyon [proxyUrl]  : 启动代理（可选：指定代理URL）
# - proxyoff           : 关闭代理
# - proxystate         : 查看当前代理状态

# Gradle 代理支持：
# - 支持全局安装的 Gradle
# - 支持 Gradle Wrapper (gradlew/gradlew.bat)
# - 通过配置文件和环境变量双重保障
# "@ -ForegroundColor Yellow
