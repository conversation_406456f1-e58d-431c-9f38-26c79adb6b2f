services:
  one-api:
    image: "${REGISTRY:-docker.io}/justsong/one-api:latest"
    container_name: one-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "${ONE_API_PORT:-3000}:3000"
    volumes:
      - ./data/oneapi:/data
      - ./logs:/app/logs
    env_file:
      - ./.env
    environment:
      - SQL_DSN=${RDS_USER}:${RDS_PASSWORD}@tcp(${RDS_HOST}:${RDS_PORT})/${RDS_DATABASE}
      - REDIS_CONN_STRING=redis://${REDIS_HOST:-redis}:${REDIS_PORT:-6379}
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - redis
    healthcheck:
      test: [ "CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $2}'" ]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: "${REGISTRY:-docker.io}/redis:${REDIS_VERSION:-latest}"
    container_name: redis
    restart: always
    env_file:
      - ./.env
