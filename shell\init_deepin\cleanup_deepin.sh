#!/bin/zsh

# 检查是否以 root 身份运行
if [ "$(id -u)" = "0" ]; then
    echo "错误: 请不要以 root 身份运行此脚本"
    exit 1
fi

cleanup_docker() {
    echo "清理 Docker..."
    sudo apt remove -y docker-ce docker-ce-cli containerd.io docker-compose-plugin || echo "清理 Docker 失败"
}

# 清理 Node.js 环境
cleanup_node() {
    echo "清理 Node.js 环境..."
    # 动态确定 NVM 目录
    NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
    
    # 删除 NVM
    rm -rf "$NVM_DIR"
    
    # 清理 NVM 相关环境变量（包括 nvm.sh 的加载）
    sed -i '/export NVM_DIR/d' ~/.bashrc
    sed -i '/\[ -s "$NVM_DIR\/nvm.sh" \]/d' ~/.bashrc
    sed -i '/export NVM_DIR/d' ~/.zshrc
    sed -i '/\[ -s "$NVM_DIR\/nvm.sh" \]/d' ~/.zshrc
    
    # 清理 npm 配置
    rm -rf ~/.npm
}

# 清理 Miniconda
cleanup_miniconda() {
    echo "清理 Miniconda..."
    # 删除 Miniconda 目录
    rm -rf ~/DevTools/miniconda
    
    # 清理环境变量
    sed -i '/miniconda/d' ~/.bashrc
    sed -i '/miniconda/d' ~/.zshrc

    echo "清理 Conda-Java 环境..."
    # 删除 Java 相关 conda 环境
    ~/DevTools/miniconda/bin/conda remove -n java21 --all -y || echo "删除 Java 21 环境失败"
}

# 清理 Vagrant
cleanup_vagrant() {
    echo "清理 Vagrant..."
    sudo apt remove -y vagrant virtualbox || echo "清理 Vagrant 和 VirtualBox 失败"
    
    # 删除 Vagrant 源
    sudo rm -f /etc/apt/sources.list.d/hashicorp.list || echo "删除 Vagrant 源失败"
}

# 清理 Flutter
cleanup_flutter() {
    echo "清理 Flutter..."
    
    # 删除 Flutter 目录
    rm -rf ~/DevTools/flutter || { echo "删除 Flutter 目录失败"; exit 1; }
    
    # 清理环境变量
    sed -i '/export PATH="$HOME\/DevTools\/flutter\/bin:$PATH"/d' ~/.bashrc || { echo "清理 .bashrc 中的 Flutter 环境变量失败"; exit 1; }
    sed -i '/export PATH="$HOME\/DevTools\/flutter\/bin:$PATH"/d' ~/.zshrc || { echo "清理 .zshrc 中的 Flutter 环境变量失败"; exit 1; }
    
    # 清理 Flutter 配置
    rm -rf ~/.flutter || { echo "删除 Flutter 配置失败"; exit 1; }
    
    echo "Flutter 清理完成！"
}

# 主函数
main() {
    echo "请选择要清理的组件："
    echo "0. 清理所有组件"
    echo "1. 清理 Docker"
    echo "2. 清理 Node.js"
    echo "3. 清理 Miniconda"
    echo "4. 清理 Vagrant"
    echo "5. 清理 Flutter"
    echo "请输入序号（0-5）： "
    read choice
        
    case $choice in
        0) 
            cleanup_docker
            cleanup_node
            cleanup_miniconda
            cleanup_vagrant
            cleanup_flutter ;;
        1) cleanup_docker ;;
        2) cleanup_node ;;
        3) cleanup_miniconda ;;
        4) cleanup_vagrant ;;
        5) cleanup_flutter ;;
        *) echo "无效的选择，请输入 0 到 5 之间的数字。" ;;
    esac
    
    # 清理可能的残留包
    sudo apt autoremove -y
    sudo apt clean
    
    echo "清理完成！建议重启系统以应用所有更改。"
    echo "注意事项："
    echo "1. 某些配置文件可能需要手动检查和清理"
    echo "2. 个人数据和项目文件不会被删除"
    echo "3. 请运行 'source ~/.bashrc' 或 'source ~/.zshrc' 以更新环境变量"
}

main