import os
from dotenv import load_dotenv

# 从父目录的 .env 文件中加载环境变量
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

# --- API 配置 ---
API_BASE_URL = os.getenv("API_BASE_URL")
API_KEY = os.getenv("API_KEY")
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", 300))

# --- 模型配置 ---
CONTESTANT_MODELS = [model.strip() for model in os.getenv("CONTESTANT_MODELS", "").split(',') if model.strip()]
JUDGE_MODELS = [model.strip() for model in os.getenv("JUDGE_MODELS", "").split(',') if model.strip()]

# --- 评测配置 ---
EVALUATION_DIMENSIONS = [dim.strip() for dim in os.getenv("EVALUATION_DIMENSIONS", "").split(',') if dim.strip()]

# --- 思考过程配置 ---
ENABLE_THINKING_PROCESS = os.getenv("ENABLE_THINKING_PROCESS", "false").lower() == "true"
CONTESTANT_MODEL_TEMPERATURE = float(os.getenv("CONTESTANT_MODEL_TEMPERATURE", 0.7))

# --- 输出配置 ---
RESULTS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'results'))

def get_config_summary():
    """返回当前配置的摘要信息。"""
    summary = (
        "--- 配置摘要 ---\n"
        f"API 基础地址: {API_BASE_URL}\n"
        f"参赛模型: {CONTESTANT_MODELS}\n"
        f"裁判模型: {JUDGE_MODELS}\n"
        f"评测维度: {EVALUATION_DIMENSIONS}\n"
        f"结果目录: {RESULTS_DIR}\n"
        f"启用思考过程: {ENABLE_THINKING_PROCESS}\n"
        f"参赛模型温度: {CONTESTANT_MODEL_TEMPERATURE}\n"
        "--------------------------"
    )
    return summary

if __name__ == '__main__':
    # 如果结果目录不存在，则创建它
    if not os.path.exists(RESULTS_DIR):
        os.makedirs(RESULTS_DIR)
        print(f"已创建结果目录: {RESULTS_DIR}")

    print(get_config_summary())
