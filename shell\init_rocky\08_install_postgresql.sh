#!/bin/bash

source $(dirname "$0")/00_init_base.sh

# 定义PostgreSQL版本
PG_VERSION="16"

log "安装和配置PostgreSQL-${PG_VERSION}"
dnf install -y https://download.postgresql.org/pub/repos/yum/reporpms/EL-9-x86_64/pgdg-redhat-repo-latest.noarch.rpm
dnf -qy module disable postgresql
dnf install -y postgresql${PG_VERSION}-server
/usr/pgsql-${PG_VERSION}/bin/postgresql-${PG_VERSION}-setup initdb

# 创建没有版本号的服务名
ln -s /usr/lib/systemd/system/postgresql-${PG_VERSION}.service /usr/lib/systemd/system/postgresql.service
systemctl daemon-reload

# 修改配置文件
sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" /var/lib/pgsql/${PG_VERSION}/data/postgresql.conf
echo "host    all             all             0.0.0.0/0               scram-sha-256" >> /var/lib/pgsql/${PG_VERSION}/data/pg_hba.conf

# 启动服务
systemctl enable postgresql
systemctl start postgresql

# 设置密码
sudo -u postgres psql -c "ALTER USER postgres WITH PASSWORD 'postgres';"

# 重启服务以应用新配置
systemctl restart postgresql

# 安装 vector 插件
dnf install -y pgvector_16

# 创建 vector 扩展
sudo -u postgres psql -c "CREATE EXTENSION IF NOT EXISTS vector;"

# 列出当前已安装和启用的所有插件
sudo -u postgres psql -c "SELECT * FROM pg_extension;"
