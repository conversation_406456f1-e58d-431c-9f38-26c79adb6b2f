services:
  gemini2api:
    build:
      context: ./app
    container_name: gemini2api
    restart: always
    network_mode: "host" # 使用宿主机网络模式，使容器可以直接访问宿主机的代理
    environment:
      - HTTP_PROXY=http://host.docker.internal:7890 # 替换为你的代理地址和端口
      - HTTPS_PROXY=http://host.docker.internal:7890 # 替换为你的代理地址和端口
      - NO_PROXY=localhost,127.0.0.1,0.0.0.0,host.docker.internal # 根据需要添加其他不需要代理的地址
    env_file:
      - .env
    volumes:
      - ./app:/app
