#!/bin/bash

# ==============================================================================
#                  全能开发环境自动化配置脚本 for WSL2 Ubuntu (模块化加载器)
# ==============================================================================
#
# Github: https://github.com/your-repo/scripts
# Author: Your Name
# Version: 3.0.0 (Modular with Interactive Mode)
#
# 脚本功能:
#   - 本脚本作为加载器，按顺序执行 modules/ 目录下的所有模块化脚本。
#   - 实现了对原单体脚本的解耦，提高了可读性、可维护性和可扩展性。
#   - 支持三种执行模式：单步执行、完整执行和从特定步骤开始执行。
#
# 使用方法:
#   1. 确保 modules/ 目录与本脚本位于同一目录下。
#   2. 赋予执行权限: chmod +x init_wsl2_ubuntu.sh
#   3. 运行脚本: ./init_wsl2_ubuntu.sh [选项]
#
# 可选参数:
#   --all 或 -a: 完整执行所有步骤（默认模式）。
#   --step 或 -s: 单步执行模式，可以选择具体执行哪一步。
#   --from 或 -f <步骤号>: 从指定步骤开始执行后续所有步骤。
#   --help 或 -h: 显示帮助信息。
#
# 环境变量 (可选):
#   GITHUB_USERNAME: 你的GitHub用户名，用于自动初始化dotfiles。
#   DOTFILES_REPO: 你的dotfiles仓库URL，用于自动初始化dotfiles。
#   例如: GITHUB_USERNAME=your_github_id ./init_wsl2_ubuntu.sh
#
# ==============================================================================


set -e

# 获取脚本所在目录
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)
MODULES_DIR="$SCRIPT_DIR/modules"

# 全局变量
DECLARED_STEPS=()
STEP_NAMES=()
STEP_DESCRIPTIONS=()
TOTAL_STEPS=0
EXECUTION_MODE="all" # 默认为完整执行模式
START_STEP=1          # 默认从第一步开始
SELECTED_STEP=0       # 单步执行时选择的步骤

# 加载通用工具函数
if [ -f "$MODULES_DIR/utils.sh" ]; then
    # shellcheck source=modules/utils.sh
    source "$MODULES_DIR/utils.sh"
else
    echo "[ERROR] 核心工具脚本 utils.sh 未找到，无法继续。" >&2
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << EOF
使用方法: $(basename "$0") [选项]

选项:
  --all, -a          完整执行所有步骤（默认模式）
  --step, -s         单步执行模式，可以选择具体执行哪一步
  --from, -f <步骤号> 从指定步骤开始执行后续所有步骤
  --help, -h         显示此帮助信息

示例:
  $(basename "$0")           # 默认完整执行所有步骤
  $(basename "$0") --step    # 进入单步执行模式
  $(basename "$0") --from 3  # 从第3步开始执行
EOF
    exit 0
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --all|-a)
                EXECUTION_MODE="all"
                shift
                ;;
            --step|-s)
                EXECUTION_MODE="step"
                shift
                ;;
            --from|-f)
                EXECUTION_MODE="from"
                if [[ -n $2 && $2 =~ ^[0-9]+$ ]]; then
                    START_STEP=$2
                    shift 2
                else
                    error "--from 选项需要一个数字参数"
                fi
                ;;
            --help|-h)
                show_help
                ;;
            *)
                warn "未知选项: $1"
                show_help
                ;;
        esac
    done
}

# 加载所有功能模块并收集步骤信息
load_modules() {
    # 静默加载所有模块
    # 遍历并加载每个模块
    while IFS= read -r module_file; do
        if [ -f "$module_file" ]; then
            # 提取模块编号和名称
            local basename_file=$(basename "$module_file")
            local module_num=$(echo "$basename_file" | grep -oP '^\d+')
            local module_name=$(echo "$basename_file" | sed -E 's/^[0-9]+_(.+)\.sh$/\1/')

            # 从模块文件中提取描述
            local step_number
            step_number=$(basename "$module_file" | cut -d'_' -f1)
            local description
            description=$(grep -E "^#\s*${step_number}\." "$module_file" | sed -E 's/^#\s*[0-9]+\.\s*//' | head -n 1)

            # 如果没有找到描述，使用模块名作为描述
            if [ -z "$description" ]; then
                description="$module_name"
            fi

            # 提取主函数名
            local function_name
            function_name=$(grep -oP '^[a-zA-Z0-9_]+\(\)' "$module_file" | head -1 | sed 's/()$//')

            # 只有当找到函数名时才添加到步骤数组
            if [ -n "$function_name" ]; then
                DECLARED_STEPS+=("$function_name")
                STEP_NAMES+=("$module_name")
                STEP_DESCRIPTIONS+=("$description")
            fi

            # 尝试加载模块
            if ! source "$module_file" 2>/dev/null; then
                error "加载模块失败: $basename_file"
                return 1
            fi
        else
            warn "未找到模块文件: $module_file"
        fi
    done < <(find "$MODULES_DIR" -maxdepth 1 -name '[0-9]*_*.sh' | sort -V)

    # 更新总步骤数
    TOTAL_STEPS=${#DECLARED_STEPS[@]}
}

# 显示步骤菜单并获取用户选择
show_step_menu() {
    echo -e "\n${C_BLUE}=== 可用步骤 ===${C_NC}"
    for ((i=0; i<TOTAL_STEPS; i++)); do
        printf "  ${C_GREEN}%2d.${C_NC} ${C_YELLOW}%-30s${C_NC} - %s\n" "$((i+1))" "${STEP_NAMES[i]}" "${STEP_DESCRIPTIONS[i]:-''}"
    done

    local valid_choice=false
    while [ "$valid_choice" = false ]; do
        echo -en "\n${C_YELLOW}请选择要执行的步骤 (1-$TOTAL_STEPS)${C_NC}: "
        read -r choice

        if [[ $choice =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "$TOTAL_STEPS" ]; then
            SELECTED_STEP=$choice
            valid_choice=true
        else
            warn "无效的选择，请输入 1 到 $TOTAL_STEPS 之间的数字"
        fi
    done
}

# 执行单个步骤
execute_step() {
    local step_index=$1
    local step_num=$((step_index+1))

    if [ "$step_index" -ge 0 ] && [ "$step_index" -lt "$TOTAL_STEPS" ]; then
        local function_name=${DECLARED_STEPS[step_index]}
        local step_name=${STEP_NAMES[step_index]}

        echo -e "\n${C_BLUE}=======================================${C_NC}"
        info "执行步骤 $step_num: $step_name"
        echo -e "${C_BLUE}=======================================${C_NC}\n"

        # 执行对应的函数
        $function_name

        success "步骤 $step_num: $step_name 已完成"
        return 0
    else
        error "无效的步骤索引: $step_index"
        return 1
    fi
}

# 显示主菜单并获取用户选择
show_main_menu() {
    echo -e "\n${C_BLUE}=== 执行模式选择 ===${C_NC}"
    echo -e "  ${C_GREEN}1${C_NC}. 完整执行所有步骤"
    echo -e "  ${C_GREEN}2${C_NC}. 单步执行（选择具体步骤）"
    echo -e "  ${C_GREEN}3${C_NC}. 从指定步骤开始执行"
    echo -e "  ${C_GREEN}4${C_NC}. 退出"

    local valid_choice=false
    while [ "$valid_choice" = false ]; do
        echo -en "\n${C_YELLOW}请选择执行模式 (1-4)${C_NC}: "
        read -r choice

        case $choice in
            1)
                EXECUTION_MODE="all"
                valid_choice=true
                ;;
            2)
                EXECUTION_MODE="step"
                valid_choice=true
                ;;
            3)
                EXECUTION_MODE="from"
                valid_choice=true
                ;;
            4)
                info "已取消执行。"
                exit 0
                ;;
            *)
                warn "无效的选择，请输入 1 到 4 之间的数字"
                ;;
        esac
    done
}

# 主执行函数
main() {
    info "开始执行全能开发环境自动化配置脚本 (模块化 v3.0)..."

    # 解析命令行参数
    parse_args "$@"

    # 加载模块
    load_modules

    # 如果没有找到任何模块
    if [ "$TOTAL_STEPS" -eq 0 ]; then
        error "未找到任何可执行的模块，请检查modules目录。"
        exit 1
    fi

    # 显示找到的模块
    info "找到以下可执行模块:"
    for ((i=0; i<TOTAL_STEPS; i++)); do
        info "  $((i+1)). ${STEP_NAMES[i]}"
    done

    # 如果没有指定执行模式，显示主菜单
    if [ "$EXECUTION_MODE" = "all" ] && [ $# -eq 0 ]; then
        show_main_menu
    fi

    # 根据执行模式执行步骤
    case "$EXECUTION_MODE" in
        "all")
            info "模式: 完整执行所有步骤"

            # 保持sudo权限
            maintain_sudo

            # 执行所有步骤
            for ((i=0; i<TOTAL_STEPS; i++)); do
                execute_step $i || {
                    error "步骤 $((i+1)) 执行失败"
                    exit 1
                }
            done
            ;;

        "step")
            info "模式: 单步执行"

            # 显示步骤菜单并获取用户选择
            show_step_menu

            # 保持sudo权限
            maintain_sudo

            # 执行选定的步骤
            execute_step $((SELECTED_STEP-1)) || {
                error "步骤 $SELECTED_STEP 执行失败"
                exit 1
            }
            ;;

        "from")
            # 如果没有指定起始步骤，提示用户输入
            if [ "$START_STEP" -eq 1 ]; then
                echo -en "\n${C_YELLOW}请输入起始步骤号 (1-$TOTAL_STEPS)${C_NC}: "
                read -r start_step

                if [[ $start_step =~ ^[0-9]+$ ]] && [ "$start_step" -ge 1 ] && [ "$start_step" -le "$TOTAL_STEPS" ]; then
                    START_STEP=$start_step
                else
                    error "无效的起始步骤: $start_step，有效范围为 1-$TOTAL_STEPS"
                fi
            fi

            info "模式: 从步骤 $START_STEP 开始执行"

            # 验证起始步骤
            if [ "$START_STEP" -lt 1 ] || [ "$START_STEP" -gt "$TOTAL_STEPS" ]; then
                error "无效的起始步骤: $START_STEP，有效范围为 1-$TOTAL_STEPS"
            fi

            # 保持sudo权限
            maintain_sudo

            # 从指定步骤开始执行
            for ((i=START_STEP-1; i<TOTAL_STEPS; i++)); do
                execute_step $i || {
                    error "步骤 $((i+1)) 执行失败"
                    exit 1
                }
            done
            ;;
        *)
            error "未知的执行模式: $EXECUTION_MODE"
            ;;
    esac

    # 杀死sudo保持进程
    if [ -n "$SUDO_PID" ]; then
        kill "$SUDO_PID" 2>/dev/null || true
    fi

    # 显示最终说明
    if type final_instructions >/dev/null 2>&1; then
        final_instructions
    else
        warn "未找到最终说明函数，跳过显示。"
    fi

    success "所有自动化任务已成功完成。"
}

# 执行主函数，并传递所有脚本参数
main "$@"
