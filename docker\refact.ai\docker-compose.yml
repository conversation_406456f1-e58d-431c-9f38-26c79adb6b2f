# Refact.ai 自托管部署配置
# 基于官方文档: https://docs.refact.ai/guides/version-specific/self-hosted/
version: '3'

services:
  refact:
    # 使用官方提供的 Refact 自托管镜像
    image: smallcloud/refact_self_hosting
    # 容器名称
    container_name: refact_self_hosting
    # 除非手动停止，否则容器会在崩溃或系统重启后自动重启
    restart: unless-stopped
    # 端口映射：将容器内的 8008 端口映射到主机的 8008 端口
    ports:
      - "8008:8008"
    # 数据卷挂载：将持久化存储卷挂载到容器内的 /perm_storage 目录
    # 所有配置文件、下载的模型权重和日志都存储在这里
    volumes:
      - perm-storage:/perm_storage
    # GPU 资源配置
    deploy:
      resources:
        reservations:
          devices:
            # 使用 NVIDIA 驱动
            - driver: nvidia
              # 使用所有可用的 GPU
              count: all
              # 启用 GPU 功能
              capabilities: [gpu]
    # 可选环境变量配置（根据需要取消注释）
    # environment:
    #   # 指定要使用的模型，默认会使用配置的模型
    #   # - MODEL=模型名称
    #   # 其他可能的环境变量配置

# 定义持久化存储卷
volumes:
  # perm-storage 卷用于存储配置、模型权重和日志
  perm-storage:
    # 指定卷名称，便于在 Docker 卷列表中识别
    name: refact-perm-storage
