# broker 集群名称
brokerClusterName = DefaultCluster
# broker 名称
brokerName = broker-a
#0表示Master，大于0表示不同的slave
brokerId = 0
# 删除文件时间点，默认是凌晨4点
deleteWhen = 04
# 文件保留时间，默认48小时
fileReservedTime = 48
# broker 角色
brokerRole = ASYNC_MASTER
# 刷盘方式
flushDiskType = ASYNC_FLUSH
# 监听的IP地址，需要修改为实际的服务器IP
brokerIP1 = **************
# 监听端口
listenPort = 10911
# nameServer地址
namesrvAddr = namesrv:9876
# 存储路径
storePathRootDir = /home/<USER>/store
# commitLog存储路径
storePathCommitLog = /home/<USER>/store/commitlog
# 消费队列存储路径
storePathConsumeQueue = /home/<USER>/store/consumequeue
# 消息索引存储路径
storePathIndex = /home/<USER>/store/index
# checkpoint文件路径
storeCheckpoint = /home/<USER>/store/checkpoint
# abort文件存储路径
abortFile = /home/<USER>/store/abort
# 关闭ACL认证
aclEnable = false
# 是否允许Broker自动创建Topic
autoCreateTopicEnable = true
# 是否允许Broker自动创建订阅组
autoCreateSubscriptionGroup = true
