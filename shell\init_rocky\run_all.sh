# 检查 Nginx 状态
systemctl status nginx

# 检查证书状态
./nginx-manager.sh certs

# 检查服务配置
./nginx-manager.sh list

# 设置错误时立即退出
set -e

# 此脚本用于自动化初始化 Rocky Linux 系统
# 主要功能包括：
# 1. 检查脚本是否以 root 用户运行
# 2. 加载基础函数
# 3. 让用户选择执行模式和起始脚本
# 4. 按照预定义顺序执行选定的初始化脚本
# 5. 每个脚本执行前后进行日志记录
# 6. 完成后提示用户重启系统以应用更改

# 检查是否以root用户运行脚本
if [ "$(id -u)" != "0" ]; then
   echo "此脚本必须以root用户运行" 1>&2
   exit 1
fi

# 切换到脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 加载基础函数
source ./00_init_base.sh

# 定义脚本执行顺序
scripts=(
   "01_setup_mirrors.sh"
   "02_install_tools.sh"
   "03_setup_rsnapshot.sh"
   "04_system_config.sh"
   "05_setup_ssh.sh"
   "06_install_docker.sh"
   "07_install_dev_env.sh"
   "08_install_postgresql.sh"
   "09_install_zsh.sh"
   "10_gen_key.sh"
)

# 显示执行模式选项
echo "请选择执行模式："
echo "1. 执行单个脚本"
echo "2. 从选定脚本开始执行后续所有脚本"

# 获取执行模式，默认为模式2
read -p "请输入模式编号 [2]: " mode
mode=${mode:-2}

if ! [[ "$mode" =~ ^[12]$ ]]; then
    log "无效的模式选择。请输入1或2。"
    exit 1
fi

# 显示脚本列表供用户选择
echo -e "\n可用的脚本列表："
for i in "${!scripts[@]}"; do
    echo "$((i+1)). ${scripts[i]}"
done

# 获取用户输入，默认为1
read -p "请输入脚本编号 [1]: " choice
choice=${choice:-1}

# 验证用户输入
if ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 1 ] || [ "$choice" -gt "${#scripts[@]}" ]; then
    log "无效的选择。请输入1到${#scripts[@]}之间的数字。"
    exit 1
fi

# 根据模式执行脚本
start_index=$((choice-1))
if [ "$mode" -eq 1 ]; then
    # 执行单个脚本
    script="${scripts[start_index]}"
    if [ -f "$script" ]; then
        log "运行脚本: $script"
        bash "$script"
        log "完成运行: $script"
    else
        log "错误: 脚本 $script 不存在"
        exit 1
    fi
else
    # 从选定脚本开始执行后续所有脚本
    for ((i=start_index; i<${#scripts[@]}; i++)); do
        script="${scripts[i]}"
        if [ -f "$script" ]; then
            log "运行脚本: $script"
            bash "$script"
            log "完成运行: $script"
            echo
        else
            log "警告: 脚本 $script 不存在，已跳过"
        fi
    done
fi

log "所有选定的脚本已执行完毕"
log "请重新启动系统以应用所有更改"
