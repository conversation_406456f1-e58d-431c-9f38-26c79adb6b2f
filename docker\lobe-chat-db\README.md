# LobeChat 与 SearXNG 集成

本项目集成了 SearXNG 搜索引擎，为 LobeChat 提供联网搜索功能。

## 功能特点

- 使用 SearXNG 作为搜索引擎，提供隐私保护的搜索服务
- 支持 LobeChat 的联网搜索功能
- 简单易用的配置和部署

## 快速开始

1. 确保已安装 Docker 和 Docker Compose

2. 先启动 SearXNG 服务
   ```bash
   cd /path/to/scripts/docker/searxng
   chmod +x setup.sh
   ./setup.sh
   ```

3. 然后启动 LobeChat 数据库服务
   ```bash
   cd /path/to/scripts/docker/lobe-chat-db
   docker compose up -d
   ```

4. 访问 SearXNG 服务：http://localhost:9191

## 配置说明

### 环境变量

在 `.env` 文件中，已简化为仅包含以下 SearXNG 相关的环境变量：

```
# SearXNG 相关配置
# SearXNG 基础 URL，用于 LobeChat 联网搜索功能
# 注意：这里使用的是独立部署的 SearXNG 服务
SEARXNG_URL=http://localhost:9191
```

### 自定义 SearXNG 配置

SearXNG 的配置现在已移至独立的 SearXNG 服务中，请参考 `/root/workspace/scripts/docker/searxng/README.md` 文件了解如何配置 SearXNG 服务。

## 注意事项

- 本配置使用独立部署的 SearXNG 服务，详细配置请参考 `/root/workspace/scripts/docker/searxng/README.md`。
- 为确保 LobeChat 能够正常使用 SearXNG 进行联网搜索，请确保 `SEARXNG_URL` 环境变量正确设置为 SearXNG 服务的访问地址。
- 在启动 LobeChat 数据库服务之前，请先启动 SearXNG 服务，以确保网络连接正常。
