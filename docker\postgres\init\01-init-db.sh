#!/bin/bash
set -e

# 创建应用数据库和用户
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- 创建应用数据库
    CREATE DATABASE ${APP_DB:-appdb};

    -- 创建应用用户并设置密码
    CREATE USER ${APP_USER:-appuser} WITH ENCRYPTED PASSWORD '${APP_PASSWORD:-apppassword}';

    -- 授予应用用户对应用数据库的所有权限
    GRANT ALL PRIVILEGES ON DATABASE ${APP_DB:-appdb} TO ${APP_USER:-appuser};

    -- 连接到应用数据库
    \c ${APP_DB:-appdb}

    -- 创建扩展
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    -- 非内置插件，需要自行安装，或者重新编译镜像
    CREATE EXTENSION IF NOT EXISTS "vector";
    -- CREATE EXTENSION IF NOT EXISTS "postgis";

    -- 为应用用户设置默认权限
    ALTER DEFAULT PRIVILEGES FOR USER ${APP_USER:-appuser} IN SCHEMA public
    GRANT ALL ON TABLES TO ${APP_USER:-appuser};

    ALTER DEFAULT PRIVILEGES FOR USER ${APP_USER:-appuser} IN SCHEMA public
    GRANT ALL ON SEQUENCES TO ${APP_USER:-appuser};

    ALTER DEFAULT PRIVILEGES FOR USER ${APP_USER:-appuser} IN SCHEMA public
    GRANT ALL ON FUNCTIONS TO ${APP_USER:-appuser};

    -- 创建只读用户（可选）
    CREATE USER ${READONLY_USER:-readonly} WITH ENCRYPTED PASSWORD '${READONLY_PASSWORD:-readonly}';
    GRANT CONNECT ON DATABASE ${APP_DB:-appdb} TO ${READONLY_USER:-readonly};
    GRANT USAGE ON SCHEMA public TO ${READONLY_USER:-readonly};
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${READONLY_USER:-readonly};

    ALTER DEFAULT PRIVILEGES FOR USER ${APP_USER:-appuser} IN SCHEMA public
    GRANT SELECT ON TABLES TO ${READONLY_USER:-readonly};
EOSQL

# 设置数据库参数（可选）
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "${APP_DB:-appdb}" <<-EOSQL
    -- 设置应用数据库的特定参数
    ALTER DATABASE ${APP_DB:-appdb} SET search_path TO public;
    ALTER DATABASE ${APP_DB:-appdb} SET timezone TO 'Asia/Shanghai';
EOSQL

echo "数据库初始化完成"