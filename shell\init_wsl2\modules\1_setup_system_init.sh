#!/usr/bin/env bash

# ==============================================================================
# 1. 系统初始化与国内镜像加速
# ==============================================================================
setup_system_init() {
    info "--- 开始系统初始化与配置 ---"
    
    # 0. 配置当前用户sudo免密
    local current_user
    current_user=$(whoami)
    if ! sudo grep -q "^${current_user}.*NOPASSWD:ALL" /etc/sudoers; then
        info "正在配置 ${current_user} 用户免密使用sudo..."
        echo "${current_user} ALL=(ALL) NOPASSWD: ALL" | sudo tee -a /etc/sudoers > /dev/null
        success "${current_user} 用户已配置为免密使用sudo。"
    else
        info "${current_user} 用户已配置免密sudo，跳过。"
    fi

    # 1.1 配置APT国内镜像源
    # 检查网络连接
    info "检查网络连接状态..."
    if ! ping -c 1 -W 2 mirrors.aliyun.com &>/dev/null && ! ping -c 1 -W 2 mirrors.tuna.tsinghua.edu.cn &>/dev/null; then
        warn "网络连接不稳定，无法访问国内镜像源，将保留当前APT源配置"
    else
        # 获取系统版本代号
        local ubuntu_codename
        ubuntu_codename=$(lsb_release -cs)
        info "检测到Ubuntu系统版本代号: $ubuntu_codename"
        
        if ! grep -q "mirrors.aliyun.com\|mirrors.tuna.tsinghua.edu.cn" /etc/apt/sources.list; then
            info "检测到未使用国内镜像源，正在配置APT镜像..."
            sudo_with_msg "备份并替换APT源列表..." cp /etc/apt/sources.list /etc/apt/sources.list.bak
            
            # 尝试阿里云镜像
            if ping -c 1 -W 2 mirrors.aliyun.com &>/dev/null; then
                info "使用阿里云镜像源..."
                sudo_with_msg "写入新的APT源列表..." tee /etc/apt/sources.list > /dev/null <<EOF
deb http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-updates main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-updates main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-backports main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-backports main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-security main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ $ubuntu_codename-security main restricted universe multiverse
EOF
            # 备选：清华源
            else
                info "阿里云镜像不可用，使用清华源..."
                sudo_with_msg "写入新的APT源列表..." tee /etc/apt/sources.list > /dev/null <<EOF
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename main restricted universe multiverse
deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-updates main restricted universe multiverse
deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-updates main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-backports main restricted universe multiverse
deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-backports main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-security main restricted universe multiverse
deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ $ubuntu_codename-security main restricted universe multiverse
EOF
            fi
            success "APT镜像源已更新为国内源。"
        else
            info "APT镜像源已配置为国内源，跳过。"
        fi
    fi

    # 1.2 启用Systemd
    if [ ! -f /etc/wsl.conf ] || ! grep -q "systemd=true" /etc/wsl.conf; then
        info "正在配置WSL以启用Systemd..."
        sudo_with_msg "写入WSL配置文件..." tee /etc/wsl.conf > /dev/null <<'EOF'
[boot]
systemd=true
EOF
        warn "WSL配置已更新以启用Systemd。您需要关闭并重启WSL (\`wsl --shutdown\`)才能生效。"
    else
        info "Systemd已在wsl.conf中启用，跳过。"
    fi

    # 1.3 设置时区与Locale
    info "正在更新系统时区为 Asia/Shanghai..."
    sudo_with_msg "设置系统时区为 Asia/Shanghai..." timedatectl set-timezone Asia/Shanghai
    
    info "正在配置系统Locale为 en_US.UTF-8..."
    sudo_with_msg "生成en_US.UTF-8 locale..." locale-gen en_US.UTF-8
    sudo_with_msg "设置系统默认locale..." update-locale LANG=en_US.UTF-8

    # 1.4 更新软件包列表并安装基础依赖
    info "正在更新软件包列表并安装核心开发工具包..."
    sudo_with_msg "更新软件包列表..." apt-get update
    sudo_with_msg "安装核心开发工具包..." apt-get install -y \
        build-essential \
        pkg-config \
        libssl-dev \
        curl \
        wget \
        git \
        zsh \
        tree \
        htop \
        vim \
        zip \
        unzip \
        net-tools \
        iotop \
        lsof \
        tcpdump \
        nmap \
        telnet \
        ca-certificates \
        gnupg \
        make \
        zlib1g-dev \
        libbz2-dev \
        libreadline-dev \
        libsqlite3-dev \
        llvm \
        libncurses5-dev \
        xz-utils \
        tk-dev \
        libxml2-dev \
        libxmlsec1-dev \
        libffi-dev \
        liblzma-dev
    success "核心开发工具包安装完成。"

    # 1.5 配置Git
    info "正在配置Git全局设置以优化性能..."
    git config --global clone.defaultDepth 1
    git config --global core.fscache true
    git config --global core.compression 1
    success "Git全局配置完成。"

    # 1.6 系统参数优化
    info "正在优化系统内核参数..."
    
    # 检查是否已经设置了vm.overcommit_memory
    if ! grep -q "^vm.overcommit_memory = 1" /etc/sysctl.conf; then
        info "设置 vm.overcommit_memory = 1 以优化内存分配策略..."
        echo "vm.overcommit_memory = 1" | sudo_with_msg "添加系统优化参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "内存分配策略优化完成。"
    else
        info "vm.overcommit_memory 已设置为 1，跳过。"
    fi
    
    # 设置文件描述符限制
    if ! grep -q "^fs.file-max" /etc/sysctl.conf; then
        info "增加系统最大文件描述符数量..."
        echo "fs.file-max = 655350" | sudo_with_msg "添加文件描述符限制参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "文件描述符限制已优化。"
    else
        info "文件描述符限制已配置，跳过。"
    fi
    
    # 优化网络参数
    if ! grep -q "^net.ipv4.tcp_fin_timeout" /etc/sysctl.conf; then
        info "优化网络连接参数..."
        cat << EOF | sudo_with_msg "添加网络优化参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
# 优化网络连接
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
EOF
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "网络连接参数优化完成。"
    else
        info "网络连接参数已配置，跳过。"
    fi
    
    # 调整虚拟内存交换行为 (Swappiness)
    if ! grep -q "^vm.swappiness" /etc/sysctl.conf; then
        info "降低系统 Swap 使用频率 (vm.swappiness=10)..."
        echo -e "\n# 降低系统 Swap 使用频率，在内存充足时提升性能\nvm.swappiness = 10" | sudo_with_msg "添加 Swappiness 参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "Swap 使用频率已优化。"
    else
        info "Swappiness 参数已配置，跳过。"
    fi

    # 优化 VFS 缓存压力
    if ! grep -q "^vm.vfs_cache_pressure" /etc/sysctl.conf; then
        info "优化文件系统缓存压力 (vm.vfs_cache_pressure=50)..."
        echo -e "\n# 优化 VFS 缓存回收策略，加快文件操作\nvm.vfs_cache_pressure = 50" | sudo_with_msg "添加 VFS 缓存压力参数到 /etc/sysctl.conf..." tee -a /etc/sysctl.conf > /dev/null
        sudo_with_msg "应用系统参数更改..." sysctl -p
        success "VFS 缓存压力已优化。"
    else
        info "VFS 缓存压力参数已配置，跳过。"
    fi
    
    # 提高用户级资源限制
    info "正在提高用户级资源限制 (nofile, nproc)..."
    local limits_conf="/etc/security/limits.conf"
    if ! grep -q "^\* soft nofile 65535" "$limits_conf"; then
        echo -e "\n# 提高所有用户的最大文件打开数和进程数\n* soft nofile 65535\n* hard nofile 65535\n* soft nproc 65535\n* hard nproc 65535" | sudo_with_msg "更新 $limits_conf..." tee -a "$limits_conf" > /dev/null
        success "用户级资源限制已提高。"
    else
        info "用户级资源限制已配置，跳过。"
    fi

    success "系统参数优化完成。"
    info "--- 系统初始化与配置完成 ---"
}
