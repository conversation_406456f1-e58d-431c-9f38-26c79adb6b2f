#!/bin/bash

# 引入基础函数
source $(dirname "$0")/00_init_base.sh

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   log "此脚本必须以root用户运行"
   exit 1
fi

# 定义变量
NGINX_VERSION="1.24.0"
NGINX_INSTALL_DIR="/usr/local/nginx"
NGINX_SRC_DIR="/usr/local/src"
NGINX_USER="nginx"
NGINX_GROUP="nginx"

log "安装依赖..."
dnf install -y gcc gcc-c++ make pcre pcre-devel zlib zlib-devel openssl openssl-devel epel-release certbot python3-certbot-nginx

log "下载并解压nginx..."
cd ${NGINX_SRC_DIR} || exit 1
if [ ! -f nginx-${NGINX_VERSION}.tar.gz ]; then
    wget http://nginx.org/download/nginx-${NGINX_VERSION}.tar.gz
fi
rm -rf nginx-${NGINX_VERSION}
tar -zxf nginx-${NGINX_VERSION}.tar.gz
cd nginx-${NGINX_VERSION} || exit 1

log "检查并创建nginx用户和组(如果不存在)..."
if ! getent group ${NGINX_GROUP} >/dev/null; then
    groupadd -r ${NGINX_GROUP}
fi
if ! getent passwd ${NGINX_USER} >/dev/null; then
    useradd -r -g ${NGINX_GROUP} -s /sbin/nologin ${NGINX_USER}
fi


log "创建目录..."
mkdir -p ${NGINX_INSTALL_DIR}/{logs,conf.d,ssl}


log "编译安装nginx..."
if [ ! -f "${NGINX_INSTALL_DIR}/sbin/nginx" ]; then
    ./configure --prefix=${NGINX_INSTALL_DIR} \
        --user=${NGINX_USER} \
        --group=${NGINX_GROUP} \
        --with-http_ssl_module \
        --with-http_v2_module \
        --with-http_realip_module \
        --with-http_stub_status_module \
        --with-http_gzip_static_module \
        --with-pcre \
        --with-stream \
        --with-threads \
        --with-file-aio && \
    make && make install
fi


log "设置权限..."
chown -R ${NGINX_USER}:${NGINX_GROUP} ${NGINX_INSTALL_DIR}
chmod -R 755 ${NGINX_INSTALL_DIR}


log "创建nginx配置..."
cat > ${NGINX_INSTALL_DIR}/conf/nginx.conf << EOF
user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
pid ${NGINX_INSTALL_DIR}/logs/nginx.pid;

events {
    use epoll;
    worker_connections 65535;
    multi_accept on;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    include       /usr/local/nginx/conf.d/*.conf;

    log_format  main  '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                      '\$status \$body_bytes_sent "\$http_referer" '
                      '"\$http_user_agent" "\$http_x_forwarded_for" '
                      '\$request_time \$upstream_response_time';

    access_log  /usr/local/nginx/logs/access.log  main buffer=16k;
    error_log   /usr/local/nginx/logs/error.log  warn;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;
    keepalive_timeout  65;
    keepalive_requests 100;
    client_max_body_size 20m;

    gzip  on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_vary on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    ssl_session_cache    shared:SSL:10m;
    ssl_session_timeout  10m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers  on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
}
EOF


log "创建systemd服务..."
cat > /usr/lib/systemd/system/nginx.service << EOF
[Unit]
Description=nginx - high performance web server
Documentation=http://nginx.org/en/docs/
After=network-online.target remote-fs.target nss-lookup.target
Wants=network-online.target

[Service]
Type=forking
PIDFile=${NGINX_INSTALL_DIR}/logs/nginx.pid
ExecStartPre=${NGINX_INSTALL_DIR}/sbin/nginx -t -c ${NGINX_INSTALL_DIR}/conf/nginx.conf
ExecStart=${NGINX_INSTALL_DIR}/sbin/nginx -c ${NGINX_INSTALL_DIR}/conf/nginx.conf
ExecReload=/bin/kill -s HUP \$MAINPID
ExecStop=/bin/kill -s QUIT \$MAINPID
PrivateTmp=true
LimitNOFILE=65535
Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target
EOF

log "配置nginx服务..."
systemctl daemon-reload
ln -sf ${NGINX_INSTALL_DIR}/sbin/nginx /usr/sbin/nginx
systemctl enable nginx
systemctl start nginx

log "配置证书自动续期..."
cat > /usr/local/bin/renew-cert.sh << 'EOF'
#!/bin/bash
certbot renew --quiet --no-self-upgrade && systemctl reload nginx
EOF
chmod +x /usr/local/bin/renew-cert.sh


log "nginx安装和配置完成"
