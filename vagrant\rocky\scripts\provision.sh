#!/bin/bash

echo -e "\n\n\033[1;36m=========== 设置root密码 ===============\033[0m"
ROOT_PASSWORD="123456"
echo "$ROOT_PASSWORD" | passwd --stdin root

echo -e "\n\n\033[1;36m=========== 更新系统 ===============\033[0m"
dnf update -y

echo -e "\n\n\033[1;36m=========== 添加日常使用的账号 ===============\033[0m"
# 创建admin用户并设置密码
useradd -m -s /bin/bash admin
echo "admin:123456" | chpasswd

# 将admin添加到wheel组
usermod -aG wheel admin
# 允许wheel组用户使用sudo
sed -i '/^%wheel/s/^# //' /etc/sudoers
sed -i '/^%wheel/s/ALL$/NOPASSWD: ALL/' /etc/sudoers

# 取消vagrant在/etc/pam.d/su中添加的限制
# account         [success=1 default=ignore] \
#                                 pam_succeed_if.so user = vagrant use_uid quiet
# account         required        pam_succeed_if.so user notin root:vagrant
sudo sed -i '/account.*pam_succeed_if.so user notin root:vagrant/s/^/#/' /etc/pam.d/su

# 允许admin用户使用docker
usermod -aG docker admin

# 创建admin用户的工作目录并设置权限
mkdir -p /home/<USER>/workspace
chown -R admin:admin /home/<USER>/workspace
