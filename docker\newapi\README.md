# NewAPI Docker 配置说明

## 概述
此配置已将 Redis 从容器内迁移到宿主机独立 Redis 容器，并使用宿主机代理。

## 配置变更

### 1. Redis 配置
- **已移除**: 容器内 Redis 服务（已注释掉）
- **已更新**: 使用宿主机 Redis，默认连接 `localhost:6379`
- **环境变量**:
  - `REDIS_HOST=localhost`
  - `REDIS_PORT=6379`
  - `REDIS_PASSWORD=123456`

### 2. 网络配置
- **网络模式**: `network_mode: "host"`（宿主机网络模式）
- **优势**: 容器可以直接访问宿主机上的所有服务，包括 Redis 和代理

### 3. 代理配置
- **HTTP 代理**: `http://127.0.0.1:7890`
- **HTTPS 代理**: `http://127.0.0.1:7890`
- **绕过代理**: `localhost,127.0.0.1,0.0.0.0`

## 使用要求

### 前置条件
1. **宿主机必须运行 Redis 容器**
   ```bash
   # 启动 Redis 容器（如果尚未运行）
   docker run -d --name redis -p 6379:6379 redis:latest redis-server --requirepass "123456"
   ```

2. **宿主机必须运行代理服务**
   - 默认端口: 7890
   - 可根据需要修改 `HTTP_PROXY` 和 `HTTPS_PROXY` 环境变量

### 启动步骤
```bash
# 1. 进入项目目录
cd docker/newapi

# 2. 启动服务
docker-compose up -d

# 3. 查看日志
docker-compose logs -f new-api
```

### 验证连接
```bash
# 测试 Redis 连接
docker exec new-api redis-cli -h localhost ping

# 查看服务状态
curl http://localhost:3000/api/status
```

## 故障排除

### Redis 连接问题
如果连接 Redis 失败：
1. 确认宿主机 Redis 正在运行：`docker ps | grep redis`
2. 检查 Redis 端口是否开放：`netstat -tlnp | grep 6379`
3. 验证 Redis 配置：`docker exec redis redis-cli ping`

### 代理连接问题
如果代理连接失败：
1. 修改 `.env` 文件中的代理地址
2. 重启服务：`docker-compose restart`

## 自定义配置
- 修改 `.env` 文件中的 `REDIS_HOST` 和 `REDIS_PORT` 以使用不同的 Redis 实例
- 修改代理地址和端口以适应你的网络环境
