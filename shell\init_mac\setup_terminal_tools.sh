#!/usr/bin/env bash

# ==============================================================================
# macOS 现代化终端与新锐CLI工具配置脚本
# ==============================================================================

# --- 辅助函数定义 ---
info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

warn() {
    echo -e "\033[33m[WARN]\033[0m $1"
}

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查是否为Apple Silicon Mac
is_apple_silicon() {
    [[ "$(uname -m)" == "arm64" ]]
}

# 获取Homebrew路径
get_brew_path() {
    if is_apple_silicon; then
        echo "/opt/homebrew"
    else
        echo "/usr/local"
    fi
}

setup_terminal_tools() {
    # --- Helper function to create a clean .zshrc configuration ---
    # By defining it inside, we ensure it's a local helper
    # and that setup_terminal_tools is the only top-level function.
    _create_clean_zshrc() {
        info "--- 开始创建全新的 .zshrc 配置 ---"
        local zshrc_file="$HOME/.zshrc"

        # 备份现有的.zshrc文件（如果存在且不是我们创建的）
        if [ -f "$zshrc_file" ] && ! grep -q "# Cascade Clean ZSH Config" "$zshrc_file"; then
            local backup_file="$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
            info "备份现有的.zshrc文件到 $backup_file"
            cp "$zshrc_file" "$backup_file"
        fi

        # 定义期望的插件列表
        local plugins_list=(
            git
            zsh-syntax-highlighting
            zsh-autosuggestions
            zsh-completions
            sudo
            extract
            cp
            docker
            docker-compose
            kubectl
            npm
            yarn
            pip
            python
            golang
            rust
            brew
            macos
        )

        # 获取Homebrew路径
        local brew_path
        brew_path=$(get_brew_path)

        # 创建完全干净的.zshrc配置
        info "正在生成全新的.zshrc配置..."
        cat > "$zshrc_file" << EOF
# ==============================================================================
# Cascade Clean ZSH Config for macOS
# ==============================================================================

# --- 基础环境配置 ---
# 设置Oh My Zsh安装路径
export ZSH="\$HOME/.oh-my-zsh"

# Homebrew路径配置
export HOMEBREW_PREFIX="$brew_path"
export HOMEBREW_CELLAR="$brew_path/Cellar"
export HOMEBREW_REPOSITORY="$brew_path"
export PATH="$brew_path/bin:$brew_path/sbin:\$PATH"
export MANPATH="$brew_path/share/man\${MANPATH+:\$MANPATH}:"
export INFOPATH="$brew_path/share/info:\${INFOPATH:-}"

# 确保用户本地bin目录在PATH中
export PATH="\$HOME/.local/bin:\$PATH"

# --- Oh My Zsh配置 ---
# 禁用默认主题，使用Oh My Posh
ZSH_THEME=""

# 启用的插件列表
plugins=(
  git
  zsh-syntax-highlighting
  zsh-autosuggestions
  zsh-completions
  sudo
  extract
  cp
  docker
  docker-compose
  kubectl
  npm
  yarn
  pip
  python
  golang
  rust
  brew
  macos
)

# 加载Oh My Zsh
source \$ZSH/oh-my-zsh.sh

# --- 自动补全配置 ---
autoload -U compinit && compinit

# --- 现代化工具集成 ---
# Oh My Posh主题配置
if command -v oh-my-posh &> /dev/null; then
    eval "\$(oh-my-posh init zsh --config ~/.poshthemes/catppuccin.omp.json)"
fi

# zoxide智能cd集成
if command -v zoxide &> /dev/null; then
    eval "\$(zoxide init zsh)"
fi

# --- 现代化别名配置 ---
# 使用eza替代ls
if command -v eza &> /dev/null; then
    alias ls='eza --icons --group-directories-first'
    alias la='eza -a --icons --group-directories-first'
    alias ll='eza -al --icons --header --git --group-directories-first'
fi

# 使用bat替代cat
if command -v bat &> /dev/null; then
    alias cat='bat --paging=never --style=plain'
fi

# macOS特定别名
alias finder='open -a Finder'
alias chrome='open -a "Google Chrome"'
alias code='open -a "Visual Studio Code"'

# 其他实用别名
alias cp='cp -r'

# --- 用户自定义配置区域 ---
# 在此处添加您的个人配置
# 例如：export EDITOR=vim

EOF

        success "全新的.zshrc配置已生成完成。"
        info "配置特点："
        info "  - 完全干净，无多余注释"
        info "  - 包含现代化CLI工具集成"
        info "  - 模块化且易于维护"
        info "  - 支持用户自定义扩展"
        info "  - 适配macOS和Homebrew环境"
    }

    info "--- 开始配置macOS现代化终端环境 ---"

    # 1. 检查并安装Homebrew
    info "--- 检查并安装Homebrew ---"
    if ! command_exists brew; then
        info "正在安装Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

        # 配置Homebrew环境变量
        local brew_path
        brew_path=$(get_brew_path)

        # 临时添加到当前会话的PATH
        export PATH="$brew_path/bin:$brew_path/sbin:$PATH"

        if command_exists brew; then
            success "Homebrew安装完成。"
        else
            error "Homebrew安装失败，请检查网络连接或手动安装。"
            return 1
        fi
    else
        info "Homebrew已安装，正在更新..."
        brew update
        success "Homebrew更新完成。"
    fi

    # 2. 安装并配置Zsh为默认Shell
    local brew_zsh_path
    brew_zsh_path="$(get_brew_path)/bin/zsh"

    # 通过Homebrew安装最新版本的Zsh
    if ! brew list zsh &>/dev/null; then
        info "正在通过Homebrew安装最新版本的Zsh..."
        brew install zsh
    else
        info "Zsh已通过Homebrew安装。"
    fi

    # 将Homebrew的zsh添加到/etc/shells（如果尚未添加）
    if ! grep -q "$brew_zsh_path" /etc/shells; then
        info "将Homebrew的Zsh添加到/etc/shells..."
        echo "$brew_zsh_path" | sudo tee -a /etc/shells
    fi

    # 设置为默认Shell
    if [[ "$SHELL" != "$brew_zsh_path" ]]; then
        info "正在设置Homebrew的Zsh为默认Shell..."
        chsh -s "$brew_zsh_path"
        success "Zsh已设置为默认Shell。请重新启动终端以生效。"
    else
        info "Homebrew的Zsh已经是默认Shell，跳过。"
    fi

    # 3. 安装Oh My Zsh (保持现有.zshrc不被覆盖)
    if [ ! -d "$HOME/.oh-my-zsh" ]; then
        info "正在安装Oh My Zsh..."
        # 备份现有的.zshrc文件（如果存在）
        if [ -f "$HOME/.zshrc" ]; then
            info "备份现有的.zshrc文件..."
            cp "$HOME/.zshrc" "$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"
        fi

        # 使用--keep-zshrc参数避免覆盖.zshrc
        KEEP_ZSHRC=yes sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended --keep-zshrc
        success "Oh My Zsh安装完成。"
    else
        info "Oh My Zsh已安装，跳过。"
    fi

    # 4. 安装Oh My Zsh插件
    info "正在安装Oh My Zsh插件..."
    local omz_custom="${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}"
    if [ ! -d "${omz_custom}/plugins/zsh-syntax-highlighting" ]; then
        git clone https://github.com/zsh-users/zsh-syntax-highlighting.git "${omz_custom}/plugins/zsh-syntax-highlighting"
    fi
    if [ ! -d "${omz_custom}/plugins/zsh-autosuggestions" ]; then
        git clone https://github.com/zsh-users/zsh-autosuggestions "${omz_custom}/plugins/zsh-autosuggestions"
    fi
    if [ ! -d "${omz_custom}/plugins/zsh-completions" ]; then
        git clone https://github.com/zsh-users/zsh-completions "${omz_custom}/plugins/zsh-completions"
    fi
    success "Oh My Zsh插件安装检查完成。"

    # 5. 安装并配置Oh My Posh
    info "--- 开始配置Oh My Posh ---"
    local user_bin_dir="$HOME/.local/bin"
    mkdir -p "$user_bin_dir"

    if ! command_exists oh-my-posh; then
        info "正在安装Oh My Posh到 $user_bin_dir..."
        if curl -s https://ohmyposh.dev/install.sh | bash -s -- -d "$user_bin_dir"; then
            success "Oh My Posh安装完成。"
        else
            error "Oh My Posh安装失败。"
            return 1
        fi
    else
        info "Oh My Posh已安装，正在检查更新..."
        if oh-my-posh upgrade &>/dev/null; then
            success "Oh My Posh更新检查完成。"
        else
            warn "Oh My Posh更新失败，请稍后手动运行 'oh-my-posh upgrade'"
        fi
    fi

    local omp_themes_dir="$HOME/.poshthemes"
    mkdir -p "$omp_themes_dir"
    if [ ! -f "$omp_themes_dir/catppuccin.omp.json" ]; then
        info "正在下载catppuccin.omp.json主题..."
        curl -s -o "$omp_themes_dir/catppuccin.omp.json" https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/catppuccin.omp.json
        success "catppuccin.omp.json主题下载完成。"
    else
        info "catppuccin.omp.json主题已存在，跳过下载。"
    fi

    # 6. 安装Nerd Fonts字体
    info "--- 检查并安装 Nerd Fonts ---"

    # 检查字体是否已安装
    local font_installed=false
    if system_profiler SPFontsDataType | grep -qi "RobotoMono.*Nerd" &>/dev/null; then
        font_installed=true
        info "检测到RobotoMono Nerd Font已安装"
    fi

    if [ "$font_installed" = true ]; then
        success "RobotoMono Nerd Font已安装，跳过下载"
    else
        # 通过Homebrew Cask安装字体
        info "正在通过Homebrew Cask安装RobotoMono Nerd Font..."
        if brew install --cask font-roboto-mono-nerd-font; then
            success "RobotoMono Nerd Font安装完成"
        else
            warn "字体安装失败，请手动安装或检查网络连接"
        fi
    fi
    info "请在终端应用中设置字体为 'RobotoMono Nerd Font'"

    # 7. 安装新锐CLI工具集
    info "--- 开始安装新锐CLI工具集 ---"
    local tools=(eza bat ripgrep fd zoxide fzf btop jq)

    for tool in "${tools[@]}"; do
        if ! brew list "$tool" &>/dev/null; then
            info "正在安装 $tool..."
            brew install "$tool"
        else
            info "$tool 已安装，跳过。"
        fi
    done

    success "新锐CLI工具集安装完成。"

    # 8. 创建全新的.zshrc配置
    _create_clean_zshrc

    info "--- macOS现代化终端环境配置完成 ---"
    info "请重新启动终端或运行 'source ~/.zshrc' 以应用新配置。"
}

# 如果脚本被直接执行，则运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    setup_terminal_tools
fi
