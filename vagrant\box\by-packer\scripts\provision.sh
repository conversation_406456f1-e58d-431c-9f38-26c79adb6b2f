#!/bin/bash

ROOT_PASSWORD="123456"
echo "$ROOT_PASSWORD" | passwd --stdin root

echo -e "\n\n\033[1;36m=========== 配置国内 YUM 源 ===============\033[0m"
# 检查 Rocky Linux 版本
if [ -f /etc/os-release ]; then
    . /etc/os-release
    ROCKY_VERSION=$(echo $VERSION_ID | cut -d. -f1)
else
    echo "无法确定 Rocky Linux 版本"
    exit 1
fi

# 根据版本选择正确的仓库文件
if [ "$ROCKY_VERSION" = "8" ]; then
    REPO_FILES="/etc/yum.repos.d/Rocky-*.repo"
elif [ "$ROCKY_VERSION" = "9" ]; then
    REPO_FILES="/etc/yum.repos.d/rocky*.repo"
else
    echo "不支持的 Rocky Linux 版本: $VERSION_ID"
    exit 1
fi

# 修改仓库文件
for repo_file in $REPO_FILES; do
    if [ -f "$repo_file" ]; then
        sed -e 's|^mirrorlist=|#mirrorlist=|g' \
            -e 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' \
            -i.bak \
            "$repo_file"
    else
        echo "警告: 仓库文件 $repo_file 不存在"
    fi
done

dnf clean all
dnf makecache

echo -e "\n\n\033[1;36m=========== 更新系统并安装常用软件 ===============\033[0m"
dnf install epel-release -y
dnf update --refresh -y
# 安装常用软件
dnf install -y dkms kernel-devel kernel-headers gcc make perl elfutils-libelf-devel vim git curl wget openssh-server zip unzip bzip2 net-tools bind-utils tree lsof

echo -e "\n\n\033[1;36m=========== 设置时区和时间同步 ===============\033[0m"
timedatectl set-timezone Asia/Shanghai
dnf install -y chrony
systemctl enable chronyd
systemctl start chronyd

echo -e "\n\n\033[1;36m=========== 优化 SSH 连接速度 ===============\033[0m"
sed -i 's/#UseDNS yes/UseDNS no/g' /etc/ssh/sshd_config
sed -i 's/GSSAPIAuthentication yes/GSSAPIAuthentication no/g' /etc/ssh/sshd_config

echo -e "\n\n\033[1;36m=========== 允许 root 远程 SSH 访问 ===============\033[0m"
sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/g' /etc/ssh/sshd_config

systemctl restart sshd

echo -e "\n\n\033[1;36m=========== 配置系统限制 ===============\033[0m"
cat >> /etc/security/limits.conf << EOF
*               soft    nofile          65535
*               hard    nofile          65535
EOF

echo -e "\n\n\033[1;36m=========== 优化内核参数 ===============\033[0m"
cat >> /etc/sysctl.conf << EOF
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
net.ipv4.ip_local_port_range = 1024 65000
net.ipv4.tcp_max_tw_buckets = 5000
EOF
sysctl -p

echo -e "\n\n\033[1;36m=========== 禁用 SELinux ===============\033[0m"
setenforce 0
sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config

echo -e "\n\n\033[1;36m=========== 关闭防火墙 ===============\033[0m"
systemctl stop firewalld
systemctl disable firewalld

echo -e "\n\n\033[1;36m=========== 安装 Docker ===============\033[0m"
dnf config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
dnf install -y docker-ce docker-ce-cli containerd.io

echo -e "\n\n\033[1;36m=========== 配置 Docker 镜像加速 ===============\033[0m"
mkdir -p /etc/docker
tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": ["https://docker.mirrors.ustc.edu.cn"]
}
EOF

systemctl enable docker
systemctl start docker

echo -e "\n\n\033[1;32m=========== Provisioning Complete. ===============\033[0m"
