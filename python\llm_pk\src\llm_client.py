import asyncio
import json
from typing import Any, Dict, List, Optional

import httpx
from src.config import (
    API_BASE_URL,
    API_KEY,
    REQUEST_TIMEOUT,
    ENABLE_THINKING_PROCESS,
    CONTESTANT_MODEL_TEMPERATURE,
)
from src.prompts import create_judge_prompt


async def get_model_response(client: httpx.AsyncClient, model_name: str, question: str) -> Optional[str]:
    """
    从指定模型异步获取回答，支持流式响应以捕获思考过程。
    """
    print(f"正在请求参赛模型: {model_name}...")
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "model": model_name,
        "messages": [{"role": "user", "content": question}],
        "temperature": CONTESTANT_MODEL_TEMPERATURE,
        "stream": ENABLE_THINKING_PROCESS,  # 根据配置决定是否启用流式响应
    }

    try:
        if not ENABLE_THINKING_PROCESS:
            # 非流式，一次性获取结果
            response = await client.post(
                f"{API_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT,
            )
            response.raise_for_status()
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"成功从 {model_name} 收到回答。")
            return content.strip()
        else:
            # 流式，捕获思考过程和最终回答
            final_answer = ""
            thinking_process = []
            async with client.stream(
                "POST",
                f"{API_BASE_URL}/chat/completions",
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT,
            ) as response:
                response.raise_for_status()
                async for chunk in response.aiter_bytes():
                    # OpenAI的流式响应以 "data: " 开头
                    if chunk.startswith(b'data: '):
                        line = chunk[len(b'data: '):].strip()
                        if line == b'[DONE]':
                            break
                        if not line:
                            continue
                        try:
                            delta_data = json.loads(line)
                            delta_content = delta_data["choices"][0]["delta"]
                            if "content" in delta_content and delta_content["content"]:
                                final_answer += delta_content["content"]
                            if "tool_calls" in delta_content and delta_content["tool_calls"]:
                                thinking_process.append(json.dumps(delta_content["tool_calls"], ensure_ascii=False, indent=2))
                        except (json.JSONDecodeError, KeyError):
                            # 忽略无法解析的行
                            continue

            print(f"成功从 {model_name} 收到流式回答。")

            if thinking_process:
                thinking_process_text = "\n".join(thinking_process)
                return f"【思考过程】\n{thinking_process_text}\n--------------------\n【最终回答】\n{final_answer.strip()}"
            else:
                return final_answer.strip()

    except httpx.HTTPStatusError as e:
        print(f"请求模型 {model_name} 出错: HTTP {e.response.status_code} - {e.response.text}")
    except httpx.RequestError as e:
        print(f"请求模型 {model_name} 出错: {e}")
    except (KeyError, IndexError) as e:
        print(f"解析模型 {model_name} 的响应出错: {e} - 响应内容: {response.text if 'response' in locals() else 'N/A'}")
    return None

async def get_judge_rating(client: httpx.AsyncClient, judge_model: str, question: str, answer_list: List[Dict], dimension_list: List[str]) -> Optional[List[Dict[str, Any]]]:
    """
    从指定的裁判模型异步获取结构化的评分。
    """
    print(f"正在请求裁判模型: {judge_model}...")
    judge_prompt = create_judge_prompt(question, answer_list, dimension_list)
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "model": judge_model,
        "messages": [{"role": "user", "content": judge_prompt}],
        "temperature": 0.1,  # 低温以确保输出格式稳定
        "response_format": {"type": "json_object"},
    }

    try:
        response = await client.post(
            f"{API_BASE_URL}/chat/completions",
            headers=headers,
            json=payload,
            timeout=REQUEST_TIMEOUT,
        )
        response.raise_for_status()
        # API返回的是一个JSON字符串，需要解析
        response_data = response.json()
        json_string = response_data["choices"][0]["message"]["content"]

        # 实际的评测数据在这个JSON字符串内部
        rating_data = json.loads(json_string)

        print(f"成功从 {judge_model} 收到评分。")
        # 提示词要求返回一个数组，所以我们期望得到一个列表
        if isinstance(rating_data, list):
            return rating_data
        # 有些模型可能会把列表包装在一个根键里
        elif isinstance(rating_data, dict) and len(rating_data) == 1:
            key = list(rating_data.keys())[0]
            if isinstance(rating_data[key], list):
                return rating_data[key]

        print(f"从 {judge_model} 收到非预期的JSON结构: {rating_data}")
        return None

    except httpx.HTTPStatusError as e:
        print(f"请求裁判 {judge_model} 出错: HTTP {e.response.status_code} - {e.response.text}")
    except httpx.RequestError as e:
        print(f"请求裁判 {judge_model} 出错: {e}")
    except (json.JSONDecodeError, KeyError, IndexError) as e:
        print(f"解析裁判 {judge_model} 的评分出错: {e} - 响应内容: {response.text}")
    return None

if __name__ == '__main__':
    # 示例用法
    async def main():
        async with httpx.AsyncClient() as client:
            question = "使用Python的主要好处是什么？"
            # 测试获取模型回答
            answer = await get_model_response(client, "gpt-4-turbo", question)
            if answer:
                print("\n--- 回答测试 ---")
                print(answer)

            # 测试获取裁判评分
            answers_to_rate = [
                {"model_name": "快速模型", "answer": "它很快。"},
                {"model_name": "简单模型", "answer": "它容易学习，并且有很多库。"},
            ]
            rating_dimensions = ["清晰度", "帮助性"]
            ratings = await get_judge_rating(client, "gpt-4o", question, answers_to_rate, rating_dimensions)
            if ratings:
                print("\n--- 评分测试 ---")
                print(json.dumps(ratings, indent=2, ensure_ascii=False))

    asyncio.run(main())
