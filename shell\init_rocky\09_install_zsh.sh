#!/bin/bash

source $(dirname "$0")/00_init_base.sh

log "安装和配置zsh"
dnf install -y zsh
sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended

# 获取当前登录的非root用户
CURRENT_USER=$(logname)

if [ "$CURRENT_USER" != "root" ] && [ -n "$CURRENT_USER" ]; then
    USER_HOME="/home/<USER>"
else
    USER_HOME="/root"
fi

if command -v chsh >/dev/null 2>&1; then
    chsh -s $(which zsh) $CURRENT_USER
elif command -v usermod >/dev/null 2>&1; then
    sudo usermod -s $(which zsh) $CURRENT_USER
else
    echo "chsh 和 usermod 命令都不存在。请手动编辑 /etc/passwd 文件来更改默认 shell。"
    echo "$CURRENT_USER:x:1000:1000:,,,:/home/<USER>/bin/bash"
fi

# 确保.zshrc文件存在
if [ ! -f "$USER_HOME/.zshrc" ]; then
    touch "$USER_HOME/.zshrc"
fi

# 配置zsh主题
sed -i 's/^ZSH_THEME=.*/ZSH_THEME="robbyrussell"/' $USER_HOME/.zshrc

# 在.zshrc中添加NVM和jenv的配置
cat << 'EOF' >> "$USER_HOME/.zshrc"
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
export PATH="$HOME/.jenv/bin:$PATH"

eval "$(jenv init -)"
EOF

log "zsh 安装和配置完成"
